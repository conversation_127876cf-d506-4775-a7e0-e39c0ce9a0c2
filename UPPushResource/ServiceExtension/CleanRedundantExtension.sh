#!/bin/sh
echo 'start clean redundant extension'
EXTENSION_BUNDLE_PATH=UPPushResource.bundle/ServiceExtension/Extensions
EXTENSION_BUILD_PATH=${BUILD_DIR}/${CONFIGURATION}${EFFECTIVE_PLATFORM_NAME}
EXTENSION_BUILD_REDUNDANT_DELETE='false';
echo ${EXTENSION_BUNDLE_PATH}
echo ${EXTENSION_BUILD_PATH}
for doc in `find ${EXTENSION_BUILD_PATH} -name "*.app"`; do
    echo "find app: ${doc}"
    EXTENSION_BUILD_REDUNDANT_PATH=${doc}/${EXTENSION_BUNDLE_PATH}
    echo ${EXTENSION_BUILD_REDUNDANT_PATH}
    if [ -d "${EXTENSION_BUILD_REDUNDANT_PATH}" ]; then
        rm -rf ${EXTENSION_BUILD_REDUNDANT_PATH}
        EXTENSION_BUILD_REDUNDANT_DELETE='true'
        echo 'remove build redundant extension complete'
    fi
done

EXTENSION_SOURCE_PATH=${EXTENSION_BUILD_PATH}/UPPush/${EXTENSION_BUNDLE_PATH}
echo ${EXTENSION_SOURCE_PATH}
if [ ${EXTENSION_BUILD_REDUNDANT_DELETE} == 'false' ] && [ -d "${EXTENSION_SOURCE_PATH}" ]; then
    rm -rf ${EXTENSION_SOURCE_PATH}
    echo 'remove source redundant extension complete'
fi
echo 'clean redundant extension complete'
