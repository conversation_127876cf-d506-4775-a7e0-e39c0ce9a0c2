#!/bin/sh
echo 'start migrate service extension'
echo "current build configuration:${CONFIGURATION}"
echo "current build action:${ACTION}"

EXTENSION_SOURCE_PATH=${PODS_TARGET_SRCROOT}/UPPushResource/ServiceExtension
EXTENSION_SOURCE_EXTENSIONS_PATH=${EXTENSION_SOURCE_PATH}/Extensions/${CONFIGURATION}
EXTENSION_CONFIG_PATH=${EXTENSION_SOURCE_PATH}/UPPushExtensionConfig.csv
echo ${EXTENSION_SOURCE_PATH}
echo ${EXTENSION_SOURCE_EXTENSIONS_PATH}
echo ${EXTENSION_CONFIG_PATH}
if [ ${ACTION} != 'install' ]; then
    exit 0
fi
for line in `cat "${EXTENSION_CONFIG_PATH}"`
do
    echo "extension original config:${line}"
    config_array=(${line//,/ })
    echo "extension config:${config_array}"
    echo "extension config item count:${#config_array[*]}"
    if [ ${#config_array[*]} != 5 ]; then 
        echo 'extension config error'
    else
        project=${config_array[0]}
        product=${config_array[1]}
        bundle_id=${config_array[2]}
        extension=${config_array[3]}
        active=${config_array[4]}
        echo "project:${project}, product:${product}, bundleId:${bundle_id}, extension:${extension}, active:${active}"
        CURRENT_APP_PROJECT=$(dirname ${PROJECT_DIR})/${project}.xcodeproj
        CURRENT_APP_PROJECT_BUILD=${BUILD_DIR}/${CONFIGURATION}${EFFECTIVE_PLATFORM_NAME}/${product}.app
        echo ${CURRENT_APP_PROJECT}
        echo ${CURRENT_APP_PROJECT_BUILD}
        if [ ${active} != "true" ]; then
            continue
        fi
        
        # 截取主工程的Bundle ID
        MAIN_PROJECT_ROOT="${PODS_ROOT}/.."
        MAIN_PROJECT_FILE_PATH="${MAIN_PROJECT_ROOT}/${project}.xcodeproj/project.pbxproj"
        if [ ! -e ${MAIN_PROJECT_FILE_PATH} ]; then
            echo "There is no ${project}.xcodeproj file, does not need to copy App Extension"
            continue
        fi
        MAIN_PROJECT_BUNDLE_ID=$(grep -o 'PRODUCT_BUNDLE_IDENTIFIER = [^;]*;' ${MAIN_PROJECT_FILE_PATH} | head -n 1)
        MAIN_PROJECT_BUNDLE_ID="${MAIN_PROJECT_BUNDLE_ID#PRODUCT_BUNDLE_IDENTIFIER = }"
        MAIN_PROJECT_BUNDLE_ID=${MAIN_PROJECT_BUNDLE_ID%;}
        echo "MAIN_PROJECT_BUNDLE_ID is:${MAIN_PROJECT_BUNDLE_ID}"
        
        if [ ${MAIN_PROJECT_BUNDLE_ID} != ${bundle_id} ] || [ ${ACTION} != 'install' ]; then
            continue
        fi

        if [ ! -d "${CURRENT_APP_PROJECT_BUILD}" ] && [ ${ACTION} == 'install' ]; then
            echo 'invalid build path'
            STOCHASTIC_BUILD_PATHS="$(dirname ${BUILD_DIR})/InstallationBuildProductsLocation"
            if [ ! -d "${STOCHASTIC_BUILD_PATHS}" ]; then
                mkdir ${STOCHASTIC_BUILD_PATHS}
            fi
            if [ ! -d "${STOCHASTIC_BUILD_PATHS}/Applications" ]; then
                mkdir "${STOCHASTIC_BUILD_PATHS}/Applications/"
            fi
            if [ ! -d "${STOCHASTIC_BUILD_PATHS}/Applications/${product}.app" ]; then
                mkdir "${STOCHASTIC_BUILD_PATHS}/Applications/${product}.app"
            fi
            CURRENT_APP_PROJECT_BUILD="${STOCHASTIC_BUILD_PATHS}/Applications/${product}.app"
            echo "stochastic build path:${STOCHASTIC_BUILD_PATHS}"
        fi
        echo ${CURRENT_APP_PROJECT_BUILD}
        if [ -d "${CURRENT_APP_PROJECT}" ] && [ -d "${CURRENT_APP_PROJECT_BUILD}" ]; then
            EXTENSION_NAME=${extension}.appex
            EXTENSION_SOURCE_EXTENSION=${EXTENSION_SOURCE_EXTENSIONS_PATH}/${EXTENSION_NAME}
            EXTENSION_BUILD_PATH=${CURRENT_APP_PROJECT_BUILD}/PlugIns
            EXTENSION_BUILD_APP_PATH=${EXTENSION_BUILD_PATH}/${EXTENSION_NAME}
            echo ${EXTENSION_NAME}
            echo ${EXTENSION_SOURCE_EXTENSION}
            echo ${EXTENSION_BUILD_PATH}
            echo ${EXTENSION_BUILD_APP_PATH}
            if [ ! -d "${EXTENSION_BUILD_PATH}" ]; then
                mkdir ${EXTENSION_BUILD_PATH}
                echo 'create PlugIns complete'
            fi

            if [ -d "${EXTENSION_BUILD_APP_PATH}" ]; then
                rm -rf ${EXTENSION_BUILD_APP_PATH}
                echo 'remove extension complete'
            fi
            
            if [ -d "${EXTENSION_SOURCE_EXTENSION}" ]; then
                cp -rf ${EXTENSION_SOURCE_EXTENSION} ${EXTENSION_BUILD_PATH}
                echo 'copy extension complete'
            else
                echo "not found source extension:${EXTENSION_SOURCE_EXTENSION}"
            fi
        fi
    fi
done
echo 'migrate service extension complete'
