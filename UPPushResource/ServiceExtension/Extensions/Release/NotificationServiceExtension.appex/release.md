# UPLog版本发布记录

## 当前版本 1.1.27
### 1.1.27
1.增加清理本地日志文件函数、删除闪退捕获机制、优化文件列表获取和排序函数
### 1.1.26
1.增加闪退捕获机制

### 1.1.25
1.通过缓存当前日志文件列表、写入日志文件路径等方式优化性能

### 1.1.24
1.合入日志2.0采集功能

### 2.0.3
1.日志输出前新增脱敏处理

### 1.1.23
1.屏蔽release控制台日志输出

### 1.1.21
1.优化日志写入功能

### 1.1.20
1.添加debug注释

### 1.1.19
1.添加debugger,日志上传只有在打开调试模式下才能上传

### 1.1.18
1.修改tag

### 1.1.17
1.修改工程为非framework

### 1.1.16
1.修改UPLog静态库依赖的iOS版本为9.0

### 1.1.15
1.添加日志写入和压碎功能
### 1.1.13
1.基于最新构建系统出tag
### 1.1.12
1.删除源码podspec文件，修改源码变量为库名
### 1.1.11
1.添加源码podSepc
### 1.1.10
1.修改工程为framework
### 1.1.9
1.根据外部传入的Logger修改日志输出
### 1.1.8
1.修改UPLogger的内部实现
### 1.1.7
1.去除日志增加的返回值
### 1.1.6
1.日志增加返回值
### 1.1.5
1.删除DDLog依赖
### 1.1.4
1.设置日志等级开启日志打印
### 1.1.3
1.修改日志等级
### 1.1.2
1.修改日志打印宏

### 1.1.0
1.调整目录结构
2.支持logger可注入

### 1.0.0

1. 日志打印库基本功能实现。
