// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXAggregateTarget section */
		40FDF44C2554170500E1B6E5 /* UPPush_Doc */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 40FDF44D2554170500E1B6E5 /* Build configuration list for PBXAggregateTarget "UPPush_Doc" */;
			buildPhases = (
				40FDF4562554170A00E1B6E5 /* ShellScript */,
			);
			dependencies = (
			);
			name = UPPush_Doc;
			productName = UPPush_Doc;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		2E90A67A9AFED74A5F37D20E /* libPods-UPPush_abstract_pod-Debugger.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DF8113219FCB10B55F40E2F5 /* libPods-UPPush_abstract_pod-Debugger.a */; };
		400E829B2592E61D0062CDC9 /* UPPushBuildInProcessObserveManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 400E829A2592E61D0062CDC9 /* UPPushBuildInProcessObserveManager.m */; };
		400E82D72593176E0062CDC9 /* UPPushBuildInProcessQueueManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 400E82D62593176E0062CDC9 /* UPPushBuildInProcessQueueManager.m */; };
		401A998D2554276700DA11FA /* libUPPushUI.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 40FDF3E42554095C00E1B6E5 /* libUPPushUI.a */; };
		4041A00A255B7C6300C0D9B0 /* UPPushMessage+Tool.m in Sources */ = {isa = PBXBuildFile; fileRef = 4041A009255B7C6300C0D9B0 /* UPPushMessage+Tool.m */; };
		4041A090255BD3E700C0D9B0 /* UPPushAlertController.m in Sources */ = {isa = PBXBuildFile; fileRef = 4041A08F255BD3E700C0D9B0 /* UPPushAlertController.m */; };
		4041A150255BE1A200C0D9B0 /* UPPushAlertModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 4041A14F255BE1A200C0D9B0 /* UPPushAlertModel.m */; };
		4041A16C255BE27B00C0D9B0 /* UPPushAlertActionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 4041A16B255BE27B00C0D9B0 /* UPPushAlertActionModel.m */; };
		4041A176255BF3D300C0D9B0 /* UPPushAlertManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 4041A175255BF3D300C0D9B0 /* UPPushAlertManager.m */; };
		4041A180255CDA0800C0D9B0 /* UPPushBroadcastController.m in Sources */ = {isa = PBXBuildFile; fileRef = 4041A17F255CDA0800C0D9B0 /* UPPushBroadcastController.m */; };
		4041A18A255CE17700C0D9B0 /* UPPushNotification.m in Sources */ = {isa = PBXBuildFile; fileRef = 4041A189255CE17700C0D9B0 /* UPPushNotification.m */; };
		4041A1A3255E215C00C0D9B0 /* UPPushAPIUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5012554181B00E1B6E5 /* UPPushAPIUtil.m */; };
		4041A1B0255E216200C0D9B0 /* UPPushEncryption.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5002554181B00E1B6E5 /* UPPushEncryption.m */; };
		4041A1BD255E216900C0D9B0 /* UPPushRegisterAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4EE2554181B00E1B6E5 /* UPPushRegisterAPI.m */; };
		4041A1C4255E216C00C0D9B0 /* UPPushReportStatusAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4EA2554181B00E1B6E5 /* UPPushReportStatusAPI.m */; };
		4041A1D1255E217000C0D9B0 /* UPPushUnregisterAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4EF2554181B00E1B6E5 /* UPPushUnregisterAPI.m */; };
		4041A1DE255E217800C0D9B0 /* UPPushSERegisterAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4F72554181B00E1B6E5 /* UPPushSERegisterAPI.m */; };
		4041A1EB255E217C00C0D9B0 /* UPPushSEReportStatusAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4F52554181B00E1B6E5 /* UPPushSEReportStatusAPI.m */; };
		4041A1F2255E217F00C0D9B0 /* UPPushSEUnregisterAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4F62554181B00E1B6E5 /* UPPushSEUnregisterAPI.m */; };
		4041A1FF255E218700C0D9B0 /* UPPushSERequestBase.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4FD2554181B00E1B6E5 /* UPPushSERequestBase.m */; };
		4041A22A255E22E300C0D9B0 /* UPPushRequestBase.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4F12554181B00E1B6E5 /* UPPushRequestBase.m */; };
		4041A23425621D1000C0D9B0 /* MessageSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = 4041A23325621D1000C0D9B0 /* MessageSteps.m */; };
		4041A25C25622B2600C0D9B0 /* FakeUPPushAllInterceptor.m in Sources */ = {isa = PBXBuildFile; fileRef = 4041A25B25622B2600C0D9B0 /* FakeUPPushAllInterceptor.m */; };
		4041A3E22562994300C0D9B0 /* UPPushBadgeManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4E52554181B00E1B6E5 /* UPPushBadgeManager.m */; };
		4041A3EB25636B8E00C0D9B0 /* TestInterceptorCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 4041A3EA25636B8E00C0D9B0 /* TestInterceptorCache.m */; };
		404EF225255A70B40030B1F7 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 404EF224255A70B40030B1F7 /* GoogleService-Info.plist */; };
		404EF242255A96420030B1F7 /* UPPushMessage+Completion.m in Sources */ = {isa = PBXBuildFile; fileRef = 404EF241255A96420030B1F7 /* UPPushMessage+Completion.m */; };
		406C012726929B2C00572FCB /* UPPushToastManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 406C012626929B2C00572FCB /* UPPushToastManager.m */; };
		406C012C26929D0100572FCB /* UPPushToastController.m in Sources */ = {isa = PBXBuildFile; fileRef = 406C012B26929D0100572FCB /* UPPushToastController.m */; };
		406C012F26929D1600572FCB /* UPPushToastModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 406C012E26929D1600572FCB /* UPPushToastModel.m */; };
		406C01322692A48C00572FCB /* UPPushToastView.m in Sources */ = {isa = PBXBuildFile; fileRef = 406C01312692A48C00572FCB /* UPPushToastView.m */; };
		406C015C269BEACD00572FCB /* UPPushToastArrowLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = 406C015B269BEACD00572FCB /* UPPushToastArrowLabel.m */; };
		406C015D269C1ACF00572FCB /* UPPushResource.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 406C0151269BE39700572FCB /* UPPushResource.bundle */; };
		40A19E81256608F3006767C1 /* UPPushStringTools.m in Sources */ = {isa = PBXBuildFile; fileRef = 40A19E80256608F3006767C1 /* UPPushStringTools.m */; };
		40FDF3F82554096B00E1B6E5 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF3F72554096B00E1B6E5 /* AppDelegate.m */; };
		40FDF3FE2554096B00E1B6E5 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF3FD2554096B00E1B6E5 /* ViewController.m */; };
		40FDF4012554096B00E1B6E5 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 40FDF3FF2554096B00E1B6E5 /* Main.storyboard */; };
		40FDF4032554096C00E1B6E5 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 40FDF4022554096C00E1B6E5 /* Assets.xcassets */; };
		40FDF4062554096C00E1B6E5 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 40FDF4042554096C00E1B6E5 /* LaunchScreen.storyboard */; };
		40FDF4092554096C00E1B6E5 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4082554096C00E1B6E5 /* main.m */; };
		40FDF4742554179100E1B6E5 /* UPPushManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4592554179100E1B6E5 /* UPPushManager.m */; };
		40FDF4752554179100E1B6E5 /* UPPushBroadcastSender.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF45C2554179100E1B6E5 /* UPPushBroadcastSender.m */; };
		40FDF4762554179100E1B6E5 /* UPPushHomeDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4632554179100E1B6E5 /* UPPushHomeDataSource.m */; };
		40FDF4772554179100E1B6E5 /* UPPushResult.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4652554179100E1B6E5 /* UPPushResult.m */; };
		40FDF4782554179100E1B6E5 /* UPPushSeasiaDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4692554179100E1B6E5 /* UPPushSeasiaDataSource.m */; };
		40FDF4792554179100E1B6E5 /* UPPushResponseParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF46C2554179100E1B6E5 /* UPPushResponseParser.m */; };
		40FDF47A2554179100E1B6E5 /* UPPushDataSourceRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4702554179100E1B6E5 /* UPPushDataSourceRequest.m */; };
		40FDF47B2554179100E1B6E5 /* UPPushInitializer.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4722554179100E1B6E5 /* UPPushInitializer.m */; };
		40FDF5042554181B00E1B6E5 /* UPPushRetryAssistant.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4B92554181B00E1B6E5 /* UPPushRetryAssistant.m */; };
		40FDF5052554181B00E1B6E5 /* UPPushDispatcher.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4BD2554181B00E1B6E5 /* UPPushDispatcher.m */; };
		40FDF5062554181B00E1B6E5 /* UPPushMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4C12554181B00E1B6E5 /* UPPushMessage.m */; };
		40FDF5072554181B00E1B6E5 /* UPPushOriginalMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4C22554181B00E1B6E5 /* UPPushOriginalMessage.m */; };
		40FDF5082554181B00E1B6E5 /* UPPushMessageUIButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4C52554181B00E1B6E5 /* UPPushMessageUIButton.m */; };
		40FDF5092554181B00E1B6E5 /* UPPushMessageUI.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4C62554181B00E1B6E5 /* UPPushMessageUI.m */; };
		40FDF50A2554181B00E1B6E5 /* UPPushMessageBody.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4C92554181B00E1B6E5 /* UPPushMessageBody.m */; };
		40FDF50C2554181B00E1B6E5 /* UPPushMessagePage.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4D02554181B00E1B6E5 /* UPPushMessagePage.m */; };
		40FDF50D2554181B00E1B6E5 /* UPPushMessageDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4D12554181B00E1B6E5 /* UPPushMessageDevice.m */; };
		40FDF50E2554181B00E1B6E5 /* UPPushMessageExtraData.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4D22554181B00E1B6E5 /* UPPushMessageExtraData.m */; };
		40FDF50F2554181B00E1B6E5 /* UPPushMessageDeviceControl.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4D32554181B00E1B6E5 /* UPPushMessageDeviceControl.m */; };
		40FDF5102554181B00E1B6E5 /* UPPushMessageAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4D62554181B00E1B6E5 /* UPPushMessageAPI.m */; };
		40FDF5112554181B00E1B6E5 /* UPPushParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4DC2554181B00E1B6E5 /* UPPushParser.m */; };
		40FDF5122554181B00E1B6E5 /* UPPush.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4E02554181B00E1B6E5 /* UPPush.m */; };
		40FDF5132554181B00E1B6E5 /* UPPushInterceptorManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4E32554181B00E1B6E5 /* UPPushInterceptorManager.m */; };
		40FDF561255418EC00E1B6E5 /* UPPushGIOTrack.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF52E255418EC00E1B6E5 /* UPPushGIOTrack.m */; };
		40FDF563255418EC00E1B6E5 /* UPPushDevControlDataHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF533255418EC00E1B6E5 /* UPPushDevControlDataHandler.m */; };
		40FDF564255418EC00E1B6E5 /* UPPushCustomizedMessageProcessor.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF534255418EC00E1B6E5 /* UPPushCustomizedMessageProcessor.m */; };
		40FDF565255418EC00E1B6E5 /* UPPushNotificationMessageProcessor.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF538255418EC00E1B6E5 /* UPPushNotificationMessageProcessor.m */; };
		40FDF566255418EC00E1B6E5 /* UPPushHandlerTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF53B255418EC00E1B6E5 /* UPPushHandlerTool.m */; };
		40FDF567255418EC00E1B6E5 /* UPPushApiDataHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF53D255418EC00E1B6E5 /* UPPushApiDataHandler.m */; };
		40FDF569255418EC00E1B6E5 /* UPPushNoViewMsgComponent.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF541255418EC00E1B6E5 /* UPPushNoViewMsgComponent.m */; };
		40FDF56B255418EC00E1B6E5 /* UPPushPageDataHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF543255418EC00E1B6E5 /* UPPushPageDataHandler.m */; };
		40FDF56C255418EC00E1B6E5 /* UPPushLogoutObserver.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF544255418EC00E1B6E5 /* UPPushLogoutObserver.m */; };
		40FDF56D255418EC00E1B6E5 /* UPPushViewMsgComponent.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF547255418EC00E1B6E5 /* UPPushViewMsgComponent.m */; };
		40FDF56F255418EC00E1B6E5 /* UPPushBuildInUIInterceptor.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF54C255418EC00E1B6E5 /* UPPushBuildInUIInterceptor.m */; };
		40FDF570255418EC00E1B6E5 /* UPPushAppManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF550255418EC00E1B6E5 /* UPPushAppManager.m */; };
		40FDF571255418EC00E1B6E5 /* UPPushProgressShow.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF554255418EC00E1B6E5 /* UPPushProgressShow.m */; };
		40FDF572255418EC00E1B6E5 /* UPPushToast.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF555255418EC00E1B6E5 /* UPPushToast.m */; };
		40FDF573255418EC00E1B6E5 /* UIImage+UPPushImageEffects.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF55B255418EC00E1B6E5 /* UIImage+UPPushImageEffects.m */; };
		40FDF574255418EC00E1B6E5 /* UIImage+UPPushEmpty.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF55C255418EC00E1B6E5 /* UIImage+UPPushEmpty.m */; };
		40FDF575255418EC00E1B6E5 /* UPPushLoading.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF560255418EC00E1B6E5 /* UPPushLoading.m */; };
		40FDF5852554193D00E1B6E5 /* UPUserLoginApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5822554193D00E1B6E5 /* UPUserLoginApi.m */; };
		40FDF5862554193D00E1B6E5 /* SEUserLoginApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5832554193D00E1B6E5 /* SEUserLoginApi.m */; };
		40FDF5922554199300E1B6E5 /* UPJPushChannel.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5912554199300E1B6E5 /* UPJPushChannel.m */; };
		40FDF59B2554199D00E1B6E5 /* UPFirebaseChannel.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5992554199D00E1B6E5 /* UPFirebaseChannel.m */; };
		40FDF5CB255419F100E1B6E5 /* InitializationSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5B1255419F100E1B6E5 /* InitializationSteps.m */; };
		40FDF5CC255419F100E1B6E5 /* PushSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5B2255419F100E1B6E5 /* PushSteps.m */; };
		40FDF5CD255419F100E1B6E5 /* FakeUPPushInitializer.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5B6255419F100E1B6E5 /* FakeUPPushInitializer.m */; };
		40FDF5CE255419F100E1B6E5 /* FakeUPPushResult.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5B9255419F100E1B6E5 /* FakeUPPushResult.m */; };
		40FDF5CF255419F100E1B6E5 /* FakeUPPushBase.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5BA255419F100E1B6E5 /* FakeUPPushBase.m */; };
		40FDF5D0255419F100E1B6E5 /* FakeUPPushInterceptor.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5BB255419F100E1B6E5 /* FakeUPPushInterceptor.m */; };
		40FDF5D1255419F100E1B6E5 /* FakeUPPushBroadcastSender.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5BC255419F100E1B6E5 /* FakeUPPushBroadcastSender.m */; };
		40FDF5D2255419F100E1B6E5 /* FakeUPPushChannel.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5BD255419F100E1B6E5 /* FakeUPPushChannel.m */; };
		40FDF5D3255419F100E1B6E5 /* FakeUPPushProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5BF255419F100E1B6E5 /* FakeUPPushProvider.m */; };
		40FDF5D4255419F100E1B6E5 /* FakeUPPushDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5C3255419F100E1B6E5 /* FakeUPPushDataSource.m */; };
		40FDF5D5255419F100E1B6E5 /* FakeUPPushManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5C6255419F100E1B6E5 /* FakeUPPushManager.m */; };
		40FDF5D6255419F100E1B6E5 /* CucumberRunner.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5C7255419F100E1B6E5 /* CucumberRunner.m */; };
		40FDF5D7255419F100E1B6E5 /* StepsUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5C9255419F100E1B6E5 /* StepsUtils.m */; };
		5E857ABE2AD682F400093015 /* UPPushNotDisturbAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 5E857ABD2AD682F400093015 /* UPPushNotDisturbAPI.m */; };
		67C898F72DF9938F002270C4 /* UPPushAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 67C898F62DF9938F002270C4 /* UPPushAlertView.m */; };
		84C17FF62D2BC68100932C86 /* UPPushLocalizationConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 84C17FF42D2BC68100932C86 /* UPPushLocalizationConfig.m */; };
		918E76D59DF40634BBDEFD36 /* libPods-UPPush_abstract_pod-UPPushUI.a in Frameworks */ = {isa = PBXBuildFile; fileRef = ECA8E9DB0BC322A63A18AE1F /* libPods-UPPush_abstract_pod-UPPushUI.a */; };
		9774326DC68172EAFFDACE4F /* libPods-UPPush_abstract_pod-UPPushTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 9069DC4A838E7273BCC13DD3 /* libPods-UPPush_abstract_pod-UPPushTests.a */; };
		A2D541CF5C555641556B18FE /* libPods-UPPush_abstract_pod-UPPushCore.a in Frameworks */ = {isa = PBXBuildFile; fileRef = ECAB625BDF2DEDF64A9287D0 /* libPods-UPPush_abstract_pod-UPPushCore.a */; };
		A58743D92DF29128005E7CC8 /* UPPushGetIconRequestTool.m in Sources */ = {isa = PBXBuildFile; fileRef = A58743D72DF29128005E7CC8 /* UPPushGetIconRequestTool.m */; };
		A58743DA2DF29128005E7CC8 /* UPPushGetIconRequestManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A58743D42DF29128005E7CC8 /* UPPushGetIconRequestManager.m */; };
		A58743DB2DF29128005E7CC8 /* UPPushGetIconAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = A58743CE2DF29128005E7CC8 /* UPPushGetIconAPI.m */; };
		A58743DC2DF29128005E7CC8 /* UPPushGetIconResultModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A58743D12DF29128005E7CC8 /* UPPushGetIconResultModel.m */; };
		A58743DD2DF291D5005E7CC8 /* libUPPush.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 40FDF3C42554093300E1B6E5 /* libUPPush.a */; };
		DCD5DD88C8A5254B90E665F3 /* libPods-UPPush_abstract_pod-Debugger.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DF8113219FCB10B55F40E2F5 /* libPods-UPPush_abstract_pod-Debugger.a */; };
		EC66299E7A093EB83B5E99A2 /* libPods-NotificationServiceExtension.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5A49963B6C247668EEBE6D8A /* libPods-NotificationServiceExtension.a */; };
		F708C1B027C36D3300B1CAC4 /* UPPushDownloadManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F708C1AF27C36D3300B1CAC4 /* UPPushDownloadManager.m */; };
		F70AC2BB2834C02B00EFF594 /* NotificationService.m in Sources */ = {isa = PBXBuildFile; fileRef = F72729EE27D72AC000DBB9C5 /* NotificationService.m */; };
		F70AC2BD2834C02B00EFF594 /* libUPPushServiceExtension.a in Frameworks */ = {isa = PBXBuildFile; fileRef = F75D020A27BF928900259519 /* libUPPushServiceExtension.a */; };
		F70AC2CF2835D11F00EFF594 /* UPFirebaseChannel.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5992554199D00E1B6E5 /* UPFirebaseChannel.m */; };
		F70AC2D02835D11F00EFF594 /* UPUserLoginApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5822554193D00E1B6E5 /* UPUserLoginApi.m */; };
		F70AC2D12835D11F00EFF594 /* SEUserLoginApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5832554193D00E1B6E5 /* SEUserLoginApi.m */; };
		F70AC2D22835D11F00EFF594 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF3FD2554096B00E1B6E5 /* ViewController.m */; };
		F70AC2D32835D11F00EFF594 /* UPJPushChannel.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF5912554199300E1B6E5 /* UPJPushChannel.m */; };
		F70AC2D42835D11F00EFF594 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF3F72554096B00E1B6E5 /* AppDelegate.m */; };
		F70AC2D52835D11F00EFF594 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 40FDF4082554096C00E1B6E5 /* main.m */; };
		F70AC2D72835D11F00EFF594 /* libUPPushServiceExtension.a in Frameworks */ = {isa = PBXBuildFile; fileRef = F75D020A27BF928900259519 /* libUPPushServiceExtension.a */; };
		F70AC2D82835D11F00EFF594 /* libUPPushUI.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 40FDF3E42554095C00E1B6E5 /* libUPPushUI.a */; };
		F70AC2DB2835D11F00EFF594 /* UPPushResource.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 406C0151269BE39700572FCB /* UPPushResource.bundle */; };
		F70AC2DC2835D11F00EFF594 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 40FDF4042554096C00E1B6E5 /* LaunchScreen.storyboard */; };
		F70AC2DD2835D11F00EFF594 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 404EF224255A70B40030B1F7 /* GoogleService-Info.plist */; };
		F70AC2DE2835D11F00EFF594 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 40FDF4022554096C00E1B6E5 /* Assets.xcassets */; };
		F70AC2DF2835D11F00EFF594 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 40FDF3FF2554096B00E1B6E5 /* Main.storyboard */; };
		F710672D27E70B4D00D2B8CB /* libUPPushServiceExtension.a in Frameworks */ = {isa = PBXBuildFile; fileRef = F75D020A27BF928900259519 /* libUPPushServiceExtension.a */; };
		F72729EF27D72AC000DBB9C5 /* NotificationService.m in Sources */ = {isa = PBXBuildFile; fileRef = F72729EE27D72AC000DBB9C5 /* NotificationService.m */; };
		F72729F327D72AC100DBB9C5 /* NotificationServiceExtension.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = F72729EB27D72AC000DBB9C5 /* NotificationServiceExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		F72729FA27D72B2300DBB9C5 /* libUPPush.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 40FDF3C42554093300E1B6E5 /* libUPPush.a */; };
		F72729FB27D72B3700DBB9C5 /* libUPPushCore.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 40FDF3D52554094B00E1B6E5 /* libUPPushCore.a */; };
		F7272A0227D72F1000DBB9C5 /* UPPushSEDeclaration.m in Sources */ = {isa = PBXBuildFile; fileRef = F7272A0127D72F1000DBB9C5 /* UPPushSEDeclaration.m */; };
		F729C254292F14D80052C67A /* UPPushAbilityTools.m in Sources */ = {isa = PBXBuildFile; fileRef = F729C252292F13900052C67A /* UPPushAbilityTools.m */; };
		F729C25B293266060052C67A /* UPPushFactor.m in Sources */ = {isa = PBXBuildFile; fileRef = F729C25A293266060052C67A /* UPPushFactor.m */; };
		F729C25E2932666A0052C67A /* UPPushRegisterFactor.m in Sources */ = {isa = PBXBuildFile; fileRef = F729C25D2932666A0052C67A /* UPPushRegisterFactor.m */; };
		F729C2682935D0460052C67A /* libUPPush.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 40FDF3C42554093300E1B6E5 /* libUPPush.a */; };
		F740E72827C4EAF700CD5C3D /* UPPushPersistenceManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F740E72727C4EAF700CD5C3D /* UPPushPersistenceManager.m */; };
		F7437E2D2702F7B20065AB9A /* features in Resources */ = {isa = PBXBuildFile; fileRef = F7437E2C2702F7B20065AB9A /* features */; };
		F75D020E27BF928A00259519 /* UPPushServiceExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = F75D020D27BF928A00259519 /* UPPushServiceExtension.m */; };
		F75D020F27BF928A00259519 /* UPPushServiceExtension.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = F75D020C27BF928A00259519 /* UPPushServiceExtension.h */; };
		F766EBAD27D6FC6200BCE5F6 /* libUPPushServiceExtension.a in Frameworks */ = {isa = PBXBuildFile; fileRef = F75D020A27BF928900259519 /* libUPPushServiceExtension.a */; };
		F784FA1F29A852B8007F03FB /* UIAlertView+UPPushEx.m in Sources */ = {isa = PBXBuildFile; fileRef = F784FA1E29A852B8007F03FB /* UIAlertView+UPPushEx.m */; };
		F79092EA2828BD5300EE61D2 /* UPPushMessageProcessor.m in Sources */ = {isa = PBXBuildFile; fileRef = F79092E92828BD5300EE61D2 /* UPPushMessageProcessor.m */; };
		F79092EE2828C23500EE61D2 /* UPPushMessageProcessorFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = F79092ED2828C23500EE61D2 /* UPPushMessageProcessorFactory.m */; };
		F7E36A382991117F00D82BF2 /* FakeUPPushSettings.m in Sources */ = {isa = PBXBuildFile; fileRef = F7E36A372991117F00D82BF2 /* FakeUPPushSettings.m */; };
		F7EA35D2283B6C75006A77B3 /* SYNNotificationServiceExtension.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = F70AC2C32834C02B00EFF594 /* SYNNotificationServiceExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		F7F1E3A0299101A900EA3BD8 /* UPPushSettings.m in Sources */ = {isa = PBXBuildFile; fileRef = F7F1E39E2990FC4400EA3BD8 /* UPPushSettings.m */; };
		FC544597A978479576C7F980 /* libPods-UPPushServiceExtension.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 0F2608C6FA3E587222EB790B /* libPods-UPPushServiceExtension.a */; };
		FE6251E0E45B513A78D736B9 /* libPods-UPPush_abstract_pod-UPPush.a in Frameworks */ = {isa = PBXBuildFile; fileRef = D3D8518C38515550A0CD1217 /* libPods-UPPush_abstract_pod-UPPush.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		401A98EF25541F5800DA11FA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 40FDF3BC2554093300E1B6E5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 40FDF3D42554094B00E1B6E5;
			remoteInfo = UPPushCore;
		};
		401A98F725541F6600DA11FA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 40FDF3BC2554093300E1B6E5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 40FDF3E32554095C00E1B6E5;
			remoteInfo = UPPushUI;
		};
		406C015E269C1AF300572FCB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 40FDF3BC2554093300E1B6E5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 406C0150269BE39700572FCB;
			remoteInfo = UPPushResource;
		};
		F70AC2B92834C02B00EFF594 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 40FDF3BC2554093300E1B6E5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F75D020927BF928900259519;
			remoteInfo = UPPushServiceExtension;
		};
		F70AC2C82835D11F00EFF594 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 40FDF3BC2554093300E1B6E5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 406C0150269BE39700572FCB;
			remoteInfo = UPPushResource;
		};
		F70AC2CA2835D11F00EFF594 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 40FDF3BC2554093300E1B6E5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 40FDF3E32554095C00E1B6E5;
			remoteInfo = UPPushUI;
		};
		F72729F127D72AC100DBB9C5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 40FDF3BC2554093300E1B6E5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F72729EA27D72AC000DBB9C5;
			remoteInfo = NotificationServiceExtension;
		};
		F72729F727D72ADE00DBB9C5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 40FDF3BC2554093300E1B6E5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F75D020927BF928900259519;
			remoteInfo = UPPushServiceExtension;
		};
		F729C2662935D0400052C67A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 40FDF3BC2554093300E1B6E5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 40FDF3C32554093300E1B6E5;
			remoteInfo = UPPush;
		};
		F766EBB327D7225100BCE5F6 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 40FDF3BC2554093300E1B6E5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 40FDF3C32554093300E1B6E5;
			remoteInfo = UPPush;
		};
		F7EA35D3283B6DE7006A77B3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 40FDF3BC2554093300E1B6E5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F70AC2B72834C02B00EFF594;
			remoteInfo = SYNNotificationServiceExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		40FDF3C22554093300E1B6E5 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40FDF3D32554094B00E1B6E5 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40FDF3E22554095C00E1B6E5 /* Copy Files */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			name = "Copy Files";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F708C1A727C35FFE00B1CAC4 /* Embed App Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				F72729F327D72AC100DBB9C5 /* NotificationServiceExtension.appex in Embed App Extensions */,
			);
			name = "Embed App Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F70AC2E12835D11F00EFF594 /* Embed App Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				F7EA35D2283B6C75006A77B3 /* SYNNotificationServiceExtension.appex in Embed App Extensions */,
			);
			name = "Embed App Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F75D020827BF928900259519 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
				F75D020F27BF928A00259519 /* UPPushServiceExtension.h in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0F2608C6FA3E587222EB790B /* libPods-UPPushServiceExtension.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UPPushServiceExtension.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		1065BB6712399C8DC3036771 /* Pods-UPPush_abstract_pod-UPPush.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPPush_abstract_pod-UPPush.debug.xcconfig"; path = "Target Support Files/Pods-UPPush_abstract_pod-UPPush/Pods-UPPush_abstract_pod-UPPush.debug.xcconfig"; sourceTree = "<group>"; };
		1712FDBED52C4424B491ABA5 /* Pods-UPPush_abstract_pod-UPPushUI.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPPush_abstract_pod-UPPushUI.release.xcconfig"; path = "Target Support Files/Pods-UPPush_abstract_pod-UPPushUI/Pods-UPPush_abstract_pod-UPPushUI.release.xcconfig"; sourceTree = "<group>"; };
		38DCA9BEDEE63E9AA6A6BBF5 /* Pods-UPPush_abstract_pod-Debugger.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPPush_abstract_pod-Debugger.release.xcconfig"; path = "Target Support Files/Pods-UPPush_abstract_pod-Debugger/Pods-UPPush_abstract_pod-Debugger.release.xcconfig"; sourceTree = "<group>"; };
		400E82992592E61D0062CDC9 /* UPPushBuildInProcessObserveManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushBuildInProcessObserveManager.h; sourceTree = "<group>"; };
		400E829A2592E61D0062CDC9 /* UPPushBuildInProcessObserveManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushBuildInProcessObserveManager.m; sourceTree = "<group>"; };
		400E82D52593176E0062CDC9 /* UPPushBuildInProcessQueueManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushBuildInProcessQueueManager.h; sourceTree = "<group>"; };
		400E82D62593176E0062CDC9 /* UPPushBuildInProcessQueueManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushBuildInProcessQueueManager.m; sourceTree = "<group>"; };
		403032D925591E5B0092915F /* UPPushInterceptorManagerProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushInterceptorManagerProtocol.h; sourceTree = "<group>"; };
		4041A008255B7C6300C0D9B0 /* UPPushMessage+Tool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UPPushMessage+Tool.h"; sourceTree = "<group>"; };
		4041A009255B7C6300C0D9B0 /* UPPushMessage+Tool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UPPushMessage+Tool.m"; sourceTree = "<group>"; };
		4041A08E255BD3E700C0D9B0 /* UPPushAlertController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushAlertController.h; sourceTree = "<group>"; };
		4041A08F255BD3E700C0D9B0 /* UPPushAlertController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushAlertController.m; sourceTree = "<group>"; };
		4041A14E255BE1A200C0D9B0 /* UPPushAlertModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushAlertModel.h; sourceTree = "<group>"; };
		4041A14F255BE1A200C0D9B0 /* UPPushAlertModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushAlertModel.m; sourceTree = "<group>"; };
		4041A16A255BE27B00C0D9B0 /* UPPushAlertActionModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushAlertActionModel.h; sourceTree = "<group>"; };
		4041A16B255BE27B00C0D9B0 /* UPPushAlertActionModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushAlertActionModel.m; sourceTree = "<group>"; };
		4041A174255BF3D300C0D9B0 /* UPPushAlertManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushAlertManager.h; sourceTree = "<group>"; };
		4041A175255BF3D300C0D9B0 /* UPPushAlertManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushAlertManager.m; sourceTree = "<group>"; };
		4041A17E255CDA0800C0D9B0 /* UPPushBroadcastController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushBroadcastController.h; sourceTree = "<group>"; };
		4041A17F255CDA0800C0D9B0 /* UPPushBroadcastController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushBroadcastController.m; sourceTree = "<group>"; };
		4041A188255CE17700C0D9B0 /* UPPushNotification.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushNotification.h; sourceTree = "<group>"; };
		4041A189255CE17700C0D9B0 /* UPPushNotification.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushNotification.m; sourceTree = "<group>"; };
		4041A23225621D1000C0D9B0 /* MessageSteps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MessageSteps.h; sourceTree = "<group>"; };
		4041A23325621D1000C0D9B0 /* MessageSteps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MessageSteps.m; sourceTree = "<group>"; };
		4041A25A25622B2600C0D9B0 /* FakeUPPushAllInterceptor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FakeUPPushAllInterceptor.h; sourceTree = "<group>"; };
		4041A25B25622B2600C0D9B0 /* FakeUPPushAllInterceptor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FakeUPPushAllInterceptor.m; sourceTree = "<group>"; };
		4041A3E925636B8E00C0D9B0 /* TestInterceptorCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TestInterceptorCache.h; sourceTree = "<group>"; };
		4041A3EA25636B8E00C0D9B0 /* TestInterceptorCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TestInterceptorCache.m; sourceTree = "<group>"; };
		404EF224255A70B40030B1F7 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = "GoogleService-Info.plist"; sourceTree = SOURCE_ROOT; };
		404EF240255A96420030B1F7 /* UPPushMessage+Completion.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UPPushMessage+Completion.h"; sourceTree = "<group>"; };
		404EF241255A96420030B1F7 /* UPPushMessage+Completion.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UPPushMessage+Completion.m"; sourceTree = "<group>"; };
		404EF24A255A97E90030B1F7 /* UPPushMessageExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushMessageExtension.h; sourceTree = "<group>"; };
		406C012526929B2C00572FCB /* UPPushToastManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushToastManager.h; sourceTree = "<group>"; };
		406C012626929B2C00572FCB /* UPPushToastManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushToastManager.m; sourceTree = "<group>"; };
		406C012A26929D0100572FCB /* UPPushToastController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushToastController.h; sourceTree = "<group>"; };
		406C012B26929D0100572FCB /* UPPushToastController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushToastController.m; sourceTree = "<group>"; };
		406C012D26929D1600572FCB /* UPPushToastModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushToastModel.h; sourceTree = "<group>"; };
		406C012E26929D1600572FCB /* UPPushToastModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushToastModel.m; sourceTree = "<group>"; };
		406C01302692A48C00572FCB /* UPPushToastView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushToastView.h; sourceTree = "<group>"; };
		406C01312692A48C00572FCB /* UPPushToastView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushToastView.m; sourceTree = "<group>"; };
		406C0151269BE39700572FCB /* UPPushResource.bundle */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = UPPushResource.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		406C015A269BEACD00572FCB /* UPPushToastArrowLabel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushToastArrowLabel.h; sourceTree = "<group>"; };
		406C015B269BEACD00572FCB /* UPPushToastArrowLabel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushToastArrowLabel.m; sourceTree = "<group>"; };
		40A19E7F256608F3006767C1 /* UPPushStringTools.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushStringTools.h; sourceTree = "<group>"; };
		40A19E80256608F3006767C1 /* UPPushStringTools.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushStringTools.m; sourceTree = "<group>"; };
		40FDF3C42554093300E1B6E5 /* libUPPush.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libUPPush.a; sourceTree = BUILT_PRODUCTS_DIR; };
		40FDF3D52554094B00E1B6E5 /* libUPPushCore.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libUPPushCore.a; sourceTree = BUILT_PRODUCTS_DIR; };
		40FDF3E42554095C00E1B6E5 /* libUPPushUI.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libUPPushUI.a; sourceTree = BUILT_PRODUCTS_DIR; };
		40FDF3F42554096B00E1B6E5 /* Debugger.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Debugger.app; sourceTree = BUILT_PRODUCTS_DIR; };
		40FDF3F62554096B00E1B6E5 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		40FDF3F72554096B00E1B6E5 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		40FDF3FC2554096B00E1B6E5 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		40FDF3FD2554096B00E1B6E5 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		40FDF4002554096B00E1B6E5 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		40FDF4022554096C00E1B6E5 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		40FDF4052554096C00E1B6E5 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		40FDF4072554096C00E1B6E5 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		40FDF4082554096C00E1B6E5 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		40FDF42D255409AA00E1B6E5 /* UPPushTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = UPPushTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		40FDF431255409AA00E1B6E5 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		40FDF4592554179100E1B6E5 /* UPPushManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushManager.m; sourceTree = "<group>"; };
		40FDF45A2554179100E1B6E5 /* UPPushManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushManager.h; sourceTree = "<group>"; };
		40FDF45C2554179100E1B6E5 /* UPPushBroadcastSender.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushBroadcastSender.m; sourceTree = "<group>"; };
		40FDF45D2554179100E1B6E5 /* UPPushBroadcastSender.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushBroadcastSender.h; sourceTree = "<group>"; };
		40FDF45F2554179100E1B6E5 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		40FDF4622554179100E1B6E5 /* UPPushHomeDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushHomeDataSource.h; sourceTree = "<group>"; };
		40FDF4632554179100E1B6E5 /* UPPushHomeDataSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushHomeDataSource.m; sourceTree = "<group>"; };
		40FDF4652554179100E1B6E5 /* UPPushResult.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushResult.m; sourceTree = "<group>"; };
		40FDF4662554179100E1B6E5 /* UPPushResult.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushResult.h; sourceTree = "<group>"; };
		40FDF4682554179100E1B6E5 /* UPPushSeasiaDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushSeasiaDataSource.h; sourceTree = "<group>"; };
		40FDF4692554179100E1B6E5 /* UPPushSeasiaDataSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushSeasiaDataSource.m; sourceTree = "<group>"; };
		40FDF46B2554179100E1B6E5 /* UPPushResponseParser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushResponseParser.h; sourceTree = "<group>"; };
		40FDF46C2554179100E1B6E5 /* UPPushResponseParser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushResponseParser.m; sourceTree = "<group>"; };
		40FDF46F2554179100E1B6E5 /* UPPushDataSourceRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushDataSourceRequest.h; sourceTree = "<group>"; };
		40FDF4702554179100E1B6E5 /* UPPushDataSourceRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushDataSourceRequest.m; sourceTree = "<group>"; };
		40FDF4722554179100E1B6E5 /* UPPushInitializer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushInitializer.m; sourceTree = "<group>"; };
		40FDF4732554179100E1B6E5 /* UPPushInitializer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushInitializer.h; sourceTree = "<group>"; };
		40FDF4902554181B00E1B6E5 /* UPPushHandlerProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushHandlerProtocol.h; sourceTree = "<group>"; };
		40FDF4922554181B00E1B6E5 /* UPPushDataSourceProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushDataSourceProtocol.h; sourceTree = "<group>"; };
		40FDF4942554181B00E1B6E5 /* UPPushResultProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushResultProtocol.h; sourceTree = "<group>"; };
		40FDF4972554181B00E1B6E5 /* UPPushDispatcherProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushDispatcherProtocol.h; sourceTree = "<group>"; };
		40FDF4992554181B00E1B6E5 /* UPPushProviderProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushProviderProtocol.h; sourceTree = "<group>"; };
		40FDF49B2554181B00E1B6E5 /* UPPushMessageCustomPropertyProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageCustomPropertyProtocol.h; sourceTree = "<group>"; };
		40FDF49C2554181B00E1B6E5 /* UPPushMessageAPNsInfoProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageAPNsInfoProtocol.h; sourceTree = "<group>"; };
		40FDF49F2554181B00E1B6E5 /* UPPushMessageUIButtonProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageUIButtonProtocol.h; sourceTree = "<group>"; };
		40FDF4A02554181B00E1B6E5 /* UPPushMessageUIProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageUIProtocol.h; sourceTree = "<group>"; };
		40FDF4A12554181B00E1B6E5 /* UPPushMessageBodyProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageBodyProtocol.h; sourceTree = "<group>"; };
		40FDF4A52554181B00E1B6E5 /* UPPushMessageDeviceControlProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageDeviceControlProtocol.h; sourceTree = "<group>"; };
		40FDF4A62554181B00E1B6E5 /* UPPushMessageDeviceProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageDeviceProtocol.h; sourceTree = "<group>"; };
		40FDF4A72554181B00E1B6E5 /* UPPushMessageExtraDataProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageExtraDataProtocol.h; sourceTree = "<group>"; };
		40FDF4A82554181B00E1B6E5 /* UPPushMessageAPIProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageAPIProtocol.h; sourceTree = "<group>"; };
		40FDF4A92554181B00E1B6E5 /* UPPushMessagePageProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessagePageProtocol.h; sourceTree = "<group>"; };
		40FDF4AA2554181B00E1B6E5 /* UPPushMessageProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageProtocol.h; sourceTree = "<group>"; };
		40FDF4AC2554181B00E1B6E5 /* UPPushParserProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushParserProtocol.h; sourceTree = "<group>"; };
		40FDF4AE2554181B00E1B6E5 /* UPPushChannelProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushChannelProtocol.h; sourceTree = "<group>"; };
		40FDF4B02554181B00E1B6E5 /* UPPushContextProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushContextProtocol.h; sourceTree = "<group>"; };
		40FDF4B12554181B00E1B6E5 /* UPPushInitializerProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushInitializerProtocol.h; sourceTree = "<group>"; };
		40FDF4B22554181B00E1B6E5 /* UPPushProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushProtocol.h; sourceTree = "<group>"; };
		40FDF4B42554181B00E1B6E5 /* UPPushBroadcastSenderProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushBroadcastSenderProtocol.h; sourceTree = "<group>"; };
		40FDF4B62554181B00E1B6E5 /* UPPushInterceptorProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushInterceptorProtocol.h; sourceTree = "<group>"; };
		40FDF4B92554181B00E1B6E5 /* UPPushRetryAssistant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushRetryAssistant.m; sourceTree = "<group>"; };
		40FDF4BA2554181B00E1B6E5 /* UPPushRetryAssistant.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushRetryAssistant.h; sourceTree = "<group>"; };
		40FDF4BC2554181B00E1B6E5 /* UPPushDispatcher.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushDispatcher.h; sourceTree = "<group>"; };
		40FDF4BD2554181B00E1B6E5 /* UPPushDispatcher.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushDispatcher.m; sourceTree = "<group>"; };
		40FDF4C12554181B00E1B6E5 /* UPPushMessage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushMessage.m; sourceTree = "<group>"; };
		40FDF4C22554181B00E1B6E5 /* UPPushOriginalMessage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushOriginalMessage.m; sourceTree = "<group>"; };
		40FDF4C52554181B00E1B6E5 /* UPPushMessageUIButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushMessageUIButton.m; sourceTree = "<group>"; };
		40FDF4C62554181B00E1B6E5 /* UPPushMessageUI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushMessageUI.m; sourceTree = "<group>"; };
		40FDF4C72554181B00E1B6E5 /* UPPushMessageUIButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageUIButton.h; sourceTree = "<group>"; };
		40FDF4C82554181B00E1B6E5 /* UPPushMessageUI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageUI.h; sourceTree = "<group>"; };
		40FDF4C92554181B00E1B6E5 /* UPPushMessageBody.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushMessageBody.m; sourceTree = "<group>"; };
		40FDF4CD2554181B00E1B6E5 /* UPPushMessageBody.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageBody.h; sourceTree = "<group>"; };
		40FDF4CF2554181B00E1B6E5 /* UPPushMessageAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageAPI.h; sourceTree = "<group>"; };
		40FDF4D02554181B00E1B6E5 /* UPPushMessagePage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushMessagePage.m; sourceTree = "<group>"; };
		40FDF4D12554181B00E1B6E5 /* UPPushMessageDevice.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushMessageDevice.m; sourceTree = "<group>"; };
		40FDF4D22554181B00E1B6E5 /* UPPushMessageExtraData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushMessageExtraData.m; sourceTree = "<group>"; };
		40FDF4D32554181B00E1B6E5 /* UPPushMessageDeviceControl.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushMessageDeviceControl.m; sourceTree = "<group>"; };
		40FDF4D42554181B00E1B6E5 /* UPPushMessageDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageDevice.h; sourceTree = "<group>"; };
		40FDF4D52554181B00E1B6E5 /* UPPushMessagePage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessagePage.h; sourceTree = "<group>"; };
		40FDF4D62554181B00E1B6E5 /* UPPushMessageAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushMessageAPI.m; sourceTree = "<group>"; };
		40FDF4D72554181B00E1B6E5 /* UPPushMessageDeviceControl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageDeviceControl.h; sourceTree = "<group>"; };
		40FDF4D82554181B00E1B6E5 /* UPPushMessageExtraData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessageExtraData.h; sourceTree = "<group>"; };
		40FDF4D92554181B00E1B6E5 /* UPPushMessage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMessage.h; sourceTree = "<group>"; };
		40FDF4DA2554181B00E1B6E5 /* UPPushOriginalMessage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushOriginalMessage.h; sourceTree = "<group>"; };
		40FDF4DC2554181B00E1B6E5 /* UPPushParser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushParser.m; sourceTree = "<group>"; };
		40FDF4DD2554181B00E1B6E5 /* UPPushParser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushParser.h; sourceTree = "<group>"; };
		40FDF4DF2554181B00E1B6E5 /* UPPush.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPush.h; sourceTree = "<group>"; };
		40FDF4E02554181B00E1B6E5 /* UPPush.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPush.m; sourceTree = "<group>"; };
		40FDF4E22554181B00E1B6E5 /* UPPushInterceptorManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushInterceptorManager.h; sourceTree = "<group>"; };
		40FDF4E32554181B00E1B6E5 /* UPPushInterceptorManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushInterceptorManager.m; sourceTree = "<group>"; };
		40FDF4E52554181B00E1B6E5 /* UPPushBadgeManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushBadgeManager.m; sourceTree = "<group>"; };
		40FDF4E62554181B00E1B6E5 /* UPPushBadgeManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushBadgeManager.h; sourceTree = "<group>"; };
		40FDF4EA2554181B00E1B6E5 /* UPPushReportStatusAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushReportStatusAPI.m; sourceTree = "<group>"; };
		40FDF4EB2554181B00E1B6E5 /* UPPushRegisterAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushRegisterAPI.h; sourceTree = "<group>"; };
		40FDF4EC2554181B00E1B6E5 /* UPPushUnregisterAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushUnregisterAPI.h; sourceTree = "<group>"; };
		40FDF4ED2554181B00E1B6E5 /* UPPushReportStatusAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushReportStatusAPI.h; sourceTree = "<group>"; };
		40FDF4EE2554181B00E1B6E5 /* UPPushRegisterAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushRegisterAPI.m; sourceTree = "<group>"; };
		40FDF4EF2554181B00E1B6E5 /* UPPushUnregisterAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushUnregisterAPI.m; sourceTree = "<group>"; };
		40FDF4F12554181B00E1B6E5 /* UPPushRequestBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushRequestBase.m; sourceTree = "<group>"; };
		40FDF4F22554181B00E1B6E5 /* UPPushRequestBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushRequestBase.h; sourceTree = "<group>"; };
		40FDF4F52554181B00E1B6E5 /* UPPushSEReportStatusAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushSEReportStatusAPI.m; sourceTree = "<group>"; };
		40FDF4F62554181B00E1B6E5 /* UPPushSEUnregisterAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushSEUnregisterAPI.m; sourceTree = "<group>"; };
		40FDF4F72554181B00E1B6E5 /* UPPushSERegisterAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushSERegisterAPI.m; sourceTree = "<group>"; };
		40FDF4F82554181B00E1B6E5 /* UPPushSEUnregisterAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushSEUnregisterAPI.h; sourceTree = "<group>"; };
		40FDF4F92554181B00E1B6E5 /* UPPushSEReportStatusAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushSEReportStatusAPI.h; sourceTree = "<group>"; };
		40FDF4FA2554181B00E1B6E5 /* UPPushSERegisterAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushSERegisterAPI.h; sourceTree = "<group>"; };
		40FDF4FC2554181B00E1B6E5 /* UPPushSERequestBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushSERequestBase.h; sourceTree = "<group>"; };
		40FDF4FD2554181B00E1B6E5 /* UPPushSERequestBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushSERequestBase.m; sourceTree = "<group>"; };
		40FDF4FE2554181B00E1B6E5 /* UPPushGlobalDeclaration.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushGlobalDeclaration.h; sourceTree = "<group>"; };
		40FDF5002554181B00E1B6E5 /* UPPushEncryption.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushEncryption.m; sourceTree = "<group>"; };
		40FDF5012554181B00E1B6E5 /* UPPushAPIUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushAPIUtil.m; sourceTree = "<group>"; };
		40FDF5022554181B00E1B6E5 /* UPPushAPIUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushAPIUtil.h; sourceTree = "<group>"; };
		40FDF5032554181B00E1B6E5 /* UPPushEncryption.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushEncryption.h; sourceTree = "<group>"; };
		40FDF52C255418EC00E1B6E5 /* UPPushApiDataHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushApiDataHandler.h; sourceTree = "<group>"; };
		40FDF52E255418EC00E1B6E5 /* UPPushGIOTrack.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushGIOTrack.m; sourceTree = "<group>"; };
		40FDF52F255418EC00E1B6E5 /* UPPushGIOTrack.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushGIOTrack.h; sourceTree = "<group>"; };
		40FDF533255418EC00E1B6E5 /* UPPushDevControlDataHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushDevControlDataHandler.m; sourceTree = "<group>"; };
		40FDF534255418EC00E1B6E5 /* UPPushCustomizedMessageProcessor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushCustomizedMessageProcessor.m; sourceTree = "<group>"; };
		40FDF535255418EC00E1B6E5 /* UPPushViewMsgComponent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushViewMsgComponent.h; sourceTree = "<group>"; };
		40FDF536255418EC00E1B6E5 /* UPPushLogoutObserver.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushLogoutObserver.h; sourceTree = "<group>"; };
		40FDF537255418EC00E1B6E5 /* UPPushPageDataHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushPageDataHandler.h; sourceTree = "<group>"; };
		40FDF538255418EC00E1B6E5 /* UPPushNotificationMessageProcessor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushNotificationMessageProcessor.m; sourceTree = "<group>"; };
		40FDF53A255418EC00E1B6E5 /* UPPushNoViewMsgComponent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushNoViewMsgComponent.h; sourceTree = "<group>"; };
		40FDF53B255418EC00E1B6E5 /* UPPushHandlerTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushHandlerTool.m; sourceTree = "<group>"; };
		40FDF53D255418EC00E1B6E5 /* UPPushApiDataHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushApiDataHandler.m; sourceTree = "<group>"; };
		40FDF53E255418EC00E1B6E5 /* UPPushDevControlDataHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushDevControlDataHandler.h; sourceTree = "<group>"; };
		40FDF541255418EC00E1B6E5 /* UPPushNoViewMsgComponent.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushNoViewMsgComponent.m; sourceTree = "<group>"; };
		40FDF543255418EC00E1B6E5 /* UPPushPageDataHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushPageDataHandler.m; sourceTree = "<group>"; };
		40FDF544255418EC00E1B6E5 /* UPPushLogoutObserver.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushLogoutObserver.m; sourceTree = "<group>"; };
		40FDF545255418EC00E1B6E5 /* UPPushNotificationMessageProcessor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushNotificationMessageProcessor.h; sourceTree = "<group>"; };
		40FDF546255418EC00E1B6E5 /* UPPushCustomizedMessageProcessor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushCustomizedMessageProcessor.h; sourceTree = "<group>"; };
		40FDF547255418EC00E1B6E5 /* UPPushViewMsgComponent.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushViewMsgComponent.m; sourceTree = "<group>"; };
		40FDF549255418EC00E1B6E5 /* UPPushHandlerTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushHandlerTool.h; sourceTree = "<group>"; };
		40FDF54B255418EC00E1B6E5 /* UPPushBuildInUIInterceptor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushBuildInUIInterceptor.h; sourceTree = "<group>"; };
		40FDF54C255418EC00E1B6E5 /* UPPushBuildInUIInterceptor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushBuildInUIInterceptor.m; sourceTree = "<group>"; };
		40FDF550255418EC00E1B6E5 /* UPPushAppManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushAppManager.m; sourceTree = "<group>"; };
		40FDF551255418EC00E1B6E5 /* UPPushAppManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushAppManager.h; sourceTree = "<group>"; };
		40FDF552255418EC00E1B6E5 /* UPPushAppManagerProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushAppManagerProtocol.h; sourceTree = "<group>"; };
		40FDF554255418EC00E1B6E5 /* UPPushProgressShow.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushProgressShow.m; sourceTree = "<group>"; };
		40FDF555255418EC00E1B6E5 /* UPPushToast.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushToast.m; sourceTree = "<group>"; };
		40FDF556255418EC00E1B6E5 /* UPPushLoading.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushLoading.h; sourceTree = "<group>"; };
		40FDF558255418EC00E1B6E5 /* UPPushMacros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushMacros.h; sourceTree = "<group>"; };
		40FDF559255418EC00E1B6E5 /* UPPushProgressShow.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushProgressShow.h; sourceTree = "<group>"; };
		40FDF55B255418EC00E1B6E5 /* UIImage+UPPushImageEffects.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+UPPushImageEffects.m"; sourceTree = "<group>"; };
		40FDF55C255418EC00E1B6E5 /* UIImage+UPPushEmpty.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+UPPushEmpty.m"; sourceTree = "<group>"; };
		40FDF55D255418EC00E1B6E5 /* UIImage+UPPushImageEffects.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+UPPushImageEffects.h"; sourceTree = "<group>"; };
		40FDF55E255418EC00E1B6E5 /* UIImage+UPPushEmpty.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+UPPushEmpty.h"; sourceTree = "<group>"; };
		40FDF55F255418EC00E1B6E5 /* UPPushToast.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushToast.h; sourceTree = "<group>"; };
		40FDF560255418EC00E1B6E5 /* UPPushLoading.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushLoading.m; sourceTree = "<group>"; };
		40FDF5802554193D00E1B6E5 /* UPUserLoginApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserLoginApi.h; sourceTree = "<group>"; };
		40FDF5812554193D00E1B6E5 /* SEUserLoginApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEUserLoginApi.h; sourceTree = "<group>"; };
		40FDF5822554193D00E1B6E5 /* UPUserLoginApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserLoginApi.m; sourceTree = "<group>"; };
		40FDF5832554193D00E1B6E5 /* SEUserLoginApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEUserLoginApi.m; sourceTree = "<group>"; };
		40FDF5902554199300E1B6E5 /* UPJPushChannel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPJPushChannel.h; sourceTree = "<group>"; };
		40FDF5912554199300E1B6E5 /* UPJPushChannel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPJPushChannel.m; sourceTree = "<group>"; };
		40FDF5992554199D00E1B6E5 /* UPFirebaseChannel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPFirebaseChannel.m; sourceTree = "<group>"; };
		40FDF59A2554199D00E1B6E5 /* UPFirebaseChannel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPFirebaseChannel.h; sourceTree = "<group>"; };
		40FDF5AF255419F100E1B6E5 /* PushSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PushSteps.h; sourceTree = "<group>"; };
		40FDF5B0255419F100E1B6E5 /* InitializationSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = InitializationSteps.h; sourceTree = "<group>"; };
		40FDF5B1255419F100E1B6E5 /* InitializationSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = InitializationSteps.m; sourceTree = "<group>"; };
		40FDF5B2255419F100E1B6E5 /* PushSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PushSteps.m; sourceTree = "<group>"; };
		40FDF5B4255419F100E1B6E5 /* FakeUPPushChannel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FakeUPPushChannel.h; sourceTree = "<group>"; };
		40FDF5B5255419F100E1B6E5 /* FakeUPPushBroadcastSender.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FakeUPPushBroadcastSender.h; sourceTree = "<group>"; };
		40FDF5B6255419F100E1B6E5 /* FakeUPPushInitializer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FakeUPPushInitializer.m; sourceTree = "<group>"; };
		40FDF5B7255419F100E1B6E5 /* FakeUPPushProvider.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FakeUPPushProvider.h; sourceTree = "<group>"; };
		40FDF5B8255419F100E1B6E5 /* FakeUPPushDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FakeUPPushDataSource.h; sourceTree = "<group>"; };
		40FDF5B9255419F100E1B6E5 /* FakeUPPushResult.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FakeUPPushResult.m; sourceTree = "<group>"; };
		40FDF5BA255419F100E1B6E5 /* FakeUPPushBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FakeUPPushBase.m; sourceTree = "<group>"; };
		40FDF5BB255419F100E1B6E5 /* FakeUPPushInterceptor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FakeUPPushInterceptor.m; sourceTree = "<group>"; };
		40FDF5BC255419F100E1B6E5 /* FakeUPPushBroadcastSender.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FakeUPPushBroadcastSender.m; sourceTree = "<group>"; };
		40FDF5BD255419F100E1B6E5 /* FakeUPPushChannel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FakeUPPushChannel.m; sourceTree = "<group>"; };
		40FDF5BE255419F100E1B6E5 /* FakeUPPushInitializer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FakeUPPushInitializer.h; sourceTree = "<group>"; };
		40FDF5BF255419F100E1B6E5 /* FakeUPPushProvider.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FakeUPPushProvider.m; sourceTree = "<group>"; };
		40FDF5C0255419F100E1B6E5 /* FakeUPPushInterceptor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FakeUPPushInterceptor.h; sourceTree = "<group>"; };
		40FDF5C1255419F100E1B6E5 /* FakeUPPushBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FakeUPPushBase.h; sourceTree = "<group>"; };
		40FDF5C2255419F100E1B6E5 /* FakeUPPushResult.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FakeUPPushResult.h; sourceTree = "<group>"; };
		40FDF5C3255419F100E1B6E5 /* FakeUPPushDataSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FakeUPPushDataSource.m; sourceTree = "<group>"; };
		40FDF5C5255419F100E1B6E5 /* FakeUPPushManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FakeUPPushManager.h; sourceTree = "<group>"; };
		40FDF5C6255419F100E1B6E5 /* FakeUPPushManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FakeUPPushManager.m; sourceTree = "<group>"; };
		40FDF5C7255419F100E1B6E5 /* CucumberRunner.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CucumberRunner.m; sourceTree = "<group>"; };
		40FDF5C9255419F100E1B6E5 /* StepsUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StepsUtils.m; sourceTree = "<group>"; };
		40FDF5CA255419F100E1B6E5 /* StepsUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StepsUtils.h; sourceTree = "<group>"; };
		5A49963B6C247668EEBE6D8A /* libPods-NotificationServiceExtension.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-NotificationServiceExtension.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		5E857ABC2AD682F400093015 /* UPPushNotDisturbAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushNotDisturbAPI.h; sourceTree = "<group>"; };
		5E857ABD2AD682F400093015 /* UPPushNotDisturbAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushNotDisturbAPI.m; sourceTree = "<group>"; };
		5FC0B6D4784301FCC12830D4 /* Pods-UPPush_abstract_pod-UPPushTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPPush_abstract_pod-UPPushTests.debug.xcconfig"; path = "Target Support Files/Pods-UPPush_abstract_pod-UPPushTests/Pods-UPPush_abstract_pod-UPPushTests.debug.xcconfig"; sourceTree = "<group>"; };
		65B0CE5EC38EE21EA62E134E /* Pods-NotificationServiceExtension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.release.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.release.xcconfig"; sourceTree = "<group>"; };
		67C898F52DF9938F002270C4 /* UPPushAlertView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushAlertView.h; sourceTree = "<group>"; };
		67C898F62DF9938F002270C4 /* UPPushAlertView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushAlertView.m; sourceTree = "<group>"; };
		71F131A52938A78DEA96DC4E /* Pods-UPPushServiceExtension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPPushServiceExtension.debug.xcconfig"; path = "Target Support Files/Pods-UPPushServiceExtension/Pods-UPPushServiceExtension.debug.xcconfig"; sourceTree = "<group>"; };
		84C17FF42D2BC68100932C86 /* UPPushLocalizationConfig.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPPushLocalizationConfig.m; sourceTree = "<group>"; };
		84C17FF52D2BC68100932C86 /* UPPushLocalizationConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPushLocalizationConfig.h; sourceTree = "<group>"; };
		9069DC4A838E7273BCC13DD3 /* libPods-UPPush_abstract_pod-UPPushTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UPPush_abstract_pod-UPPushTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		91C15C6DAA9BBFE5DB9C93C8 /* Pods-UPPush_abstract_pod-UPPush.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPPush_abstract_pod-UPPush.release.xcconfig"; path = "Target Support Files/Pods-UPPush_abstract_pod-UPPush/Pods-UPPush_abstract_pod-UPPush.release.xcconfig"; sourceTree = "<group>"; };
		9E9B57D8BD763292BD85ECA5 /* Pods-NotificationServiceExtension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.debug.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.debug.xcconfig"; sourceTree = "<group>"; };
		A549AABA2DF6D1A000161174 /* UPPushResource_NoSign.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = UPPushResource_NoSign.xcconfig; sourceTree = "<group>"; };
		A58743CD2DF29128005E7CC8 /* UPPushGetIconAPI.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushGetIconAPI.h; sourceTree = "<group>"; };
		A58743CE2DF29128005E7CC8 /* UPPushGetIconAPI.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushGetIconAPI.m; sourceTree = "<group>"; };
		A58743D02DF29128005E7CC8 /* UPPushGetIconResultModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushGetIconResultModel.h; sourceTree = "<group>"; };
		A58743D12DF29128005E7CC8 /* UPPushGetIconResultModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushGetIconResultModel.m; sourceTree = "<group>"; };
		A58743D32DF29128005E7CC8 /* UPPushGetIconRequestManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushGetIconRequestManager.h; sourceTree = "<group>"; };
		A58743D42DF29128005E7CC8 /* UPPushGetIconRequestManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushGetIconRequestManager.m; sourceTree = "<group>"; };
		A58743D62DF29128005E7CC8 /* UPPushGetIconRequestTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushGetIconRequestTool.h; sourceTree = "<group>"; };
		A58743D72DF29128005E7CC8 /* UPPushGetIconRequestTool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushGetIconRequestTool.m; sourceTree = "<group>"; };
		AEC5C2562C560D9753A175D1 /* Pods-UPPush_abstract_pod-Debugger.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPPush_abstract_pod-Debugger.debug.xcconfig"; path = "Target Support Files/Pods-UPPush_abstract_pod-Debugger/Pods-UPPush_abstract_pod-Debugger.debug.xcconfig"; sourceTree = "<group>"; };
		B8CEC4896AB2CFF3B7C37020 /* Pods-UPPushServiceExtension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPPushServiceExtension.release.xcconfig"; path = "Target Support Files/Pods-UPPushServiceExtension/Pods-UPPushServiceExtension.release.xcconfig"; sourceTree = "<group>"; };
		BCA93657FB2533EF8003BBE6 /* Pods-UPPush_abstract_pod-UPPushTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPPush_abstract_pod-UPPushTests.release.xcconfig"; path = "Target Support Files/Pods-UPPush_abstract_pod-UPPushTests/Pods-UPPush_abstract_pod-UPPushTests.release.xcconfig"; sourceTree = "<group>"; };
		D05F810056980B3D907C0845 /* Pods-UPPush_abstract_pod-UPPushUI.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPPush_abstract_pod-UPPushUI.debug.xcconfig"; path = "Target Support Files/Pods-UPPush_abstract_pod-UPPushUI/Pods-UPPush_abstract_pod-UPPushUI.debug.xcconfig"; sourceTree = "<group>"; };
		D3D8518C38515550A0CD1217 /* libPods-UPPush_abstract_pod-UPPush.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UPPush_abstract_pod-UPPush.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		DF8113219FCB10B55F40E2F5 /* libPods-UPPush_abstract_pod-Debugger.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UPPush_abstract_pod-Debugger.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		E3BAEF2FC30694EF9C0DAE41 /* Pods-UPPush_abstract_pod-UPPushCore.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPPush_abstract_pod-UPPushCore.debug.xcconfig"; path = "Target Support Files/Pods-UPPush_abstract_pod-UPPushCore/Pods-UPPush_abstract_pod-UPPushCore.debug.xcconfig"; sourceTree = "<group>"; };
		ECA8E9DB0BC322A63A18AE1F /* libPods-UPPush_abstract_pod-UPPushUI.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UPPush_abstract_pod-UPPushUI.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		ECAB625BDF2DEDF64A9287D0 /* libPods-UPPush_abstract_pod-UPPushCore.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UPPush_abstract_pod-UPPushCore.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		F7020AD927E0A24300A1C78D /* NotificationServiceExtensionDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = NotificationServiceExtensionDebug.entitlements; sourceTree = "<group>"; };
		F708C19427C35F5100B1CAC4 /* UPPushServiceExtensionProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushServiceExtensionProtocol.h; sourceTree = "<group>"; };
		F708C1AE27C36D3300B1CAC4 /* UPPushDownloadManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushDownloadManager.h; sourceTree = "<group>"; };
		F708C1AF27C36D3300B1CAC4 /* UPPushDownloadManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushDownloadManager.m; sourceTree = "<group>"; };
		F70AC2C32834C02B00EFF594 /* SYNNotificationServiceExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = SYNNotificationServiceExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		F70AC2C42834C02B00EFF594 /* SYNNotificationServiceExtension-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "SYNNotificationServiceExtension-Info.plist"; path = "/Users/<USER>/develop/project/haier/native/modules/Push/UPPush/SYNNotificationServiceExtension-Info.plist"; sourceTree = "<absolute>"; };
		F70AC2E72835D11F00EFF594 /* SYNDebugger.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SYNDebugger.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F70AC2E82835D11F00EFF594 /* SYNDebugger-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "SYNDebugger-Info.plist"; path = "/Users/<USER>/develop/project/haier/native/modules/Push/UPPush/SYNDebugger-Info.plist"; sourceTree = "<absolute>"; };
		F72729EB27D72AC000DBB9C5 /* NotificationServiceExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = NotificationServiceExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		F72729ED27D72AC000DBB9C5 /* NotificationService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NotificationService.h; sourceTree = "<group>"; };
		F72729EE27D72AC000DBB9C5 /* NotificationService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NotificationService.m; sourceTree = "<group>"; };
		F72729F027D72AC000DBB9C5 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		F7272A0027D72F1000DBB9C5 /* UPPushSEDeclaration.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushSEDeclaration.h; sourceTree = "<group>"; };
		F7272A0127D72F1000DBB9C5 /* UPPushSEDeclaration.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushSEDeclaration.m; sourceTree = "<group>"; };
		F729C251292F13900052C67A /* UPPushAbilityTools.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushAbilityTools.h; sourceTree = "<group>"; };
		F729C252292F13900052C67A /* UPPushAbilityTools.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushAbilityTools.m; sourceTree = "<group>"; };
		F729C255292F17C70052C67A /* UPPushDataSourceDeclaration.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushDataSourceDeclaration.h; sourceTree = "<group>"; };
		F729C259293266060052C67A /* UPPushFactor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushFactor.h; sourceTree = "<group>"; };
		F729C25A293266060052C67A /* UPPushFactor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushFactor.m; sourceTree = "<group>"; };
		F729C25C2932666A0052C67A /* UPPushRegisterFactor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushRegisterFactor.h; sourceTree = "<group>"; };
		F729C25D2932666A0052C67A /* UPPushRegisterFactor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushRegisterFactor.m; sourceTree = "<group>"; };
		F740E72627C4EAF700CD5C3D /* UPPushPersistenceManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushPersistenceManager.h; sourceTree = "<group>"; };
		F740E72727C4EAF700CD5C3D /* UPPushPersistenceManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushPersistenceManager.m; sourceTree = "<group>"; };
		F7437E2C2702F7B20065AB9A /* features */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = features; sourceTree = SOURCE_ROOT; };
		F75D020A27BF928900259519 /* libUPPushServiceExtension.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libUPPushServiceExtension.a; sourceTree = BUILT_PRODUCTS_DIR; };
		F75D020C27BF928A00259519 /* UPPushServiceExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushServiceExtension.h; sourceTree = "<group>"; };
		F75D020D27BF928A00259519 /* UPPushServiceExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushServiceExtension.m; sourceTree = "<group>"; };
		F76C311E27E301A0007B8CED /* NotificationServiceExtension.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = NotificationServiceExtension.entitlements; sourceTree = "<group>"; };
		F7763B082990A50E00038A27 /* UPPushResource */ = {isa = PBXFileReference; lastKnownFileType = folder; path = UPPushResource; sourceTree = "<group>"; };
		F784FA1D29A852B8007F03FB /* UIAlertView+UPPushEx.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIAlertView+UPPushEx.h"; sourceTree = "<group>"; };
		F784FA1E29A852B8007F03FB /* UIAlertView+UPPushEx.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIAlertView+UPPushEx.m"; sourceTree = "<group>"; };
		F79092E82828BD5300EE61D2 /* UPPushMessageProcessor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushMessageProcessor.h; sourceTree = "<group>"; };
		F79092E92828BD5300EE61D2 /* UPPushMessageProcessor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushMessageProcessor.m; sourceTree = "<group>"; };
		F79092EC2828C23500EE61D2 /* UPPushMessageProcessorFactory.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushMessageProcessorFactory.h; sourceTree = "<group>"; };
		F79092ED2828C23500EE61D2 /* UPPushMessageProcessorFactory.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushMessageProcessorFactory.m; sourceTree = "<group>"; };
		F795132A27E1C8EA00F4B5D6 /* Debugger.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Debugger.entitlements; sourceTree = "<group>"; };
		F7E36A362991117F00D82BF2 /* FakeUPPushSettings.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FakeUPPushSettings.h; sourceTree = "<group>"; };
		F7E36A372991117F00D82BF2 /* FakeUPPushSettings.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FakeUPPushSettings.m; sourceTree = "<group>"; };
		F7F1E3992990FBF100EA3BD8 /* UPPushSettingsProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushSettingsProtocol.h; sourceTree = "<group>"; };
		F7F1E39D2990FC4400EA3BD8 /* UPPushSettings.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPPushSettings.h; sourceTree = "<group>"; };
		F7F1E39E2990FC4400EA3BD8 /* UPPushSettings.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPPushSettings.m; sourceTree = "<group>"; };
		FCC22B519AD6896512D3312C /* Pods-UPPush_abstract_pod-UPPushCore.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPPush_abstract_pod-UPPushCore.release.xcconfig"; path = "Target Support Files/Pods-UPPush_abstract_pod-UPPushCore/Pods-UPPush_abstract_pod-UPPushCore.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		406C014E269BE39700572FCB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40FDF3C12554093300E1B6E5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				F72729FB27D72B3700DBB9C5 /* libUPPushCore.a in Frameworks */,
				FE6251E0E45B513A78D736B9 /* libPods-UPPush_abstract_pod-UPPush.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40FDF3D22554094B00E1B6E5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				A2D541CF5C555641556B18FE /* libPods-UPPush_abstract_pod-UPPushCore.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40FDF3E12554095C00E1B6E5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				F72729FA27D72B2300DBB9C5 /* libUPPush.a in Frameworks */,
				918E76D59DF40634BBDEFD36 /* libPods-UPPush_abstract_pod-UPPushUI.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40FDF3F12554096B00E1B6E5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				F766EBAD27D6FC6200BCE5F6 /* libUPPushServiceExtension.a in Frameworks */,
				401A998D2554276700DA11FA /* libUPPushUI.a in Frameworks */,
				DCD5DD88C8A5254B90E665F3 /* libPods-UPPush_abstract_pod-Debugger.a in Frameworks */,
				2E90A67A9AFED74A5F37D20E /* libPods-UPPush_abstract_pod-Debugger.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40FDF42A255409AA00E1B6E5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				F729C2682935D0460052C67A /* libUPPush.a in Frameworks */,
				9774326DC68172EAFFDACE4F /* libPods-UPPush_abstract_pod-UPPushTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F70AC2BC2834C02B00EFF594 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				F70AC2BD2834C02B00EFF594 /* libUPPushServiceExtension.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F70AC2D62835D11F00EFF594 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				F70AC2D72835D11F00EFF594 /* libUPPushServiceExtension.a in Frameworks */,
				F70AC2D82835D11F00EFF594 /* libUPPushUI.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F72729E827D72AC000DBB9C5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				A58743DD2DF291D5005E7CC8 /* libUPPush.a in Frameworks */,
				F710672D27E70B4D00D2B8CB /* libUPPushServiceExtension.a in Frameworks */,
				EC66299E7A093EB83B5E99A2 /* libPods-NotificationServiceExtension.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F75D020727BF928900259519 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				FC544597A978479576C7F980 /* libPods-UPPushServiceExtension.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		3A60E3E71CAF9BE0C09F90D4 /* Pods */ = {
			isa = PBXGroup;
			children = (
				1065BB6712399C8DC3036771 /* Pods-UPPush_abstract_pod-UPPush.debug.xcconfig */,
				91C15C6DAA9BBFE5DB9C93C8 /* Pods-UPPush_abstract_pod-UPPush.release.xcconfig */,
				E3BAEF2FC30694EF9C0DAE41 /* Pods-UPPush_abstract_pod-UPPushCore.debug.xcconfig */,
				FCC22B519AD6896512D3312C /* Pods-UPPush_abstract_pod-UPPushCore.release.xcconfig */,
				5FC0B6D4784301FCC12830D4 /* Pods-UPPush_abstract_pod-UPPushTests.debug.xcconfig */,
				BCA93657FB2533EF8003BBE6 /* Pods-UPPush_abstract_pod-UPPushTests.release.xcconfig */,
				D05F810056980B3D907C0845 /* Pods-UPPush_abstract_pod-UPPushUI.debug.xcconfig */,
				1712FDBED52C4424B491ABA5 /* Pods-UPPush_abstract_pod-UPPushUI.release.xcconfig */,
				AEC5C2562C560D9753A175D1 /* Pods-UPPush_abstract_pod-Debugger.debug.xcconfig */,
				38DCA9BEDEE63E9AA6A6BBF5 /* Pods-UPPush_abstract_pod-Debugger.release.xcconfig */,
				9E9B57D8BD763292BD85ECA5 /* Pods-NotificationServiceExtension.debug.xcconfig */,
				65B0CE5EC38EE21EA62E134E /* Pods-NotificationServiceExtension.release.xcconfig */,
				71F131A52938A78DEA96DC4E /* Pods-UPPushServiceExtension.debug.xcconfig */,
				B8CEC4896AB2CFF3B7C37020 /* Pods-UPPushServiceExtension.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		400E82982592E4F80062CDC9 /* Process */ = {
			isa = PBXGroup;
			children = (
				400E82D42593173E0062CDC9 /* Queue */,
				400E82D3259317360062CDC9 /* Observe */,
			);
			path = Process;
			sourceTree = "<group>";
		};
		400E82D3259317360062CDC9 /* Observe */ = {
			isa = PBXGroup;
			children = (
				400E82992592E61D0062CDC9 /* UPPushBuildInProcessObserveManager.h */,
				400E829A2592E61D0062CDC9 /* UPPushBuildInProcessObserveManager.m */,
			);
			path = Observe;
			sourceTree = "<group>";
		};
		400E82D42593173E0062CDC9 /* Queue */ = {
			isa = PBXGroup;
			children = (
				400E82D52593176E0062CDC9 /* UPPushBuildInProcessQueueManager.h */,
				400E82D62593176E0062CDC9 /* UPPushBuildInProcessQueueManager.m */,
			);
			path = Queue;
			sourceTree = "<group>";
		};
		401A9A182554EDFE00DA11FA /* Common */ = {
			isa = PBXGroup;
			children = (
				40FDF4FE2554181B00E1B6E5 /* UPPushGlobalDeclaration.h */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		4041A08D255BD32100C0D9B0 /* Alert */ = {
			isa = PBXGroup;
			children = (
				F784FA1C29A852B8007F03FB /* Extension */,
				4041A157255BE1A600C0D9B0 /* Model */,
				4041A08E255BD3E700C0D9B0 /* UPPushAlertController.h */,
				4041A08F255BD3E700C0D9B0 /* UPPushAlertController.m */,
				67C898F52DF9938F002270C4 /* UPPushAlertView.h */,
				67C898F62DF9938F002270C4 /* UPPushAlertView.m */,
			);
			path = Alert;
			sourceTree = "<group>";
		};
		4041A157255BE1A600C0D9B0 /* Model */ = {
			isa = PBXGroup;
			children = (
				4041A14E255BE1A200C0D9B0 /* UPPushAlertModel.h */,
				4041A14F255BE1A200C0D9B0 /* UPPushAlertModel.m */,
				4041A16A255BE27B00C0D9B0 /* UPPushAlertActionModel.h */,
				4041A16B255BE27B00C0D9B0 /* UPPushAlertActionModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		4041A173255BF3A800C0D9B0 /* UPPushAlert */ = {
			isa = PBXGroup;
			children = (
				4041A174255BF3D300C0D9B0 /* UPPushAlertManager.h */,
				4041A175255BF3D300C0D9B0 /* UPPushAlertManager.m */,
			);
			path = UPPushAlert;
			sourceTree = "<group>";
		};
		4041A17D255CD97400C0D9B0 /* Broadcast */ = {
			isa = PBXGroup;
			children = (
				4041A187255CDFC300C0D9B0 /* Model */,
				4041A17E255CDA0800C0D9B0 /* UPPushBroadcastController.h */,
				4041A17F255CDA0800C0D9B0 /* UPPushBroadcastController.m */,
			);
			path = Broadcast;
			sourceTree = "<group>";
		};
		4041A187255CDFC300C0D9B0 /* Model */ = {
			isa = PBXGroup;
			children = (
				4041A188255CE17700C0D9B0 /* UPPushNotification.h */,
				4041A189255CE17700C0D9B0 /* UPPushNotification.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		404EF23E255A95BC0030B1F7 /* Common */ = {
			isa = PBXGroup;
			children = (
				404EF23F255A95C30030B1F7 /* Extension */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		404EF23F255A95C30030B1F7 /* Extension */ = {
			isa = PBXGroup;
			children = (
				404EF249255A97A10030B1F7 /* Message */,
			);
			path = Extension;
			sourceTree = "<group>";
		};
		404EF249255A97A10030B1F7 /* Message */ = {
			isa = PBXGroup;
			children = (
				404EF240255A96420030B1F7 /* UPPushMessage+Completion.h */,
				404EF241255A96420030B1F7 /* UPPushMessage+Completion.m */,
				4041A008255B7C6300C0D9B0 /* UPPushMessage+Tool.h */,
				4041A009255B7C6300C0D9B0 /* UPPushMessage+Tool.m */,
				404EF24A255A97E90030B1F7 /* UPPushMessageExtension.h */,
			);
			path = Message;
			sourceTree = "<group>";
		};
		406C012426929A7900572FCB /* UPPushToast */ = {
			isa = PBXGroup;
			children = (
				406C012526929B2C00572FCB /* UPPushToastManager.h */,
				406C012626929B2C00572FCB /* UPPushToastManager.m */,
			);
			path = UPPushToast;
			sourceTree = "<group>";
		};
		406C012826929CD600572FCB /* Toast */ = {
			isa = PBXGroup;
			children = (
				406C01332692A49200572FCB /* View */,
				406C012926929CE400572FCB /* Model */,
				406C012A26929D0100572FCB /* UPPushToastController.h */,
				406C012B26929D0100572FCB /* UPPushToastController.m */,
			);
			path = Toast;
			sourceTree = "<group>";
		};
		406C012926929CE400572FCB /* Model */ = {
			isa = PBXGroup;
			children = (
				406C012D26929D1600572FCB /* UPPushToastModel.h */,
				406C012E26929D1600572FCB /* UPPushToastModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		406C01332692A49200572FCB /* View */ = {
			isa = PBXGroup;
			children = (
				406C01302692A48C00572FCB /* UPPushToastView.h */,
				406C01312692A48C00572FCB /* UPPushToastView.m */,
				406C015A269BEACD00572FCB /* UPPushToastArrowLabel.h */,
				406C015B269BEACD00572FCB /* UPPushToastArrowLabel.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		40A19E7E256608BF006767C1 /* Common */ = {
			isa = PBXGroup;
			children = (
				40A19E7F256608F3006767C1 /* UPPushStringTools.h */,
				40A19E80256608F3006767C1 /* UPPushStringTools.m */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		40FDF3BB2554093300E1B6E5 = {
			isa = PBXGroup;
			children = (
				A549AABA2DF6D1A000161174 /* UPPushResource_NoSign.xcconfig */,
				40FDF3C62554093300E1B6E5 /* UPPush */,
				40FDF3D62554094B00E1B6E5 /* UPPushCore */,
				40FDF3E52554095C00E1B6E5 /* UPPushUI */,
				F75D020B27BF928A00259519 /* UPPushServiceExtension */,
				F72729EC27D72AC000DBB9C5 /* NotificationServiceExtension */,
				F7763B082990A50E00038A27 /* UPPushResource */,
				40FDF3F52554096B00E1B6E5 /* Debugger */,
				40FDF42E255409AA00E1B6E5 /* UPPushTests */,
				40FDF3C52554093300E1B6E5 /* Products */,
				3A60E3E71CAF9BE0C09F90D4 /* Pods */,
				920F8EA9CDB60FC0ED88B5BF /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		40FDF3C52554093300E1B6E5 /* Products */ = {
			isa = PBXGroup;
			children = (
				40FDF3C42554093300E1B6E5 /* libUPPush.a */,
				40FDF3D52554094B00E1B6E5 /* libUPPushCore.a */,
				40FDF3E42554095C00E1B6E5 /* libUPPushUI.a */,
				40FDF3F42554096B00E1B6E5 /* Debugger.app */,
				40FDF42D255409AA00E1B6E5 /* UPPushTests.xctest */,
				406C0151269BE39700572FCB /* UPPushResource.bundle */,
				F75D020A27BF928900259519 /* libUPPushServiceExtension.a */,
				F72729EB27D72AC000DBB9C5 /* NotificationServiceExtension.appex */,
				F70AC2C32834C02B00EFF594 /* SYNNotificationServiceExtension.appex */,
				F70AC2E72835D11F00EFF594 /* SYNDebugger.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		40FDF3C62554093300E1B6E5 /* UPPush */ = {
			isa = PBXGroup;
			children = (
				F7F1E39C2990FC3400EA3BD8 /* Settings */,
				40FDF45B2554179100E1B6E5 /* Broadcast */,
				40FDF4712554179100E1B6E5 /* Config */,
				40FDF4602554179100E1B6E5 /* DataSource */,
				40FDF4582554179100E1B6E5 /* Manager */,
				40FDF45E2554179100E1B6E5 /* Resource */,
			);
			path = UPPush;
			sourceTree = "<group>";
		};
		40FDF3D62554094B00E1B6E5 /* UPPushCore */ = {
			isa = PBXGroup;
			children = (
				40A19E7E256608BF006767C1 /* Common */,
				40FDF4B72554181B00E1B6E5 /* Implement */,
				40FDF48E2554181B00E1B6E5 /* Protocol */,
			);
			path = UPPushCore;
			sourceTree = "<group>";
		};
		40FDF3E52554095C00E1B6E5 /* UPPushUI */ = {
			isa = PBXGroup;
			children = (
				404EF23E255A95BC0030B1F7 /* Common */,
				40FDF54A255418EC00E1B6E5 /* Interceptor */,
				40FDF52B255418EC00E1B6E5 /* OldLogic */,
				40FDF54E255418EC00E1B6E5 /* Plugin */,
			);
			path = UPPushUI;
			sourceTree = "<group>";
		};
		40FDF3F52554096B00E1B6E5 /* Debugger */ = {
			isa = PBXGroup;
			children = (
				F729C2582932658D0052C67A /* Factor */,
				F795132A27E1C8EA00F4B5D6 /* Debugger.entitlements */,
				40FDF58D2554195200E1B6E5 /* Channel */,
				40FDF57F2554193D00E1B6E5 /* UserDomain */,
				40FDF3F62554096B00E1B6E5 /* AppDelegate.h */,
				40FDF3F72554096B00E1B6E5 /* AppDelegate.m */,
				40FDF3FC2554096B00E1B6E5 /* ViewController.h */,
				40FDF3FD2554096B00E1B6E5 /* ViewController.m */,
				40FDF3FF2554096B00E1B6E5 /* Main.storyboard */,
				40FDF4022554096C00E1B6E5 /* Assets.xcassets */,
				40FDF4042554096C00E1B6E5 /* LaunchScreen.storyboard */,
				40FDF4072554096C00E1B6E5 /* Info.plist */,
				F70AC2E82835D11F00EFF594 /* SYNDebugger-Info.plist */,
				404EF224255A70B40030B1F7 /* GoogleService-Info.plist */,
				40FDF4082554096C00E1B6E5 /* main.m */,
			);
			path = Debugger;
			sourceTree = "<group>";
		};
		40FDF42E255409AA00E1B6E5 /* UPPushTests */ = {
			isa = PBXGroup;
			children = (
				F7437E2C2702F7B20065AB9A /* features */,
				40FDF5C7255419F100E1B6E5 /* CucumberRunner.m */,
				40FDF5B3255419F100E1B6E5 /* Fake */,
				40FDF5C4255419F100E1B6E5 /* Holder */,
				40FDF431255409AA00E1B6E5 /* Info.plist */,
				40FDF5AE255419F100E1B6E5 /* Steps */,
				40FDF5C8255419F100E1B6E5 /* Utils */,
			);
			path = UPPushTests;
			sourceTree = "<group>";
		};
		40FDF4582554179100E1B6E5 /* Manager */ = {
			isa = PBXGroup;
			children = (
				40FDF45A2554179100E1B6E5 /* UPPushManager.h */,
				40FDF4592554179100E1B6E5 /* UPPushManager.m */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		40FDF45B2554179100E1B6E5 /* Broadcast */ = {
			isa = PBXGroup;
			children = (
				40FDF45D2554179100E1B6E5 /* UPPushBroadcastSender.h */,
				40FDF45C2554179100E1B6E5 /* UPPushBroadcastSender.m */,
			);
			path = Broadcast;
			sourceTree = "<group>";
		};
		40FDF45E2554179100E1B6E5 /* Resource */ = {
			isa = PBXGroup;
			children = (
				40FDF45F2554179100E1B6E5 /* Info.plist */,
			);
			path = Resource;
			sourceTree = "<group>";
		};
		40FDF4602554179100E1B6E5 /* DataSource */ = {
			isa = PBXGroup;
			children = (
				40FDF4E72554181B00E1B6E5 /* APIs */,
				40FDF4612554179100E1B6E5 /* Home */,
				40FDF46A2554179100E1B6E5 /* Parser */,
				40FDF46E2554179100E1B6E5 /* Request */,
				40FDF4642554179100E1B6E5 /* Result */,
				40FDF4672554179100E1B6E5 /* Seasia */,
			);
			path = DataSource;
			sourceTree = "<group>";
		};
		40FDF4612554179100E1B6E5 /* Home */ = {
			isa = PBXGroup;
			children = (
				40FDF4622554179100E1B6E5 /* UPPushHomeDataSource.h */,
				40FDF4632554179100E1B6E5 /* UPPushHomeDataSource.m */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		40FDF4642554179100E1B6E5 /* Result */ = {
			isa = PBXGroup;
			children = (
				40FDF4662554179100E1B6E5 /* UPPushResult.h */,
				40FDF4652554179100E1B6E5 /* UPPushResult.m */,
			);
			path = Result;
			sourceTree = "<group>";
		};
		40FDF4672554179100E1B6E5 /* Seasia */ = {
			isa = PBXGroup;
			children = (
				40FDF4682554179100E1B6E5 /* UPPushSeasiaDataSource.h */,
				40FDF4692554179100E1B6E5 /* UPPushSeasiaDataSource.m */,
			);
			path = Seasia;
			sourceTree = "<group>";
		};
		40FDF46A2554179100E1B6E5 /* Parser */ = {
			isa = PBXGroup;
			children = (
				40FDF46B2554179100E1B6E5 /* UPPushResponseParser.h */,
				40FDF46C2554179100E1B6E5 /* UPPushResponseParser.m */,
			);
			path = Parser;
			sourceTree = "<group>";
		};
		40FDF46E2554179100E1B6E5 /* Request */ = {
			isa = PBXGroup;
			children = (
				40FDF46F2554179100E1B6E5 /* UPPushDataSourceRequest.h */,
				40FDF4702554179100E1B6E5 /* UPPushDataSourceRequest.m */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		40FDF4712554179100E1B6E5 /* Config */ = {
			isa = PBXGroup;
			children = (
				40FDF4732554179100E1B6E5 /* UPPushInitializer.h */,
				40FDF4722554179100E1B6E5 /* UPPushInitializer.m */,
			);
			path = Config;
			sourceTree = "<group>";
		};
		40FDF48E2554181B00E1B6E5 /* Protocol */ = {
			isa = PBXGroup;
			children = (
				401A9A182554EDFE00DA11FA /* Common */,
				40FDF48F2554181B00E1B6E5 /* Handler */,
				40FDF4912554181B00E1B6E5 /* DataSource */,
				40FDF4932554181B00E1B6E5 /* Result */,
				40FDF4962554181B00E1B6E5 /* Dispatcher */,
				40FDF4982554181B00E1B6E5 /* Provider */,
				40FDF49A2554181B00E1B6E5 /* Message */,
				40FDF4AB2554181B00E1B6E5 /* Parser */,
				40FDF4AD2554181B00E1B6E5 /* Channel */,
				40FDF4AF2554181B00E1B6E5 /* Push */,
				40FDF4B32554181B00E1B6E5 /* Boardcast */,
				40FDF4B52554181B00E1B6E5 /* Interceptor */,
			);
			path = Protocol;
			sourceTree = "<group>";
		};
		40FDF48F2554181B00E1B6E5 /* Handler */ = {
			isa = PBXGroup;
			children = (
				40FDF4902554181B00E1B6E5 /* UPPushHandlerProtocol.h */,
			);
			path = Handler;
			sourceTree = "<group>";
		};
		40FDF4912554181B00E1B6E5 /* DataSource */ = {
			isa = PBXGroup;
			children = (
				40FDF4922554181B00E1B6E5 /* UPPushDataSourceProtocol.h */,
			);
			path = DataSource;
			sourceTree = "<group>";
		};
		40FDF4932554181B00E1B6E5 /* Result */ = {
			isa = PBXGroup;
			children = (
				40FDF4942554181B00E1B6E5 /* UPPushResultProtocol.h */,
			);
			path = Result;
			sourceTree = "<group>";
		};
		40FDF4962554181B00E1B6E5 /* Dispatcher */ = {
			isa = PBXGroup;
			children = (
				40FDF4972554181B00E1B6E5 /* UPPushDispatcherProtocol.h */,
			);
			path = Dispatcher;
			sourceTree = "<group>";
		};
		40FDF4982554181B00E1B6E5 /* Provider */ = {
			isa = PBXGroup;
			children = (
				40FDF4992554181B00E1B6E5 /* UPPushProviderProtocol.h */,
			);
			path = Provider;
			sourceTree = "<group>";
		};
		40FDF49A2554181B00E1B6E5 /* Message */ = {
			isa = PBXGroup;
			children = (
				40FDF49D2554181B00E1B6E5 /* Body */,
				40FDF49C2554181B00E1B6E5 /* UPPushMessageAPNsInfoProtocol.h */,
				40FDF49B2554181B00E1B6E5 /* UPPushMessageCustomPropertyProtocol.h */,
				40FDF4AA2554181B00E1B6E5 /* UPPushMessageProtocol.h */,
			);
			path = Message;
			sourceTree = "<group>";
		};
		40FDF49D2554181B00E1B6E5 /* Body */ = {
			isa = PBXGroup;
			children = (
				40FDF4A42554181B00E1B6E5 /* Data */,
				40FDF49E2554181B00E1B6E5 /* UI */,
				40FDF4A12554181B00E1B6E5 /* UPPushMessageBodyProtocol.h */,
			);
			path = Body;
			sourceTree = "<group>";
		};
		40FDF49E2554181B00E1B6E5 /* UI */ = {
			isa = PBXGroup;
			children = (
				40FDF49F2554181B00E1B6E5 /* UPPushMessageUIButtonProtocol.h */,
				40FDF4A02554181B00E1B6E5 /* UPPushMessageUIProtocol.h */,
			);
			path = UI;
			sourceTree = "<group>";
		};
		40FDF4A42554181B00E1B6E5 /* Data */ = {
			isa = PBXGroup;
			children = (
				40FDF4A82554181B00E1B6E5 /* UPPushMessageAPIProtocol.h */,
				40FDF4A52554181B00E1B6E5 /* UPPushMessageDeviceControlProtocol.h */,
				40FDF4A62554181B00E1B6E5 /* UPPushMessageDeviceProtocol.h */,
				40FDF4A72554181B00E1B6E5 /* UPPushMessageExtraDataProtocol.h */,
				40FDF4A92554181B00E1B6E5 /* UPPushMessagePageProtocol.h */,
			);
			path = Data;
			sourceTree = "<group>";
		};
		40FDF4AB2554181B00E1B6E5 /* Parser */ = {
			isa = PBXGroup;
			children = (
				40FDF4AC2554181B00E1B6E5 /* UPPushParserProtocol.h */,
			);
			path = Parser;
			sourceTree = "<group>";
		};
		40FDF4AD2554181B00E1B6E5 /* Channel */ = {
			isa = PBXGroup;
			children = (
				40FDF4AE2554181B00E1B6E5 /* UPPushChannelProtocol.h */,
			);
			path = Channel;
			sourceTree = "<group>";
		};
		40FDF4AF2554181B00E1B6E5 /* Push */ = {
			isa = PBXGroup;
			children = (
				40FDF4B02554181B00E1B6E5 /* UPPushContextProtocol.h */,
				40FDF4B12554181B00E1B6E5 /* UPPushInitializerProtocol.h */,
				40FDF4B22554181B00E1B6E5 /* UPPushProtocol.h */,
				F7F1E3992990FBF100EA3BD8 /* UPPushSettingsProtocol.h */,
			);
			path = Push;
			sourceTree = "<group>";
		};
		40FDF4B32554181B00E1B6E5 /* Boardcast */ = {
			isa = PBXGroup;
			children = (
				40FDF4B42554181B00E1B6E5 /* UPPushBroadcastSenderProtocol.h */,
			);
			path = Boardcast;
			sourceTree = "<group>";
		};
		40FDF4B52554181B00E1B6E5 /* Interceptor */ = {
			isa = PBXGroup;
			children = (
				40FDF4B62554181B00E1B6E5 /* UPPushInterceptorProtocol.h */,
				403032D925591E5B0092915F /* UPPushInterceptorManagerProtocol.h */,
			);
			path = Interceptor;
			sourceTree = "<group>";
		};
		40FDF4B72554181B00E1B6E5 /* Implement */ = {
			isa = PBXGroup;
			children = (
				40FDF4E42554181B00E1B6E5 /* Badge */,
				40FDF4B82554181B00E1B6E5 /* Util */,
				40FDF4BB2554181B00E1B6E5 /* Dispatch */,
				40FDF4C02554181B00E1B6E5 /* Message */,
				40FDF4DB2554181B00E1B6E5 /* Parser */,
				40FDF4DE2554181B00E1B6E5 /* UPPush */,
				40FDF4E12554181B00E1B6E5 /* Interceptor */,
			);
			path = Implement;
			sourceTree = "<group>";
		};
		40FDF4B82554181B00E1B6E5 /* Util */ = {
			isa = PBXGroup;
			children = (
				40FDF4BA2554181B00E1B6E5 /* UPPushRetryAssistant.h */,
				40FDF4B92554181B00E1B6E5 /* UPPushRetryAssistant.m */,
			);
			path = Util;
			sourceTree = "<group>";
		};
		40FDF4BB2554181B00E1B6E5 /* Dispatch */ = {
			isa = PBXGroup;
			children = (
				40FDF4BC2554181B00E1B6E5 /* UPPushDispatcher.h */,
				40FDF4BD2554181B00E1B6E5 /* UPPushDispatcher.m */,
			);
			path = Dispatch;
			sourceTree = "<group>";
		};
		40FDF4C02554181B00E1B6E5 /* Message */ = {
			isa = PBXGroup;
			children = (
				40FDF4C32554181B00E1B6E5 /* Body */,
				40FDF4D92554181B00E1B6E5 /* UPPushMessage.h */,
				40FDF4C12554181B00E1B6E5 /* UPPushMessage.m */,
				40FDF4DA2554181B00E1B6E5 /* UPPushOriginalMessage.h */,
				40FDF4C22554181B00E1B6E5 /* UPPushOriginalMessage.m */,
			);
			path = Message;
			sourceTree = "<group>";
		};
		40FDF4C32554181B00E1B6E5 /* Body */ = {
			isa = PBXGroup;
			children = (
				40FDF4CE2554181B00E1B6E5 /* Data */,
				40FDF4C42554181B00E1B6E5 /* UI */,
				40FDF4CD2554181B00E1B6E5 /* UPPushMessageBody.h */,
				40FDF4C92554181B00E1B6E5 /* UPPushMessageBody.m */,
			);
			path = Body;
			sourceTree = "<group>";
		};
		40FDF4C42554181B00E1B6E5 /* UI */ = {
			isa = PBXGroup;
			children = (
				40FDF4C82554181B00E1B6E5 /* UPPushMessageUI.h */,
				40FDF4C62554181B00E1B6E5 /* UPPushMessageUI.m */,
				40FDF4C72554181B00E1B6E5 /* UPPushMessageUIButton.h */,
				40FDF4C52554181B00E1B6E5 /* UPPushMessageUIButton.m */,
			);
			path = UI;
			sourceTree = "<group>";
		};
		40FDF4CE2554181B00E1B6E5 /* Data */ = {
			isa = PBXGroup;
			children = (
				40FDF4CF2554181B00E1B6E5 /* UPPushMessageAPI.h */,
				40FDF4D62554181B00E1B6E5 /* UPPushMessageAPI.m */,
				40FDF4D42554181B00E1B6E5 /* UPPushMessageDevice.h */,
				40FDF4D12554181B00E1B6E5 /* UPPushMessageDevice.m */,
				40FDF4D72554181B00E1B6E5 /* UPPushMessageDeviceControl.h */,
				40FDF4D32554181B00E1B6E5 /* UPPushMessageDeviceControl.m */,
				40FDF4D82554181B00E1B6E5 /* UPPushMessageExtraData.h */,
				40FDF4D22554181B00E1B6E5 /* UPPushMessageExtraData.m */,
				40FDF4D52554181B00E1B6E5 /* UPPushMessagePage.h */,
				40FDF4D02554181B00E1B6E5 /* UPPushMessagePage.m */,
			);
			path = Data;
			sourceTree = "<group>";
		};
		40FDF4DB2554181B00E1B6E5 /* Parser */ = {
			isa = PBXGroup;
			children = (
				40FDF4DD2554181B00E1B6E5 /* UPPushParser.h */,
				40FDF4DC2554181B00E1B6E5 /* UPPushParser.m */,
			);
			path = Parser;
			sourceTree = "<group>";
		};
		40FDF4DE2554181B00E1B6E5 /* UPPush */ = {
			isa = PBXGroup;
			children = (
				40FDF4DF2554181B00E1B6E5 /* UPPush.h */,
				40FDF4E02554181B00E1B6E5 /* UPPush.m */,
			);
			path = UPPush;
			sourceTree = "<group>";
		};
		40FDF4E12554181B00E1B6E5 /* Interceptor */ = {
			isa = PBXGroup;
			children = (
				40FDF4E22554181B00E1B6E5 /* UPPushInterceptorManager.h */,
				40FDF4E32554181B00E1B6E5 /* UPPushInterceptorManager.m */,
			);
			path = Interceptor;
			sourceTree = "<group>";
		};
		40FDF4E42554181B00E1B6E5 /* Badge */ = {
			isa = PBXGroup;
			children = (
				40FDF4E62554181B00E1B6E5 /* UPPushBadgeManager.h */,
				40FDF4E52554181B00E1B6E5 /* UPPushBadgeManager.m */,
			);
			path = Badge;
			sourceTree = "<group>";
		};
		40FDF4E72554181B00E1B6E5 /* APIs */ = {
			isa = PBXGroup;
			children = (
				40FDF4FF2554181B00E1B6E5 /* Common */,
				40FDF4E82554181B00E1B6E5 /* Home */,
				40FDF4F32554181B00E1B6E5 /* Seasia */,
			);
			path = APIs;
			sourceTree = "<group>";
		};
		40FDF4E82554181B00E1B6E5 /* Home */ = {
			isa = PBXGroup;
			children = (
				40FDF4E92554181B00E1B6E5 /* Push */,
				40FDF4F02554181B00E1B6E5 /* Common */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		40FDF4E92554181B00E1B6E5 /* Push */ = {
			isa = PBXGroup;
			children = (
				40FDF4EB2554181B00E1B6E5 /* UPPushRegisterAPI.h */,
				40FDF4EE2554181B00E1B6E5 /* UPPushRegisterAPI.m */,
				40FDF4ED2554181B00E1B6E5 /* UPPushReportStatusAPI.h */,
				40FDF4EA2554181B00E1B6E5 /* UPPushReportStatusAPI.m */,
				40FDF4EC2554181B00E1B6E5 /* UPPushUnregisterAPI.h */,
				40FDF4EF2554181B00E1B6E5 /* UPPushUnregisterAPI.m */,
				5E857ABC2AD682F400093015 /* UPPushNotDisturbAPI.h */,
				5E857ABD2AD682F400093015 /* UPPushNotDisturbAPI.m */,
			);
			path = Push;
			sourceTree = "<group>";
		};
		40FDF4F02554181B00E1B6E5 /* Common */ = {
			isa = PBXGroup;
			children = (
				40FDF4F22554181B00E1B6E5 /* UPPushRequestBase.h */,
				40FDF4F12554181B00E1B6E5 /* UPPushRequestBase.m */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		40FDF4F32554181B00E1B6E5 /* Seasia */ = {
			isa = PBXGroup;
			children = (
				40FDF4F42554181B00E1B6E5 /* Push */,
				40FDF4FB2554181B00E1B6E5 /* Common */,
			);
			path = Seasia;
			sourceTree = "<group>";
		};
		40FDF4F42554181B00E1B6E5 /* Push */ = {
			isa = PBXGroup;
			children = (
				40FDF4FA2554181B00E1B6E5 /* UPPushSERegisterAPI.h */,
				40FDF4F72554181B00E1B6E5 /* UPPushSERegisterAPI.m */,
				40FDF4F92554181B00E1B6E5 /* UPPushSEReportStatusAPI.h */,
				40FDF4F52554181B00E1B6E5 /* UPPushSEReportStatusAPI.m */,
				40FDF4F82554181B00E1B6E5 /* UPPushSEUnregisterAPI.h */,
				40FDF4F62554181B00E1B6E5 /* UPPushSEUnregisterAPI.m */,
			);
			path = Push;
			sourceTree = "<group>";
		};
		40FDF4FB2554181B00E1B6E5 /* Common */ = {
			isa = PBXGroup;
			children = (
				40FDF4FC2554181B00E1B6E5 /* UPPushSERequestBase.h */,
				40FDF4FD2554181B00E1B6E5 /* UPPushSERequestBase.m */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		40FDF4FF2554181B00E1B6E5 /* Common */ = {
			isa = PBXGroup;
			children = (
				F729C251292F13900052C67A /* UPPushAbilityTools.h */,
				F729C252292F13900052C67A /* UPPushAbilityTools.m */,
				40FDF5022554181B00E1B6E5 /* UPPushAPIUtil.h */,
				40FDF5012554181B00E1B6E5 /* UPPushAPIUtil.m */,
				40FDF5032554181B00E1B6E5 /* UPPushEncryption.h */,
				40FDF5002554181B00E1B6E5 /* UPPushEncryption.m */,
				F729C255292F17C70052C67A /* UPPushDataSourceDeclaration.h */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		40FDF52B255418EC00E1B6E5 /* OldLogic */ = {
			isa = PBXGroup;
			children = (
				F79092EB2828C13A00EE61D2 /* UPPushMessageProcessor */,
				40FDF52D255418EC00E1B6E5 /* Track */,
				40FDF52C255418EC00E1B6E5 /* UPPushApiDataHandler.h */,
				40FDF53D255418EC00E1B6E5 /* UPPushApiDataHandler.m */,
				40FDF53E255418EC00E1B6E5 /* UPPushDevControlDataHandler.h */,
				40FDF533255418EC00E1B6E5 /* UPPushDevControlDataHandler.m */,
				40FDF549255418EC00E1B6E5 /* UPPushHandlerTool.h */,
				40FDF53B255418EC00E1B6E5 /* UPPushHandlerTool.m */,
				84C17FF52D2BC68100932C86 /* UPPushLocalizationConfig.h */,
				84C17FF42D2BC68100932C86 /* UPPushLocalizationConfig.m */,
				40FDF536255418EC00E1B6E5 /* UPPushLogoutObserver.h */,
				40FDF544255418EC00E1B6E5 /* UPPushLogoutObserver.m */,
				40FDF53A255418EC00E1B6E5 /* UPPushNoViewMsgComponent.h */,
				40FDF541255418EC00E1B6E5 /* UPPushNoViewMsgComponent.m */,
				40FDF537255418EC00E1B6E5 /* UPPushPageDataHandler.h */,
				40FDF543255418EC00E1B6E5 /* UPPushPageDataHandler.m */,
				40FDF535255418EC00E1B6E5 /* UPPushViewMsgComponent.h */,
				40FDF547255418EC00E1B6E5 /* UPPushViewMsgComponent.m */,
			);
			path = OldLogic;
			sourceTree = "<group>";
		};
		40FDF52D255418EC00E1B6E5 /* Track */ = {
			isa = PBXGroup;
			children = (
				40FDF52F255418EC00E1B6E5 /* UPPushGIOTrack.h */,
				40FDF52E255418EC00E1B6E5 /* UPPushGIOTrack.m */,
			);
			path = Track;
			sourceTree = "<group>";
		};
		40FDF54A255418EC00E1B6E5 /* Interceptor */ = {
			isa = PBXGroup;
			children = (
				406C012426929A7900572FCB /* UPPushToast */,
				400E82982592E4F80062CDC9 /* Process */,
				4041A173255BF3A800C0D9B0 /* UPPushAlert */,
				40FDF54B255418EC00E1B6E5 /* UPPushBuildInUIInterceptor.h */,
				40FDF54C255418EC00E1B6E5 /* UPPushBuildInUIInterceptor.m */,
			);
			path = Interceptor;
			sourceTree = "<group>";
		};
		40FDF54E255418EC00E1B6E5 /* Plugin */ = {
			isa = PBXGroup;
			children = (
				40FDF557255418EC00E1B6E5 /* Common */,
				406C012826929CD600572FCB /* Toast */,
				4041A17D255CD97400C0D9B0 /* Broadcast */,
				4041A08D255BD32100C0D9B0 /* Alert */,
				40FDF54F255418EC00E1B6E5 /* AppManager */,
				40FDF553255418EC00E1B6E5 /* Progress */,
			);
			path = Plugin;
			sourceTree = "<group>";
		};
		40FDF54F255418EC00E1B6E5 /* AppManager */ = {
			isa = PBXGroup;
			children = (
				40FDF551255418EC00E1B6E5 /* UPPushAppManager.h */,
				40FDF550255418EC00E1B6E5 /* UPPushAppManager.m */,
				40FDF552255418EC00E1B6E5 /* UPPushAppManagerProtocol.h */,
			);
			path = AppManager;
			sourceTree = "<group>";
		};
		40FDF553255418EC00E1B6E5 /* Progress */ = {
			isa = PBXGroup;
			children = (
				40FDF55A255418EC00E1B6E5 /* Categories */,
				40FDF556255418EC00E1B6E5 /* UPPushLoading.h */,
				40FDF560255418EC00E1B6E5 /* UPPushLoading.m */,
				40FDF559255418EC00E1B6E5 /* UPPushProgressShow.h */,
				40FDF554255418EC00E1B6E5 /* UPPushProgressShow.m */,
				40FDF55F255418EC00E1B6E5 /* UPPushToast.h */,
				40FDF555255418EC00E1B6E5 /* UPPushToast.m */,
			);
			path = Progress;
			sourceTree = "<group>";
		};
		40FDF557255418EC00E1B6E5 /* Common */ = {
			isa = PBXGroup;
			children = (
				40FDF558255418EC00E1B6E5 /* UPPushMacros.h */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		40FDF55A255418EC00E1B6E5 /* Categories */ = {
			isa = PBXGroup;
			children = (
				40FDF55B255418EC00E1B6E5 /* UIImage+UPPushImageEffects.m */,
				40FDF55C255418EC00E1B6E5 /* UIImage+UPPushEmpty.m */,
				40FDF55D255418EC00E1B6E5 /* UIImage+UPPushImageEffects.h */,
				40FDF55E255418EC00E1B6E5 /* UIImage+UPPushEmpty.h */,
			);
			path = Categories;
			sourceTree = "<group>";
		};
		40FDF57F2554193D00E1B6E5 /* UserDomain */ = {
			isa = PBXGroup;
			children = (
				40FDF5812554193D00E1B6E5 /* SEUserLoginApi.h */,
				40FDF5832554193D00E1B6E5 /* SEUserLoginApi.m */,
				40FDF5802554193D00E1B6E5 /* UPUserLoginApi.h */,
				40FDF5822554193D00E1B6E5 /* UPUserLoginApi.m */,
			);
			path = UserDomain;
			sourceTree = "<group>";
		};
		40FDF58D2554195200E1B6E5 /* Channel */ = {
			isa = PBXGroup;
			children = (
				40FDF58F2554196100E1B6E5 /* Seasia */,
				40FDF58E2554195900E1B6E5 /* Home */,
			);
			path = Channel;
			sourceTree = "<group>";
		};
		40FDF58E2554195900E1B6E5 /* Home */ = {
			isa = PBXGroup;
			children = (
				40FDF5902554199300E1B6E5 /* UPJPushChannel.h */,
				40FDF5912554199300E1B6E5 /* UPJPushChannel.m */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		40FDF58F2554196100E1B6E5 /* Seasia */ = {
			isa = PBXGroup;
			children = (
				40FDF59A2554199D00E1B6E5 /* UPFirebaseChannel.h */,
				40FDF5992554199D00E1B6E5 /* UPFirebaseChannel.m */,
			);
			path = Seasia;
			sourceTree = "<group>";
		};
		40FDF5AE255419F100E1B6E5 /* Steps */ = {
			isa = PBXGroup;
			children = (
				40FDF5B0255419F100E1B6E5 /* InitializationSteps.h */,
				40FDF5B1255419F100E1B6E5 /* InitializationSteps.m */,
				40FDF5AF255419F100E1B6E5 /* PushSteps.h */,
				40FDF5B2255419F100E1B6E5 /* PushSteps.m */,
				4041A23225621D1000C0D9B0 /* MessageSteps.h */,
				4041A23325621D1000C0D9B0 /* MessageSteps.m */,
			);
			path = Steps;
			sourceTree = "<group>";
		};
		40FDF5B3255419F100E1B6E5 /* Fake */ = {
			isa = PBXGroup;
			children = (
				40FDF5C1255419F100E1B6E5 /* FakeUPPushBase.h */,
				40FDF5BA255419F100E1B6E5 /* FakeUPPushBase.m */,
				40FDF5B5255419F100E1B6E5 /* FakeUPPushBroadcastSender.h */,
				40FDF5BC255419F100E1B6E5 /* FakeUPPushBroadcastSender.m */,
				40FDF5B4255419F100E1B6E5 /* FakeUPPushChannel.h */,
				40FDF5BD255419F100E1B6E5 /* FakeUPPushChannel.m */,
				40FDF5BE255419F100E1B6E5 /* FakeUPPushInitializer.h */,
				40FDF5B6255419F100E1B6E5 /* FakeUPPushInitializer.m */,
				40FDF5B8255419F100E1B6E5 /* FakeUPPushDataSource.h */,
				40FDF5C3255419F100E1B6E5 /* FakeUPPushDataSource.m */,
				40FDF5C0255419F100E1B6E5 /* FakeUPPushInterceptor.h */,
				40FDF5BB255419F100E1B6E5 /* FakeUPPushInterceptor.m */,
				40FDF5B7255419F100E1B6E5 /* FakeUPPushProvider.h */,
				40FDF5BF255419F100E1B6E5 /* FakeUPPushProvider.m */,
				40FDF5C2255419F100E1B6E5 /* FakeUPPushResult.h */,
				40FDF5B9255419F100E1B6E5 /* FakeUPPushResult.m */,
				4041A25A25622B2600C0D9B0 /* FakeUPPushAllInterceptor.h */,
				4041A25B25622B2600C0D9B0 /* FakeUPPushAllInterceptor.m */,
				F7E36A362991117F00D82BF2 /* FakeUPPushSettings.h */,
				F7E36A372991117F00D82BF2 /* FakeUPPushSettings.m */,
			);
			path = Fake;
			sourceTree = "<group>";
		};
		40FDF5C4255419F100E1B6E5 /* Holder */ = {
			isa = PBXGroup;
			children = (
				40FDF5C5255419F100E1B6E5 /* FakeUPPushManager.h */,
				40FDF5C6255419F100E1B6E5 /* FakeUPPushManager.m */,
			);
			path = Holder;
			sourceTree = "<group>";
		};
		40FDF5C8255419F100E1B6E5 /* Utils */ = {
			isa = PBXGroup;
			children = (
				40FDF5CA255419F100E1B6E5 /* StepsUtils.h */,
				40FDF5C9255419F100E1B6E5 /* StepsUtils.m */,
				4041A3E925636B8E00C0D9B0 /* TestInterceptorCache.h */,
				4041A3EA25636B8E00C0D9B0 /* TestInterceptorCache.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		920F8EA9CDB60FC0ED88B5BF /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				DF8113219FCB10B55F40E2F5 /* libPods-UPPush_abstract_pod-Debugger.a */,
				D3D8518C38515550A0CD1217 /* libPods-UPPush_abstract_pod-UPPush.a */,
				ECAB625BDF2DEDF64A9287D0 /* libPods-UPPush_abstract_pod-UPPushCore.a */,
				9069DC4A838E7273BCC13DD3 /* libPods-UPPush_abstract_pod-UPPushTests.a */,
				ECA8E9DB0BC322A63A18AE1F /* libPods-UPPush_abstract_pod-UPPushUI.a */,
				5A49963B6C247668EEBE6D8A /* libPods-NotificationServiceExtension.a */,
				0F2608C6FA3E587222EB790B /* libPods-UPPushServiceExtension.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A58743CF2DF29128005E7CC8 /* API */ = {
			isa = PBXGroup;
			children = (
				A58743CD2DF29128005E7CC8 /* UPPushGetIconAPI.h */,
				A58743CE2DF29128005E7CC8 /* UPPushGetIconAPI.m */,
			);
			path = API;
			sourceTree = "<group>";
		};
		A58743D22DF29128005E7CC8 /* Model */ = {
			isa = PBXGroup;
			children = (
				A58743D02DF29128005E7CC8 /* UPPushGetIconResultModel.h */,
				A58743D12DF29128005E7CC8 /* UPPushGetIconResultModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		A58743D52DF29128005E7CC8 /* Request */ = {
			isa = PBXGroup;
			children = (
				A58743D32DF29128005E7CC8 /* UPPushGetIconRequestManager.h */,
				A58743D42DF29128005E7CC8 /* UPPushGetIconRequestManager.m */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		A58743D82DF29128005E7CC8 /* ImageTool */ = {
			isa = PBXGroup;
			children = (
				A58743CF2DF29128005E7CC8 /* API */,
				A58743D22DF29128005E7CC8 /* Model */,
				A58743D52DF29128005E7CC8 /* Request */,
				A58743D62DF29128005E7CC8 /* UPPushGetIconRequestTool.h */,
				A58743D72DF29128005E7CC8 /* UPPushGetIconRequestTool.m */,
			);
			path = ImageTool;
			sourceTree = "<group>";
		};
		F708C19327C35F3200B1CAC4 /* Protocol */ = {
			isa = PBXGroup;
			children = (
				F708C19427C35F5100B1CAC4 /* UPPushServiceExtensionProtocol.h */,
			);
			path = Protocol;
			sourceTree = "<group>";
		};
		F708C1AB27C3684C00B1CAC4 /* Download */ = {
			isa = PBXGroup;
			children = (
				F708C1AE27C36D3300B1CAC4 /* UPPushDownloadManager.h */,
				F708C1AF27C36D3300B1CAC4 /* UPPushDownloadManager.m */,
			);
			path = Download;
			sourceTree = "<group>";
		};
		F708C1AD27C3689C00B1CAC4 /* Implement */ = {
			isa = PBXGroup;
			children = (
				F72729FF27D72F0200DBB9C5 /* Defines */,
				F740E72527C4EAB900CD5C3D /* Persistence */,
				F708C1AB27C3684C00B1CAC4 /* Download */,
				F75D020C27BF928A00259519 /* UPPushServiceExtension.h */,
				F75D020D27BF928A00259519 /* UPPushServiceExtension.m */,
			);
			path = Implement;
			sourceTree = "<group>";
		};
		F72729EC27D72AC000DBB9C5 /* NotificationServiceExtension */ = {
			isa = PBXGroup;
			children = (
				F76C311E27E301A0007B8CED /* NotificationServiceExtension.entitlements */,
				F7020AD927E0A24300A1C78D /* NotificationServiceExtensionDebug.entitlements */,
				F72729ED27D72AC000DBB9C5 /* NotificationService.h */,
				F72729EE27D72AC000DBB9C5 /* NotificationService.m */,
				F70AC2C42834C02B00EFF594 /* SYNNotificationServiceExtension-Info.plist */,
				F72729F027D72AC000DBB9C5 /* Info.plist */,
			);
			path = NotificationServiceExtension;
			sourceTree = "<group>";
		};
		F72729FF27D72F0200DBB9C5 /* Defines */ = {
			isa = PBXGroup;
			children = (
				F7272A0027D72F1000DBB9C5 /* UPPushSEDeclaration.h */,
				F7272A0127D72F1000DBB9C5 /* UPPushSEDeclaration.m */,
			);
			path = Defines;
			sourceTree = "<group>";
		};
		F729C2582932658D0052C67A /* Factor */ = {
			isa = PBXGroup;
			children = (
				F729C259293266060052C67A /* UPPushFactor.h */,
				F729C25A293266060052C67A /* UPPushFactor.m */,
				F729C25C2932666A0052C67A /* UPPushRegisterFactor.h */,
				F729C25D2932666A0052C67A /* UPPushRegisterFactor.m */,
			);
			path = Factor;
			sourceTree = "<group>";
		};
		F740E72527C4EAB900CD5C3D /* Persistence */ = {
			isa = PBXGroup;
			children = (
				F740E72627C4EAF700CD5C3D /* UPPushPersistenceManager.h */,
				F740E72727C4EAF700CD5C3D /* UPPushPersistenceManager.m */,
			);
			path = Persistence;
			sourceTree = "<group>";
		};
		F75D020B27BF928A00259519 /* UPPushServiceExtension */ = {
			isa = PBXGroup;
			children = (
				A58743D82DF29128005E7CC8 /* ImageTool */,
				F708C19327C35F3200B1CAC4 /* Protocol */,
				F708C1AD27C3689C00B1CAC4 /* Implement */,
			);
			path = UPPushServiceExtension;
			sourceTree = "<group>";
		};
		F784FA1C29A852B8007F03FB /* Extension */ = {
			isa = PBXGroup;
			children = (
				F784FA1D29A852B8007F03FB /* UIAlertView+UPPushEx.h */,
				F784FA1E29A852B8007F03FB /* UIAlertView+UPPushEx.m */,
			);
			path = Extension;
			sourceTree = "<group>";
		};
		F79092EB2828C13A00EE61D2 /* UPPushMessageProcessor */ = {
			isa = PBXGroup;
			children = (
				40FDF546255418EC00E1B6E5 /* UPPushCustomizedMessageProcessor.h */,
				40FDF534255418EC00E1B6E5 /* UPPushCustomizedMessageProcessor.m */,
				40FDF545255418EC00E1B6E5 /* UPPushNotificationMessageProcessor.h */,
				40FDF538255418EC00E1B6E5 /* UPPushNotificationMessageProcessor.m */,
				F79092E82828BD5300EE61D2 /* UPPushMessageProcessor.h */,
				F79092E92828BD5300EE61D2 /* UPPushMessageProcessor.m */,
				F79092EC2828C23500EE61D2 /* UPPushMessageProcessorFactory.h */,
				F79092ED2828C23500EE61D2 /* UPPushMessageProcessorFactory.m */,
			);
			path = UPPushMessageProcessor;
			sourceTree = "<group>";
		};
		F7F1E39C2990FC3400EA3BD8 /* Settings */ = {
			isa = PBXGroup;
			children = (
				F7F1E39D2990FC4400EA3BD8 /* UPPushSettings.h */,
				F7F1E39E2990FC4400EA3BD8 /* UPPushSettings.m */,
			);
			path = Settings;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		406C0150269BE39700572FCB /* UPPushResource */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 406C0156269BE39700572FCB /* Build configuration list for PBXNativeTarget "UPPushResource" */;
			buildPhases = (
				406C014D269BE39700572FCB /* Sources */,
				406C014E269BE39700572FCB /* Frameworks */,
				406C014F269BE39700572FCB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UPPushResource;
			productName = UPPushResource;
			productReference = 406C0151269BE39700572FCB /* UPPushResource.bundle */;
			productType = "com.apple.product-type.bundle";
		};
		40FDF3C32554093300E1B6E5 /* UPPush */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 40FDF3CD2554093300E1B6E5 /* Build configuration list for PBXNativeTarget "UPPush" */;
			buildPhases = (
				5910D6855390C2315BE6116A /* [CP] Check Pods Manifest.lock */,
				40FDF3C02554093300E1B6E5 /* Sources */,
				40FDF3C12554093300E1B6E5 /* Frameworks */,
				40FDF3C22554093300E1B6E5 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
				401A98F025541F5800DA11FA /* PBXTargetDependency */,
			);
			name = UPPush;
			productName = UPPush;
			productReference = 40FDF3C42554093300E1B6E5 /* libUPPush.a */;
			productType = "com.apple.product-type.library.static";
		};
		40FDF3D42554094B00E1B6E5 /* UPPushCore */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 40FDF3DB2554094B00E1B6E5 /* Build configuration list for PBXNativeTarget "UPPushCore" */;
			buildPhases = (
				0D6FF6EBE42F4482CE4D8834 /* [CP] Check Pods Manifest.lock */,
				40FDF3D12554094B00E1B6E5 /* Sources */,
				40FDF3D22554094B00E1B6E5 /* Frameworks */,
				40FDF3D32554094B00E1B6E5 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UPPushCore;
			productName = UPPushCore;
			productReference = 40FDF3D52554094B00E1B6E5 /* libUPPushCore.a */;
			productType = "com.apple.product-type.library.static";
		};
		40FDF3E32554095C00E1B6E5 /* UPPushUI */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 40FDF3EA2554095C00E1B6E5 /* Build configuration list for PBXNativeTarget "UPPushUI" */;
			buildPhases = (
				E0D68110AD105A0826A2EDC7 /* [CP] Check Pods Manifest.lock */,
				40FDF3E02554095C00E1B6E5 /* Sources */,
				40FDF3E12554095C00E1B6E5 /* Frameworks */,
				40FDF3E22554095C00E1B6E5 /* Copy Files */,
			);
			buildRules = (
			);
			dependencies = (
				F766EBB427D7225100BCE5F6 /* PBXTargetDependency */,
			);
			name = UPPushUI;
			productName = UPPushUI;
			productReference = 40FDF3E42554095C00E1B6E5 /* libUPPushUI.a */;
			productType = "com.apple.product-type.library.static";
		};
		40FDF3F32554096B00E1B6E5 /* Debugger */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 40FDF40A2554096C00E1B6E5 /* Build configuration list for PBXNativeTarget "Debugger" */;
			buildPhases = (
				AC588D630A0945BBAB307C44 /* [CP] Check Pods Manifest.lock */,
				40FDF3F02554096B00E1B6E5 /* Sources */,
				40FDF3F12554096B00E1B6E5 /* Frameworks */,
				40FDF3F22554096B00E1B6E5 /* Resources */,
				39032AD62C04877A956B9D19 /* [CP] Copy Pods Resources */,
				F708C1A727C35FFE00B1CAC4 /* Embed App Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				406C015F269C1AF300572FCB /* PBXTargetDependency */,
				401A98F825541F6600DA11FA /* PBXTargetDependency */,
				F72729F227D72AC100DBB9C5 /* PBXTargetDependency */,
			);
			name = Debugger;
			productName = Debugger;
			productReference = 40FDF3F42554096B00E1B6E5 /* Debugger.app */;
			productType = "com.apple.product-type.application";
		};
		40FDF42C255409AA00E1B6E5 /* UPPushTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 40FDF435255409AA00E1B6E5 /* Build configuration list for PBXNativeTarget "UPPushTests" */;
			buildPhases = (
				55214EB27DA20F5920B29F57 /* [CP] Check Pods Manifest.lock */,
				40FDF429255409AA00E1B6E5 /* Sources */,
				40FDF42A255409AA00E1B6E5 /* Frameworks */,
				40FDF42B255409AA00E1B6E5 /* Resources */,
				34DFA1B9EA2DE64AEBE82409 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F729C2672935D0400052C67A /* PBXTargetDependency */,
			);
			name = UPPushTests;
			productName = UPPushTests;
			productReference = 40FDF42D255409AA00E1B6E5 /* UPPushTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		F70AC2B72834C02B00EFF594 /* SYNNotificationServiceExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F70AC2C02834C02B00EFF594 /* Build configuration list for PBXNativeTarget "SYNNotificationServiceExtension" */;
			buildPhases = (
				F70AC2BA2834C02B00EFF594 /* Sources */,
				F70AC2BC2834C02B00EFF594 /* Frameworks */,
				F70AC2BE2834C02B00EFF594 /* Resources */,
				F70AC2BF2834C02B00EFF594 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
				F70AC2B82834C02B00EFF594 /* PBXTargetDependency */,
			);
			name = SYNNotificationServiceExtension;
			productName = NotificationServiceExtension;
			productReference = F70AC2C32834C02B00EFF594 /* SYNNotificationServiceExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		F70AC2C62835D11F00EFF594 /* SYNDebugger */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F70AC2E42835D11F00EFF594 /* Build configuration list for PBXNativeTarget "SYNDebugger" */;
			buildPhases = (
				F70AC2CE2835D11F00EFF594 /* Sources */,
				F70AC2D62835D11F00EFF594 /* Frameworks */,
				F70AC2DA2835D11F00EFF594 /* Resources */,
				F70AC2E12835D11F00EFF594 /* Embed App Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				F7EA35D4283B6DE7006A77B3 /* PBXTargetDependency */,
				F70AC2C72835D11F00EFF594 /* PBXTargetDependency */,
				F70AC2C92835D11F00EFF594 /* PBXTargetDependency */,
			);
			name = SYNDebugger;
			productName = Debugger;
			productReference = F70AC2E72835D11F00EFF594 /* SYNDebugger.app */;
			productType = "com.apple.product-type.application";
		};
		F72729EA27D72AC000DBB9C5 /* NotificationServiceExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F72729F427D72AC100DBB9C5 /* Build configuration list for PBXNativeTarget "NotificationServiceExtension" */;
			buildPhases = (
				2C46D99F94E97BBAED8D764D /* [CP] Check Pods Manifest.lock */,
				F72729E727D72AC000DBB9C5 /* Sources */,
				F72729E827D72AC000DBB9C5 /* Frameworks */,
				F72729E927D72AC000DBB9C5 /* Resources */,
				F7E3BA8B27D98C310056517F /* ShellScript */,
				A6DCACD761BB041FCFE5390B /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F72729F827D72ADE00DBB9C5 /* PBXTargetDependency */,
			);
			name = NotificationServiceExtension;
			productName = NotificationServiceExtension;
			productReference = F72729EB27D72AC000DBB9C5 /* NotificationServiceExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		F75D020927BF928900259519 /* UPPushServiceExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F75D021227BF928A00259519 /* Build configuration list for PBXNativeTarget "UPPushServiceExtension" */;
			buildPhases = (
				53AD5D8A3CC03AA6CF67C523 /* [CP] Check Pods Manifest.lock */,
				F75D020627BF928900259519 /* Sources */,
				F75D020727BF928900259519 /* Frameworks */,
				F75D020827BF928900259519 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UPPushServiceExtension;
			productName = UPPushServiceExtension;
			productReference = F75D020A27BF928900259519 /* libUPPushServiceExtension.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		40FDF3BC2554093300E1B6E5 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				KnownAssetTags = (
					New,
				);
				LastUpgradeCheck = 1200;
				TargetAttributes = {
					406C0150269BE39700572FCB = {
						CreatedOnToolsVersion = 12.5.1;
					};
					40FDF3C32554093300E1B6E5 = {
						CreatedOnToolsVersion = 12.0.1;
					};
					40FDF3D42554094B00E1B6E5 = {
						CreatedOnToolsVersion = 12.0.1;
					};
					40FDF3E32554095C00E1B6E5 = {
						CreatedOnToolsVersion = 12.0.1;
					};
					40FDF3F32554096B00E1B6E5 = {
						CreatedOnToolsVersion = 12.0.1;
					};
					40FDF42C255409AA00E1B6E5 = {
						CreatedOnToolsVersion = 12.0.1;
					};
					40FDF44C2554170500E1B6E5 = {
						CreatedOnToolsVersion = 12.0.1;
					};
					F72729EA27D72AC000DBB9C5 = {
						CreatedOnToolsVersion = 13.2.1;
					};
					F75D020927BF928900259519 = {
						CreatedOnToolsVersion = 13.2.1;
					};
				};
			};
			buildConfigurationList = 40FDF3BF2554093300E1B6E5 /* Build configuration list for PBXProject "UPPush" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 40FDF3BB2554093300E1B6E5;
			productRefGroup = 40FDF3C52554093300E1B6E5 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				40FDF3C32554093300E1B6E5 /* UPPush */,
				40FDF3D42554094B00E1B6E5 /* UPPushCore */,
				40FDF3E32554095C00E1B6E5 /* UPPushUI */,
				40FDF3F32554096B00E1B6E5 /* Debugger */,
				F70AC2C62835D11F00EFF594 /* SYNDebugger */,
				40FDF42C255409AA00E1B6E5 /* UPPushTests */,
				40FDF44C2554170500E1B6E5 /* UPPush_Doc */,
				406C0150269BE39700572FCB /* UPPushResource */,
				F75D020927BF928900259519 /* UPPushServiceExtension */,
				F72729EA27D72AC000DBB9C5 /* NotificationServiceExtension */,
				F70AC2B72834C02B00EFF594 /* SYNNotificationServiceExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		406C014F269BE39700572FCB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40FDF3F22554096B00E1B6E5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				406C015D269C1ACF00572FCB /* UPPushResource.bundle in Resources */,
				40FDF4062554096C00E1B6E5 /* LaunchScreen.storyboard in Resources */,
				404EF225255A70B40030B1F7 /* GoogleService-Info.plist in Resources */,
				40FDF4032554096C00E1B6E5 /* Assets.xcassets in Resources */,
				40FDF4012554096B00E1B6E5 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40FDF42B255409AA00E1B6E5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F7437E2D2702F7B20065AB9A /* features in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F70AC2BE2834C02B00EFF594 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F70AC2DA2835D11F00EFF594 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F70AC2DB2835D11F00EFF594 /* UPPushResource.bundle in Resources */,
				F70AC2DC2835D11F00EFF594 /* LaunchScreen.storyboard in Resources */,
				F70AC2DD2835D11F00EFF594 /* GoogleService-Info.plist in Resources */,
				F70AC2DE2835D11F00EFF594 /* Assets.xcassets in Resources */,
				F70AC2DF2835D11F00EFF594 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F72729E927D72AC000DBB9C5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0D6FF6EBE42F4482CE4D8834 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UPPush_abstract_pod-UPPushCore-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		2C46D99F94E97BBAED8D764D /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NotificationServiceExtension-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		34DFA1B9EA2DE64AEBE82409 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UPPush_abstract_pod-UPPushTests/Pods-UPPush_abstract_pod-UPPushTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UPPush_abstract_pod-UPPushTests/Pods-UPPush_abstract_pod-UPPushTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-UPPush_abstract_pod-UPPushTests/Pods-UPPush_abstract_pod-UPPushTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		39032AD62C04877A956B9D19 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UPPush_abstract_pod-Debugger/Pods-UPPush_abstract_pod-Debugger-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UPPush_abstract_pod-Debugger/Pods-UPPush_abstract_pod-Debugger-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-UPPush_abstract_pod-Debugger/Pods-UPPush_abstract_pod-Debugger-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		40FDF4562554170A00E1B6E5 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\n#appledoc Xcode script  \n# Start constants  \ncompany=\"haier\";  \ncompanyID=\"com.haier\";\ncompanyURL=\"https://www.haier.com/\";\ntarget=\"iphoneos\";\n#target=\"macosx\";\noutputPath=\"~/help\";\n# End constants\n \n/usr/local/bin/appledoc \\\n--project-name \"${PROJECT_NAME}\" \\\n--project-company \"${company}\" \\\n--company-id \"${companyID}\" \\\n--docset-atom-filename \"${company}.atom\" \\\n--docset-feed-url \"${companyURL}/${company}/%DOCSETATOMFILENAME\" \\\n--docset-package-url \"${companyURL}/${company}/%DOCSETPACKAGEFILENAME\" \\\n--docset-fallback-url \"${companyURL}/${company}\" \\\n--output \"${outputPath}\" \\\n--publish-docset \\\n--docset-platform-family \"${target}\" \\\n--logformat xcode \\\n--keep-intermediate-files \\\n--no-repeat-first-par \\\n--no-warn-invalid-crossref \\\n--exit-threshold 2 \\\n--ignore \"Pods\" \\\n\"${PROJECT_DIR}\"\n";
		};
		53AD5D8A3CC03AA6CF67C523 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UPPushServiceExtension-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		55214EB27DA20F5920B29F57 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UPPush_abstract_pod-UPPushTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		5910D6855390C2315BE6116A /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UPPush_abstract_pod-UPPush-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		A6DCACD761BB041FCFE5390B /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		AC588D630A0945BBAB307C44 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UPPush_abstract_pod-Debugger-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E0D68110AD105A0826A2EDC7 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UPPush_abstract_pod-UPPushUI-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		F70AC2BF2834C02B00EFF594 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "echo 'start service extension migrate'\n# Release 版本的extension 由于涉及_CodeSignature无法生成问题，由本地手动archive生成\nif [ ${CONFIGURATION} == 'Debug' ] && [ ${PLATFORM_NAME} == 'iphoneos' ]; then\n    EXTENSION_NAME=${TARGET_NAME}.appex\n    EXTENSION_TARGET_PATH=${PROJECT_DIR}/UPPushResource/ServiceExtension/Extensions/${CONFIGURATION}\n    EXTENSION_TARGET_APP_PATH=${EXTENSION_TARGET_PATH}/${EXTENSION_NAME}\n    EXTENSION_BUILD_PATH=${BUILT_PRODUCTS_DIR}/${EXTENSION_NAME}\n    if [ -d \"$EXTENSION_TARGET_APP_PATH\" ]; then\n        rm -rf ${EXTENSION_TARGET_APP_PATH}\n    fi\n    cp -rf ${EXTENSION_BUILD_PATH} ${EXTENSION_TARGET_PATH}\nfi\n";
		};
		F7E3BA8B27D98C310056517F /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "echo 'start service extension migrate'\n# Release 版本的extension 由于涉及_CodeSignature无法生成问题，由本地手动archive生成\nif [ ${CONFIGURATION} == 'Debug' ] && [ ${PLATFORM_NAME} == 'iphoneos' ]; then\n    EXTENSION_NAME=${TARGET_NAME}.appex\n    EXTENSION_TARGET_PATH=${PROJECT_DIR}/UPPushResource/ServiceExtension/Extensions/${CONFIGURATION}\n    EXTENSION_TARGET_APP_PATH=${EXTENSION_TARGET_PATH}/${EXTENSION_NAME}\n    EXTENSION_BUILD_PATH=${BUILT_PRODUCTS_DIR}/${EXTENSION_NAME}\n    if [ -d \"$EXTENSION_TARGET_APP_PATH\" ]; then\n        rm -rf ${EXTENSION_TARGET_APP_PATH}\n    fi\n    cp -rf ${EXTENSION_BUILD_PATH} ${EXTENSION_TARGET_PATH}\nfi\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		406C014D269BE39700572FCB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40FDF3C02554093300E1B6E5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				4041A1DE255E217800C0D9B0 /* UPPushSERegisterAPI.m in Sources */,
				4041A1F2255E217F00C0D9B0 /* UPPushSEUnregisterAPI.m in Sources */,
				4041A1BD255E216900C0D9B0 /* UPPushRegisterAPI.m in Sources */,
				4041A22A255E22E300C0D9B0 /* UPPushRequestBase.m in Sources */,
				40FDF4792554179100E1B6E5 /* UPPushResponseParser.m in Sources */,
				4041A1EB255E217C00C0D9B0 /* UPPushSEReportStatusAPI.m in Sources */,
				40FDF4742554179100E1B6E5 /* UPPushManager.m in Sources */,
				40FDF4782554179100E1B6E5 /* UPPushSeasiaDataSource.m in Sources */,
				F729C254292F14D80052C67A /* UPPushAbilityTools.m in Sources */,
				40FDF4772554179100E1B6E5 /* UPPushResult.m in Sources */,
				5E857ABE2AD682F400093015 /* UPPushNotDisturbAPI.m in Sources */,
				40FDF47B2554179100E1B6E5 /* UPPushInitializer.m in Sources */,
				4041A1B0255E216200C0D9B0 /* UPPushEncryption.m in Sources */,
				40FDF4762554179100E1B6E5 /* UPPushHomeDataSource.m in Sources */,
				4041A1A3255E215C00C0D9B0 /* UPPushAPIUtil.m in Sources */,
				4041A1D1255E217000C0D9B0 /* UPPushUnregisterAPI.m in Sources */,
				4041A1FF255E218700C0D9B0 /* UPPushSERequestBase.m in Sources */,
				4041A1C4255E216C00C0D9B0 /* UPPushReportStatusAPI.m in Sources */,
				F7F1E3A0299101A900EA3BD8 /* UPPushSettings.m in Sources */,
				40FDF4752554179100E1B6E5 /* UPPushBroadcastSender.m in Sources */,
				40FDF47A2554179100E1B6E5 /* UPPushDataSourceRequest.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40FDF3D12554094B00E1B6E5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				40A19E81256608F3006767C1 /* UPPushStringTools.m in Sources */,
				40FDF50D2554181B00E1B6E5 /* UPPushMessageDevice.m in Sources */,
				40FDF5112554181B00E1B6E5 /* UPPushParser.m in Sources */,
				40FDF5092554181B00E1B6E5 /* UPPushMessageUI.m in Sources */,
				40FDF5082554181B00E1B6E5 /* UPPushMessageUIButton.m in Sources */,
				40FDF5132554181B00E1B6E5 /* UPPushInterceptorManager.m in Sources */,
				40FDF50A2554181B00E1B6E5 /* UPPushMessageBody.m in Sources */,
				40FDF50E2554181B00E1B6E5 /* UPPushMessageExtraData.m in Sources */,
				40FDF50F2554181B00E1B6E5 /* UPPushMessageDeviceControl.m in Sources */,
				4041A3E22562994300C0D9B0 /* UPPushBadgeManager.m in Sources */,
				40FDF5062554181B00E1B6E5 /* UPPushMessage.m in Sources */,
				40FDF5072554181B00E1B6E5 /* UPPushOriginalMessage.m in Sources */,
				40FDF50C2554181B00E1B6E5 /* UPPushMessagePage.m in Sources */,
				40FDF5042554181B00E1B6E5 /* UPPushRetryAssistant.m in Sources */,
				40FDF5052554181B00E1B6E5 /* UPPushDispatcher.m in Sources */,
				40FDF5122554181B00E1B6E5 /* UPPush.m in Sources */,
				40FDF5102554181B00E1B6E5 /* UPPushMessageAPI.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40FDF3E02554095C00E1B6E5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F784FA1F29A852B8007F03FB /* UIAlertView+UPPushEx.m in Sources */,
				40FDF56F255418EC00E1B6E5 /* UPPushBuildInUIInterceptor.m in Sources */,
				40FDF56B255418EC00E1B6E5 /* UPPushPageDataHandler.m in Sources */,
				404EF242255A96420030B1F7 /* UPPushMessage+Completion.m in Sources */,
				67C898F72DF9938F002270C4 /* UPPushAlertView.m in Sources */,
				406C01322692A48C00572FCB /* UPPushToastView.m in Sources */,
				40FDF571255418EC00E1B6E5 /* UPPushProgressShow.m in Sources */,
				400E82D72593176E0062CDC9 /* UPPushBuildInProcessQueueManager.m in Sources */,
				40FDF567255418EC00E1B6E5 /* UPPushApiDataHandler.m in Sources */,
				400E829B2592E61D0062CDC9 /* UPPushBuildInProcessObserveManager.m in Sources */,
				40FDF561255418EC00E1B6E5 /* UPPushGIOTrack.m in Sources */,
				40FDF570255418EC00E1B6E5 /* UPPushAppManager.m in Sources */,
				40FDF573255418EC00E1B6E5 /* UIImage+UPPushImageEffects.m in Sources */,
				406C012726929B2C00572FCB /* UPPushToastManager.m in Sources */,
				F79092EA2828BD5300EE61D2 /* UPPushMessageProcessor.m in Sources */,
				4041A090255BD3E700C0D9B0 /* UPPushAlertController.m in Sources */,
				406C015C269BEACD00572FCB /* UPPushToastArrowLabel.m in Sources */,
				4041A150255BE1A200C0D9B0 /* UPPushAlertModel.m in Sources */,
				4041A176255BF3D300C0D9B0 /* UPPushAlertManager.m in Sources */,
				40FDF565255418EC00E1B6E5 /* UPPushNotificationMessageProcessor.m in Sources */,
				40FDF574255418EC00E1B6E5 /* UIImage+UPPushEmpty.m in Sources */,
				40FDF564255418EC00E1B6E5 /* UPPushCustomizedMessageProcessor.m in Sources */,
				40FDF569255418EC00E1B6E5 /* UPPushNoViewMsgComponent.m in Sources */,
				406C012F26929D1600572FCB /* UPPushToastModel.m in Sources */,
				F79092EE2828C23500EE61D2 /* UPPushMessageProcessorFactory.m in Sources */,
				4041A180255CDA0800C0D9B0 /* UPPushBroadcastController.m in Sources */,
				40FDF56D255418EC00E1B6E5 /* UPPushViewMsgComponent.m in Sources */,
				40FDF563255418EC00E1B6E5 /* UPPushDevControlDataHandler.m in Sources */,
				40FDF56C255418EC00E1B6E5 /* UPPushLogoutObserver.m in Sources */,
				40FDF575255418EC00E1B6E5 /* UPPushLoading.m in Sources */,
				84C17FF62D2BC68100932C86 /* UPPushLocalizationConfig.m in Sources */,
				40FDF566255418EC00E1B6E5 /* UPPushHandlerTool.m in Sources */,
				4041A00A255B7C6300C0D9B0 /* UPPushMessage+Tool.m in Sources */,
				4041A18A255CE17700C0D9B0 /* UPPushNotification.m in Sources */,
				4041A16C255BE27B00C0D9B0 /* UPPushAlertActionModel.m in Sources */,
				406C012C26929D0100572FCB /* UPPushToastController.m in Sources */,
				40FDF572255418EC00E1B6E5 /* UPPushToast.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40FDF3F02554096B00E1B6E5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				40FDF59B2554199D00E1B6E5 /* UPFirebaseChannel.m in Sources */,
				40FDF5852554193D00E1B6E5 /* UPUserLoginApi.m in Sources */,
				40FDF5862554193D00E1B6E5 /* SEUserLoginApi.m in Sources */,
				40FDF3FE2554096B00E1B6E5 /* ViewController.m in Sources */,
				40FDF5922554199300E1B6E5 /* UPJPushChannel.m in Sources */,
				F729C25B293266060052C67A /* UPPushFactor.m in Sources */,
				40FDF3F82554096B00E1B6E5 /* AppDelegate.m in Sources */,
				40FDF4092554096C00E1B6E5 /* main.m in Sources */,
				F729C25E2932666A0052C67A /* UPPushRegisterFactor.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		40FDF429255409AA00E1B6E5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				4041A3EB25636B8E00C0D9B0 /* TestInterceptorCache.m in Sources */,
				40FDF5D4255419F100E1B6E5 /* FakeUPPushDataSource.m in Sources */,
				F7E36A382991117F00D82BF2 /* FakeUPPushSettings.m in Sources */,
				40FDF5CE255419F100E1B6E5 /* FakeUPPushResult.m in Sources */,
				40FDF5D0255419F100E1B6E5 /* FakeUPPushInterceptor.m in Sources */,
				40FDF5D6255419F100E1B6E5 /* CucumberRunner.m in Sources */,
				40FDF5D3255419F100E1B6E5 /* FakeUPPushProvider.m in Sources */,
				40FDF5CB255419F100E1B6E5 /* InitializationSteps.m in Sources */,
				40FDF5D2255419F100E1B6E5 /* FakeUPPushChannel.m in Sources */,
				40FDF5CD255419F100E1B6E5 /* FakeUPPushInitializer.m in Sources */,
				40FDF5D1255419F100E1B6E5 /* FakeUPPushBroadcastSender.m in Sources */,
				4041A25C25622B2600C0D9B0 /* FakeUPPushAllInterceptor.m in Sources */,
				40FDF5CC255419F100E1B6E5 /* PushSteps.m in Sources */,
				40FDF5D7255419F100E1B6E5 /* StepsUtils.m in Sources */,
				40FDF5CF255419F100E1B6E5 /* FakeUPPushBase.m in Sources */,
				40FDF5D5255419F100E1B6E5 /* FakeUPPushManager.m in Sources */,
				4041A23425621D1000C0D9B0 /* MessageSteps.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F70AC2BA2834C02B00EFF594 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F70AC2BB2834C02B00EFF594 /* NotificationService.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F70AC2CE2835D11F00EFF594 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F70AC2CF2835D11F00EFF594 /* UPFirebaseChannel.m in Sources */,
				F70AC2D02835D11F00EFF594 /* UPUserLoginApi.m in Sources */,
				F70AC2D12835D11F00EFF594 /* SEUserLoginApi.m in Sources */,
				F70AC2D22835D11F00EFF594 /* ViewController.m in Sources */,
				F70AC2D32835D11F00EFF594 /* UPJPushChannel.m in Sources */,
				F70AC2D42835D11F00EFF594 /* AppDelegate.m in Sources */,
				F70AC2D52835D11F00EFF594 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F72729E727D72AC000DBB9C5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F72729EF27D72AC000DBB9C5 /* NotificationService.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F75D020627BF928900259519 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F740E72827C4EAF700CD5C3D /* UPPushPersistenceManager.m in Sources */,
				F708C1B027C36D3300B1CAC4 /* UPPushDownloadManager.m in Sources */,
				F7272A0227D72F1000DBB9C5 /* UPPushSEDeclaration.m in Sources */,
				A58743D92DF29128005E7CC8 /* UPPushGetIconRequestTool.m in Sources */,
				A58743DA2DF29128005E7CC8 /* UPPushGetIconRequestManager.m in Sources */,
				A58743DB2DF29128005E7CC8 /* UPPushGetIconAPI.m in Sources */,
				A58743DC2DF29128005E7CC8 /* UPPushGetIconResultModel.m in Sources */,
				F75D020E27BF928A00259519 /* UPPushServiceExtension.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		401A98F025541F5800DA11FA /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 40FDF3D42554094B00E1B6E5 /* UPPushCore */;
			targetProxy = 401A98EF25541F5800DA11FA /* PBXContainerItemProxy */;
		};
		401A98F825541F6600DA11FA /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 40FDF3E32554095C00E1B6E5 /* UPPushUI */;
			targetProxy = 401A98F725541F6600DA11FA /* PBXContainerItemProxy */;
		};
		406C015F269C1AF300572FCB /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 406C0150269BE39700572FCB /* UPPushResource */;
			targetProxy = 406C015E269C1AF300572FCB /* PBXContainerItemProxy */;
		};
		F70AC2B82834C02B00EFF594 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F75D020927BF928900259519 /* UPPushServiceExtension */;
			targetProxy = F70AC2B92834C02B00EFF594 /* PBXContainerItemProxy */;
		};
		F70AC2C72835D11F00EFF594 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 406C0150269BE39700572FCB /* UPPushResource */;
			targetProxy = F70AC2C82835D11F00EFF594 /* PBXContainerItemProxy */;
		};
		F70AC2C92835D11F00EFF594 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 40FDF3E32554095C00E1B6E5 /* UPPushUI */;
			targetProxy = F70AC2CA2835D11F00EFF594 /* PBXContainerItemProxy */;
		};
		F72729F227D72AC100DBB9C5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F72729EA27D72AC000DBB9C5 /* NotificationServiceExtension */;
			targetProxy = F72729F127D72AC100DBB9C5 /* PBXContainerItemProxy */;
		};
		F72729F827D72ADE00DBB9C5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F75D020927BF928900259519 /* UPPushServiceExtension */;
			targetProxy = F72729F727D72ADE00DBB9C5 /* PBXContainerItemProxy */;
		};
		F729C2672935D0400052C67A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 40FDF3C32554093300E1B6E5 /* UPPush */;
			targetProxy = F729C2662935D0400052C67A /* PBXContainerItemProxy */;
		};
		F766EBB427D7225100BCE5F6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 40FDF3C32554093300E1B6E5 /* UPPush */;
			targetProxy = F766EBB327D7225100BCE5F6 /* PBXContainerItemProxy */;
		};
		F7EA35D4283B6DE7006A77B3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F70AC2B72834C02B00EFF594 /* SYNNotificationServiceExtension */;
			targetProxy = F7EA35D3283B6DE7006A77B3 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		40FDF3FF2554096B00E1B6E5 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				40FDF4002554096B00E1B6E5 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		40FDF4042554096C00E1B6E5 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				40FDF4052554096C00E1B6E5 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		406C0154269BE39700572FCB /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A549AABA2DF6D1A000161174 /* UPPushResource_NoSign.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Manual;
				COMBINE_HIDPI_IMAGES = YES;
				DEVELOPMENT_TEAM = 997277Q4SF;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = "";
				"DEVELOPMENT_TEAM[sdk=macosx*]" = "";
				INFOPLIST_FILE = UPPushResource/UPPushResource.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 11.3;
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.UPPushResource;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		406C0155269BE39700572FCB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A549AABA2DF6D1A000161174 /* UPPushResource_NoSign.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Manual;
				COMBINE_HIDPI_IMAGES = YES;
				DEVELOPMENT_TEAM = 997277Q4SF;
				INFOPLIST_FILE = UPPushResource/UPPushResource.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 11.3;
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.UPPushResource;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		40FDF3CB2554093300E1B6E5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		40FDF3CC2554093300E1B6E5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		40FDF3CE2554093300E1B6E5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1065BB6712399C8DC3036771 /* Pods-UPPush_abstract_pod-UPPush.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 4989M57QUH;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		40FDF3CF2554093300E1B6E5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 91C15C6DAA9BBFE5DB9C93C8 /* Pods-UPPush_abstract_pod-UPPush.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 4989M57QUH;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		40FDF3DC2554094B00E1B6E5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E3BAEF2FC30694EF9C0DAE41 /* Pods-UPPush_abstract_pod-UPPushCore.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 4989M57QUH;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		40FDF3DD2554094B00E1B6E5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FCC22B519AD6896512D3312C /* Pods-UPPush_abstract_pod-UPPushCore.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 4989M57QUH;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		40FDF3EB2554095C00E1B6E5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D05F810056980B3D907C0845 /* Pods-UPPush_abstract_pod-UPPushUI.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 4989M57QUH;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		40FDF3EC2554095C00E1B6E5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1712FDBED52C4424B491ABA5 /* Pods-UPPush_abstract_pod-UPPushUI.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 4989M57QUH;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		40FDF40B2554096C00E1B6E5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AEC5C2562C560D9753A175D1 /* Pods-UPPush_abstract_pod-Debugger.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Debugger/Debugger.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = PP27UD8NYZ;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Debugger/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = Uplus99Dev;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		40FDF40C2554096C00E1B6E5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 38DCA9BEDEE63E9AA6A6BBF5 /* Pods-UPPush_abstract_pod-Debugger.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Debugger/Debugger.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = PP27UD8NYZ;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Debugger/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "XC Ad Hoc: com.haier.uhome.Uplus";
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		40FDF436255409AA00E1B6E5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5FC0B6D4784301FCC12830D4 /* Pods-UPPush_abstract_pod-UPPushTests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 4989M57QUH;
				INFOPLIST_FILE = UPPushTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.UPPushTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		40FDF437255409AA00E1B6E5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BCA93657FB2533EF8003BBE6 /* Pods-UPPush_abstract_pod-UPPushTests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 4989M57QUH;
				INFOPLIST_FILE = UPPushTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.UPPushTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		40FDF44E2554170500E1B6E5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 4989M57QUH;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		40FDF44F2554170500E1B6E5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 4989M57QUH;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		F70AC2C12834C02B00EFF594 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CODE_SIGN_ENTITLEMENTS = NotificationServiceExtension/NotificationServiceExtensionDebug.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = KPTA3ZM58R;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 68Y472N582;
				ENABLE_BITCODE = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "SYNNotificationServiceExtension-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = SYNNotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.sybird.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = SybirdNotificationServiceExtension;
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = SybirdAppStoreNotificationServiceExtension;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		F70AC2C22834C02B00EFF594 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CODE_SIGN_ENTITLEMENTS = NotificationServiceExtension/NotificationServiceExtension.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = KPTA3ZM58R;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 68Y472N582;
				ENABLE_BITCODE = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "SYNNotificationServiceExtension-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = SYNNotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.sybird.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = SybirdAppStoreNotificationServiceExtension;
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = SybirdAppStoreNotificationServiceExtension;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		F70AC2E52835D11F00EFF594 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Debugger/Debugger.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = KPTA3ZM58R;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "SYNDebugger-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.sybird;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = sanyiniao_dev;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		F70AC2E62835D11F00EFF594 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Debugger/Debugger.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = KPTA3ZM58R;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "SYNDebugger-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.sybird;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = sanyiniao_appstore;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		F72729F527D72AC100DBB9C5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9E9B57D8BD763292BD85ECA5 /* Pods-NotificationServiceExtension.debug.xcconfig */;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CODE_SIGN_ENTITLEMENTS = NotificationServiceExtension/NotificationServiceExtensionDebug.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PP27UD8NYZ;
				ENABLE_BITCODE = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = UplusNotificationServiceExtension;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		F72729F627D72AC100DBB9C5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 65B0CE5EC38EE21EA62E134E /* Pods-NotificationServiceExtension.release.xcconfig */;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CODE_SIGN_ENTITLEMENTS = NotificationServiceExtension/NotificationServiceExtension.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PP27UD8NYZ;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = PP27UD8NYZ;
				ENABLE_BITCODE = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = UplusAppStoreNotificationServiceExtension;
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = UplusAppStoreNotificationServiceExtension;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		F75D021027BF928A00259519 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71F131A52938A78DEA96DC4E /* Pods-UPPushServiceExtension.debug.xcconfig */;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		F75D021127BF928A00259519 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B8CEC4896AB2CFF3B7C37020 /* Pods-UPPushServiceExtension.release.xcconfig */;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		406C0156269BE39700572FCB /* Build configuration list for PBXNativeTarget "UPPushResource" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				406C0154269BE39700572FCB /* Debug */,
				406C0155269BE39700572FCB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		40FDF3BF2554093300E1B6E5 /* Build configuration list for PBXProject "UPPush" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				40FDF3CB2554093300E1B6E5 /* Debug */,
				40FDF3CC2554093300E1B6E5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		40FDF3CD2554093300E1B6E5 /* Build configuration list for PBXNativeTarget "UPPush" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				40FDF3CE2554093300E1B6E5 /* Debug */,
				40FDF3CF2554093300E1B6E5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		40FDF3DB2554094B00E1B6E5 /* Build configuration list for PBXNativeTarget "UPPushCore" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				40FDF3DC2554094B00E1B6E5 /* Debug */,
				40FDF3DD2554094B00E1B6E5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		40FDF3EA2554095C00E1B6E5 /* Build configuration list for PBXNativeTarget "UPPushUI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				40FDF3EB2554095C00E1B6E5 /* Debug */,
				40FDF3EC2554095C00E1B6E5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		40FDF40A2554096C00E1B6E5 /* Build configuration list for PBXNativeTarget "Debugger" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				40FDF40B2554096C00E1B6E5 /* Debug */,
				40FDF40C2554096C00E1B6E5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		40FDF435255409AA00E1B6E5 /* Build configuration list for PBXNativeTarget "UPPushTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				40FDF436255409AA00E1B6E5 /* Debug */,
				40FDF437255409AA00E1B6E5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		40FDF44D2554170500E1B6E5 /* Build configuration list for PBXAggregateTarget "UPPush_Doc" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				40FDF44E2554170500E1B6E5 /* Debug */,
				40FDF44F2554170500E1B6E5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F70AC2C02834C02B00EFF594 /* Build configuration list for PBXNativeTarget "SYNNotificationServiceExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F70AC2C12834C02B00EFF594 /* Debug */,
				F70AC2C22834C02B00EFF594 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F70AC2E42835D11F00EFF594 /* Build configuration list for PBXNativeTarget "SYNDebugger" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F70AC2E52835D11F00EFF594 /* Debug */,
				F70AC2E62835D11F00EFF594 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F72729F427D72AC100DBB9C5 /* Build configuration list for PBXNativeTarget "NotificationServiceExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F72729F527D72AC100DBB9C5 /* Debug */,
				F72729F627D72AC100DBB9C5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F75D021227BF928A00259519 /* Build configuration list for PBXNativeTarget "UPPushServiceExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F75D021027BF928A00259519 /* Debug */,
				F75D021127BF928A00259519 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 40FDF3BC2554093300E1B6E5 /* Project object */;
}
