# 1.5.1
## BUG:
> -x_haieruplus-12573 iOS偶现单点登录退出后，还会收到推送消息<br /> 

# 1.5.2
## BUG:
> -hdstwb-4301 【验收】【ios】收到推送之后，后台更新隐私协议，重启app之后，不同意退出app之后，仍显示推送消息，且点击消息，推送消息直接消失<br /> 

# 1.5.0
## TASK:
> -hdszjes-1255 iOS_消息中心V1.0<br /> 
## BUG:
> -hdszjes-1444 【ios验收】后台收到问题反馈消息，APP杀进程后点击消息拉起APP，弹窗出现后立即消失<br /> 

# 1.4.0
## TASK:
> -x_haieruplus-7088 iOS_iOS移除mPaaS框架<br /> 
> -x_haieruplus-7142 iOS_XX_221230_000869端上在推送库中开放pushid字段的API接口<br /> 
> -x_haieruplus-7128 iOS_iOS推送弹框切换为alertView<br /> 

# 1.3.1
## BUG:
> -x_haieruplus-6878 iOS APP应用图标推送角标不消失<br /> 

# 1.3.0
## TASK:
> -x_haieruplus-4776 iOS_XX_221123_000848端上在消息通道注册接听视音频能力字段<br /> 
> -x_haieruplus-4772 iOS_推送平台化<br /> 

# 1.0.34
## TASK:
> -hdstwb-188 iOS_推送新增验收域名支持<br /> 
> -hdstwb-186 iOS_官方号增加字段<br /> 

# 1.0.33
## TASK:
> -UAPPAC2-3194 iOS_消息推送调试问题修复<br /> 

# 1.0.32
## TASK:
> -SYNAPP-10519 iOS_推送管理<br /> 

# 1.0.30
## TASK:
> -ZHIJIAAPP-41066 iOS_JY-iOS 基于Xcode13空安全检查修改<br /> 

# 1.0.29
## TASK:
> -ZHIJIAAPP-38219 iOS_iOS 空安全隐患的修改(二期)<br /> 
> -ZHIJIAAPP-38353 iOS_US-消息体结构优化（支持extraParam、通知栏增加缩略图）<br /> 

# 1.0.27
## TASK:
> -ZHIJIAAPP-35676 iOS_US-小优助手支持异步消息<br /> 

# 1.0.25
## TASK:
> -ZHIJIAAPP-33557 iOS_iOS 空安全隐患的修改<br /> 
