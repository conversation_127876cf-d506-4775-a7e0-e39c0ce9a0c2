* {
	box-sizing: border-box;
}

.clear {
	clear: both;
}

.clearfix {
	&:before, &:after {
		clear: both;
		display: table;
		content: "";
	}
}

.xcode .hide-in-xcode {
	display: none;
}

body {
	font: 62.5% $body-font;
	background: $body-background;

	@media (max-width: $mobile-max-width) {
		background-color: $content-background;
	}
}

h1, h2, h3 {
	font-weight: 300;
	color: #808080;
}

h1 {
	font-size: 2em;
	color: #000;
}

h4 {
	font-size: 13px;
	line-height: 1.5;
	margin: 21px 0 0 0;
}

a {
	color: $tint-color;
	text-decoration: none;
}

pre, code {
	font-family: $code-font;
	word-wrap: break-word;
}

pre > code, .method-declaration code {
	display: inline-block;
	font-size: .85em;
	padding: 4px 0 4px 10px;
	border-left: 5px solid rgba(0, 155, 51, .2);

	&:before {
		content: "Objective-C";
		display: block;

		font: 9px/1 $body-font;
		color: #009b33;
		text-transform: uppercase;
		letter-spacing: 2px;
		padding-bottom: 6px;
	}
}

pre > code {
	font-size: inherit;
}

table, th, td {
	border: 1px solid #e9e9e9;
}

table {
	width: 100%;
}

th, td {
	padding: 7px;

	> :first-child {
		margin-top: 0;
	}

	> :last-child {
		margin-bottom: 0;
	}
}

.container {
	@extend .clearfix;

	max-width: 980px;
	padding: 0 10px;
	margin: 0 auto;

	@media (max-width: $mobile-max-width) {
		padding: 0;
	}
}

header {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 2;

	background: #414141;
	color: #fff;
	font-size: 1.1em;
	line-height: 25px;
	letter-spacing: .05em;

	#library-title {
		float: left;
	}

	#developer-home {
		float: right;
	}

	h1 {
		font-size: inherit;
		font-weight: inherit;
		margin: 0;
	}

	p {
		margin: 0;
	}

	h1, a {
		color: inherit;
	}

	@media (max-width: $mobile-max-width) {
		.container {
			padding: 0 10px;
		}
	}
}

aside {
	position: fixed;
	top: 25px;
	left: 0;
	width: 100%;
	height: 25px;
	z-index: 2;

	font-size: 1.1em;

	#header-buttons {
		background: rgba(255, 255, 255, .8);
		margin: 0 1px;
		padding: 0;
		list-style: none;
		text-align: right;
		line-height: 32px;

		li {
			display: inline-block;
			cursor: pointer;
			padding: 0 10px;
		}

		label, select {
			cursor: inherit;
		}

		#on-this-page {
			position: relative;

			.chevron {
				display: inline-block;
				width: 14px;
				height: 4px;
				position: relative;

				.chevy {
					background: #878787;
					height: 2px;
					position: absolute;
					width: 10px;

					&.chevron-left {
						left: 0;
						transform: rotateZ(45deg) scale(0.6);
					}

					&.chevron-right {
						right: 0;
						transform: rotateZ(-45deg) scale(0.6);
					}
				}
			}

			#jump-to {
				opacity: 0;
				font-size: 16px;

				position: absolute;
				top: 5px;
				left: 0;
				width: 100%;
				height: 100%;
			}
		}
	}
}

article {
	margin-top: 25px;

	#content {
		@extend .clearfix;

		background: $content-background;
		border: 1px solid $content-border;
		padding: 15px 25px 30px 25px;

		font-size: 1.4em;
		line-height: 1.45;

		position: relative;

		@media (max-width: $mobile-max-width) {
			padding: 15px 10px 20px 10px;
			border: none;
		}

		.navigation-top {
			position: absolute;
			top: 15px;
			right: 25px;
		}

		.title {
			margin: 21px 0 0 0;
			padding: 15px 0;
		}

		p {
			color: #414141;
			margin: 0 0 15px 0;
		}

		th, td {
			p:last-child {
				margin-bottom: 0;
			}
		}

		main {
			ul {
				list-style: none;
				margin-left: 24px;
				margin-bottom: 12px;
				padding: 0;

				li {
					position: relative;
					padding-left: 1.3em;

					&:before {
						content: "\02022";

						color: #414141;
						font-size: 1.08em;
						line-height: 1;

						position: absolute;
						left: 0;
						padding-top: 2px;
					}
				}
			}
		}

		footer {
			@extend .clearfix;

			.footer-copyright {
				margin: 70px 25px 10px 0;
			}

			p {
				font-size: .71em;
				color: #a0a0a0;
			}
		}
	}
}
