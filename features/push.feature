Feature: push

    功能范围：
    主要提供推送注册、注销，发送消息广播，上报消息状态等功能:
    - 注册和注销推送；
    - 添加和移除消息拦截器；
    - 当推送信道收到消息时根据不同类型发送广播消息；
    - 阅读推送消息后，向服务器上报推送为已读状态；
    - 获取推送ID

    外部依赖：
    1、推送数据源
    2、推送信道
    3、广播接收器
    4、推送id提供者

    接口说明：

    1. 注册推送信道
    调用者传入用户id 和回调函数为参数。
    判断传入用户id，为空或空字符串则失败返回。
    不为空，从推送id提供者获取推送id，调用推送数据源的注册接口；
    将返回的结果解析后将调用结果通过回调函数返回调用者。
    2. 注销推送信道
    传入参数回调函数，调用者调用推送数据源注销推送接口
    3. 添加消息拦截器
    传入参数，消息拦截器。设置拦截器，拦截器设置后。
    从推送信道发送的广播消息被拦截，由拦截器处理。
    4. 移除消息拦截器
    移除拦截器后，从推送信道发送的广播消息有默认拦截器处理。
    5. 发送消息广播
    推送信道收到信息，广播发送给广播接收器。消息类型有："通知栏接收"、"通知栏打开"、"自定义消息"
    6. 消息状态上报
    对传入参数消息ID进行非空校验，如果消息ID为空，则返回。
    消息ID不为空，调用上报消息状态接口。
    7. 获取推送ID
    获取由推送信道生成的推送ID

    Background:
        Given 组件已正常初始化

    Scenario: [1001] 注册推送,从推送id提供者得到的推送id为空，调用注册接口成功，注册成功
        Given 推送id提供者提供的推送id为"空对象"
        When 调用注册推送方法
        Then 推送id提供者的获取推送id接口调用"5"次
        Then 推送数据源的注册接口调用"1"次,参数为:
            |pushId| channel|devAlias    |msgVersion| ability   | clientType |
            |空对象 |4      |fakeModel   |  v3       | []        | 2          |
        Then 调用回调函数注册结果,参数为"成功"

    Scenario: [1002] 注册推送,获取的推送id为空，调用注册接口失败，注册失败
        Given 推送id提供者提供的推送id为"空对象"
        Given 推送数据源注册接口返回"失败"
        When 调用注册推送方法
        Then 推送id提供者的获取推送id接口调用"5"次
        Then 推送数据源的注册接口调用"1"次,参数为:
            |pushId| channel|devAlias    |msgVersion| ability   | clientType |
            |空对象 |4      |fakeModel   |  v3       | []        |    2       |
        Then 调用回调函数注册结果,参数为"失败"

    Scenario: [1003] 注册推送,获取的推送id不为空,调用注册接口成功，注册成功
        Given 推送id提供者提供的推送id为"fakePushId"
        When 调用注册推送方法
        Then 推送id提供者的获取推送id接口调用"1"次
        Then 推送数据源的注册接口调用"1"次,参数为:
            |pushId     | channel|devAlias    |msgVersion| ability   | clientType |
            |fakePushId |0       |fakeModel   |  v3      |   []      |       2    |
        Then 调用回调函数注册结果,参数为"成功"

    Scenario: [1004] 注册推送,获取的推送id不为空,调用注册接口失败，注册失败
        Given 推送id提供者提供的推送id为"fakePushId"
        Given 推送数据源注册接口返回"失败"
        When 调用注册推送方法
        Then 推送id提供者的获取推送id接口调用"1"次
        Then 推送数据源的注册接口调用"1"次,参数为:
            |pushId     | channel|devAlias    |msgVersion| ability   | clientType |
            |fakePushId |0       |fakeModel   |  v3      |       []  |         2  |
        Then 调用回调函数注册结果,参数为"失败"

    Scenario: [1010] 注销推送,推送数据源调用注销推送接口失败，注销失败
        Given 推送数据源调用注销推送接口返回"失败"
        When 调用注销推送方法
        Then 推送数据源的注销接口调用"1"次
        Then 调用回调函数注销结果,参数为"失败"

    Scenario: [1011] 注销推送,推送数据源调用注销推送接口成功，注销成功
        When 调用注销推送方法
        Then 推送数据源的注销接口调用"1"次
        Then 调用回调函数注销结果,参数为"成功"

    Scenario: [1020] 添加拦截器,传入拦截器为空,设置失败
        When 调用添加拦截器方法,参数为"空对象"
        Then 拦截器列表如下:
            |interceptorList|

    Scenario: [1021] 添加拦截器,传入拦截器不为空,且当前拦截器集合中不存在该设备,设置成功
        When 调用添加拦截器方法,参数为"fakeInterceptor1"
        Then 拦截器列表如下:
            |inteceptorList |
            |fakeInterceptor1|

    Scenario: [1022] 添加拦截器,传入拦截器不为空,拦截器列表中已经存在该拦截器，调用添加拦截器,设置失败
        When 调用添加拦截器方法,参数为"fakeInterceptor1"
        Then 拦截器列表如下:
            |inteceptorList |
            |fakeInterceptor1|
        When 调用添加拦截器方法,参数为"fakeInterceptor1"
        Then 拦截器列表如下:
            |inteceptorList |
            |fakeInterceptor1|

    Scenario: [1030] 移除拦截器,传入拦截器为空,移除失败
        When 调用添加拦截器方法,参数为"fakeInterceptor1"
        Then 拦截器列表如下:
            |inteceptorList |
            |fakeInterceptor1|
        When 调用移除拦截器方法,参数为"空对象"
        Then 拦截器列表如下:
            |inteceptorList |
            |fakeInterceptor1|

    Scenario: [1031] 移除拦截器,传入拦截器不为空,从拦截器列表中移除失败,移除拦截器失败
        When 调用添加拦截器方法,参数为"fakeInterceptor1"
        Then 拦截器列表如下:
            |inteceptorList |
            |fakeInterceptor1|
        When 调用移除拦截器方法,参数为"fakeInterceptor2"
        Then 拦截器列表如下:
            |inteceptorList |
            |fakeInterceptor1|

    Scenario: [1032] 移除拦截器,传入拦截器不为空,从拦截器列表中移除成功,移除拦截器成功
        When 调用添加拦截器方法,参数为"fakeInterceptor1"
        Then 拦截器列表如下:
            |inteceptorList |
            |fakeInterceptor1|
        When 调用移除拦截器方法,参数为"fakeInterceptor1"
        Then 拦截器列表如下:
            |inteceptorList |

    Scenario: [1040] 发送广播消息,分发器为空,不发送消息广播
        Given 消息分发器为空
        When 推送信道收到一条消息如下:
            |  messageType     |  message     |
            |  fakeMessageType  |  fakeMessage  |
        Then 消息分发器的分发消息方法被调用"0"次

    Scenario: [1041] 发送广播消息,分发器不为空,广播消息对象为空,发送广播消息失败
        When 推送信道收到一条消息如下:
            |  messageType  |  message     |
        Then 消息分发器的分发消息方法被调用"0"次

    Scenario Outline: [1042] 发送广播消息,分发器不为空,广播消息对象不为空,消息内容为空,发送广播消息失败
        When 推送信道收到一条消息如下:
            |  messageType      |  message     |
            | <fakeMessageType> | <fakeMessage> |
        Then 消息分发器的分发消息方法被调用"0"次
        Examples:
            | fakeMessageType   | fakeMessage |
            | fakeMessageType1 |  空对象     |
            |fakeMessageType1  |  空字符串   |

    Scenario Outline: [1043] 发送广播消息,分发器不为空,广播消息合法,消息类型为通知栏消息,发送通知栏消息广播
        When 推送信道收到一条消息如下:
            |  messageType      |  message     |
            | <fakeMessageType> | <fakeMessage> |
        Then 消息分发器的分发消息方法被调用"1"次,参数为:
            |  messageType      |  message     |
            | <fakeMessageType> | <fakeMessage> |
        Then 广播发送器,发送如下消息:
            |  messageType      |  message     |
            | <fakeMessageType> | <fakeMessage> |
        Examples:
            | fakeMessageType      | fakeMessage |
            | NOTIFICATION_RECEIVE |  fakeMessage1  |
            |NOTIFICATION_OPEN     |  fakeMessage2   |

    Scenario: [1044] 发送广播消息,分发器不为空,广播消息不为空,消息类型为自定义消息,没有设置拦截器,发送自定义消息广播
        When 推送信道收到一条消息如下:
            |  messageType  |  message     |
            | CUST_MESSAGE | fakeMessage1 |
        Then 消息分发器的分发消息方法被调用"1"次,参数为:
            |  messageType  |  message     |
            | CUST_MESSAGE  | fakeMessage1 |
        Then 广播发送器,发送如下消息:
            |  messageType  |  message     |
            | CUST_MESSAGE  | fakeMessage1 |

    Scenario: [1045] 发送广播消息,分发器不为空,广播消息不为空,消息类型为自定义消息,有拦截器,但不满足拦截条件,发送自定义消息广播
        When 调用添加拦截器方法,参数为"fakeInterceptor1"
        Then 拦截器列表如下:
            |inteceptorList  |
            |fakeInterceptor1|
        Given 拦截器"fakeInterceptor1"消息拦截条件为:
            |condition   |
            |fakeMessage2|
        When 推送信道收到一条消息如下:
            |  messageType  |  message     |
            | CUST_MESSAGE  | fakeMessage1 |
        Then 消息分发器的分发消息方法被调用"1"次,参数为:
            |  messageType  |  message     |
            | CUST_MESSAGE  | fakeMessage1 |
        Then 广播发送器,发送如下消息:
            |  messageType  |  message     |
            | CUST_MESSAGE  | fakeMessage1 |

    Scenario: [1046] 发送广播消息,分发器不为空,广播消息不为空,消息类型为自定义消息,有拦截器,且满足拦截条件,拦截器处理消息,发送自定义消息广播
        Given 拦截器"fakeInterceptor1"消息拦截条件为:
            |condition  |
            |fakeMessage1 |
        When 调用添加拦截器方法,参数为"fakeInterceptor1"
        Then 拦截器列表如下:
            |inteceptorList  |
            |fakeInterceptor1|
        When 推送信道收到一条消息如下:
            |  messageType  |  message     |
            | CUST_MESSAGE  | fakeMessage1 |
        Then 消息分发器的分发消息方法被调用"1"次,参数为:
            |  messageType  |  message     |
            | CUST_MESSAGE  | fakeMessage1 |
        Then 广播发送器,发送如下消息:
            |  messageType  |  message     |
            | CUST_MESSAGE  | fakeMessage1 |
        Then 消息拦截器"fakeInterceptor1"的处理方法被调用"1"次,参数为:
            |  messageType  |  message     |
            | CUST_MESSAGE  | fakeMessage1 |

    Scenario: [1047] 发送广播消息,分发器不为空,广播消息不为空,消息类型为自定义消息,有两个拦截器,且都满足拦截条件,拦截器处理消息,发送自定义消息广播
        Given 拦截器"fakeInterceptor1"消息拦截条件为:
            |condition    |
            |fakeMessage1 |
        Given 拦截器"fakeInterceptor2"消息拦截条件为:
            |condition    |
            |fakeMessage1 |
        When 调用添加拦截器方法,参数为"fakeInterceptor1"
        When 调用添加拦截器方法,参数为"fakeInterceptor2"
        Then 拦截器列表如下:
            |inteceptorList  |
            |fakeInterceptor1|
            |fakeInterceptor2|
        When 推送信道收到一条消息如下:
            |  messageType |  message     |
            | CUST_MESSAGE | fakeMessage1 |
        Then 消息分发器的分发消息方法被调用"1"次,参数为:
            |  messageType |  message     |
            | CUST_MESSAGE | fakeMessage1 |
        Then 广播发送器,发送如下消息:
            |  messageType  |  message     |
            | CUST_MESSAGE  | fakeMessage1 |
        Then 消息拦截器"fakeInterceptor1"的处理方法被调用"1"次,参数为:
            |  messageType |  message     |
            | CUST_MESSAGE | fakeMessage1 |
        Then 消息拦截器"fakeInterceptor2"的处理方法被调用"1"次,参数为:
            |  messageType |  message     |
            | CUST_MESSAGE | fakeMessage1 |
            
    Scenario: [1050] 消息状态上报,传入消息ID,上报消息状态为已读,回调成功结果。
        When 调用消息状态上报接口,传入消息ID为"1234567"
        Then 推送数据源消息状态上报接口被调用"1"次,参数为"1234567"
        Then 消息状态上报接口回调结果为"成功"

    Scenario Outline: [1051] 消息状态上报,传入消息ID为空对象,回调失败结果。
        When 调用消息状态上报接口,传入消息ID为"<messageID>"
        Then 推送数据源消息状态上报接口被调用"0"次
        Then 消息状态上报接口回调结果为"失败"
        Examples:
            | messageID | 
            | 空对象  |
            | 空字符串  |

    Scenario Outline: [1052] 注册推送,获取的推送id不为空,推送模块和app分别注册能力,调用注册接口成功，注册成功
        Given 推送id提供者提供的推送id为"fakePushId"
        Given 推送库注册能力为"<pushLibAbility>"
        When 调用注册推送方法,自定义能力为"<appCustomAbility>"
        Then 推送id提供者的获取推送id接口调用"1"次
        Then 推送数据源的注册接口调用"1"次,参数为:
            | pushId     | channel | devAlias  | msgVersion | ability   | clientType |
            | fakePushId | 0       | fakeModel | v3         | <ability> | 2          |
        Then 调用回调函数注册结果,参数为"成功"
        Examples:
            | pushLibAbility      | appCustomAbility           | ability                                      |
            |                     |                            | []                                           |
            |                     | MakeVideoCall              | [action_makeVideoCall]                       |
            | Notification        |                            | [notification]                               |
            | Action,Notification | MakeVideoCall              | [action, action_makeVideoCall, notification] |
            | Action,Notification | Notification,MakeVideoCall | [action, action_makeVideoCall, notification] |

    Scenario Outline: [1053]初始化推送组件，获取由推送id提供者得到的推送id。
        Given 推送id提供者提供的推送id为"<pushID>"
        Then 获取推送ID接口返回结果为"<pushID>"
        Examples:
            | pushID | 
            | 空对象  |
            |        |
            | mock_Pushid |
