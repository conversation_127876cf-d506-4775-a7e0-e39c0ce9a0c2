各Feature中每条Scenario对应序号如下：
push.feature : 1000


```
Scenario Outline: [1000]xxxxx
        Given xxxx
        When xxx
        Then xxx
```

### Feature中通用Background如下

```
    Given 推送信道使用"模拟的"
    Given 本地存储使用"模拟的"
    Given 对话框管理器使用"模拟的"
    Given 通知栏管理器使用"模拟的"  
    Given 广播接收器使用"模拟的"  
```

## 通用术语 
1. 空对象 - iOS指nil,Android指null
2. 空字符串- 双端均指""长度为0的字符串对象
3. 推送信道 - 指推送消息信息来源，是对推送sdk 的封装
4. 广播接收器 - 广播发送后，接收广播消息的对象
5. 推送id提供者 - 从该提供者可以获取到推送id
6. 拦截器 - 也叫消息拦截器，可以对消息进行拦截处理
7. 拦截器列表 - 也叫消息拦截器列表，推送中可以添加多个消息拦截器；形成列表，消息遍历拦截器检查是否满足条件，是否需要处理
8. 消息分发器 - 用于分发消息的组件，该组件负责收到消息后根据消息的类型，拦截器的情况决定是否发送广播，是否需拦截器处理等


### Feature中的所有依赖源接口，中文名称整理如下：
1、推送数据源
2、推送信道
3、广播接收器
4、对话框管理器
5、通知栏管理器
6、本地存储

### 标识符解释

[Function]表示方法，方法名与组件Feature中接口名一致
[Operation]表示操作,可以为"添加"，"移除"，"获取"，"更新"等操作动词
[Bool]表示YES或者NO,可以对应"成功"或者"失败"
[Object]表示使用对象,可以为"设备"，"用户"等
[Argments]表示参数
[API]表示指外部依赖中用到的接口
[Number]表示数字
[Attribute]表示对象的某个属性
<###>表示泛指任意词
[Data]表示数据，也可以表示参数和返回值的关系表，参数列表等。可以为列表,JSON等任意数据,

```
//如下只是列表格式，可以为其他格式
    | A  | B  | C  |
    | a1 | b1 | c1 |
    | a2 | b2 | c2 | 
```
### 相关领域语言
* Given <###>数据源的<###>数据如下：[Data] 

```
  Given 设备数据源的设备列表数据如下：
    | A  | B  | C  |
    | a1 | b1 | c1 |
    | a2 | b2 | c2 | 
```

* Given <###>数据源的[API]接口执行结果为[Bool]

```
   Given 设备数据源的获取设备列表接口执行结果为“成功”
```
* Given <###>数据源的[API]接口执行结果为[Bool]，返回的数据如下：[Data]

```
  Given  设备数据源的获取设备列表接口执行结果为“成功”，返回的数据如下：
    | A  | B  | C  |
    | a1 | b1 | c1 |
    | a2 | b2 | c2 | 
```
* Given <###>数据源的[API]接口，<###>与<###>的对应关系如下：[Data]

```
   Given 设备数据源的获取设备列表接口，参数与执行结果的对应关系如下：
    | A  | B  | C  |
    | a1 | b1 | c1 |
    | a2 | b2 | c2 | 
```
* Then <###>数据源[API]接口被调用[Number]次

```
   Then 设备数据源的获取设备列表接口被调用“2”次 
```
* Then <###>数据源[API]接口被调用[Number]次，参数如下：[Data]

```
   Then 设备数据源的获取设备列表接口被调用“2”次，参数如下：
    | A  | B  | C  |
    | a1 | b1 | c1 |
```

### 调用方法的相关领域语言
* When [Object][Operation][Function|<###>]

```
  When 使用者获取设备"A"
```
* When [Object][Operation][Function|<###>]，参数如下：[Data]

```
  When 使用者"A"调用获取设备方法，参数如下：
    | A  | B  | C  |
    | a1 | b1 | c1 |
```
* Then [Object]收到[API]接口的返回值如下：[Data]

```
  Then 使用者收到获取设备接口的返回值如下：
    | A  | B  | C  |
    | a1 | b1 | c1 |
```
* Then [Object]收到[API]接口的返回值为[Bool]

```
  Then 使用者"A"收到更新设备接口的返回值为“成功”
```
