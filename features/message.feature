Feature: message

    功能范围：
    主要提供接收消息，对推送消息实体化，并且根据消息属性进行不同动作等功能:
    - 推送消息实体化：对推送信道接收的消息Json，或字符串，或者Dictionary解析，创建消息对象实体。
    - Badge数量同步：调用者可用过同步Badge数量接口，将本地Badge与各信道服务器Badge数量同步，同时设定App Badge。（此功能为iOS独有）

    外部依赖：
    1、推送数据源
    2、推送信道
    3、广播接收器
    7、推送id提供者

    接口说明：
    1. 推送消息实体化
    对传入的推送信息进行非空校验。
    为空对象或空字符串，消息对象创建失败，创建消息对象为空对象。
    不为空，对传入参数进行解析，若传入参数为非法数据（非法Json等），消息对象创建失败，创建消息对象为空对象；
    若传入参数为合法数据，解析成功，并创建消息对象填入对应属性；
    创建的消息对象发送给拦截器。
    2.Badge数量同步
    调用Badge数量同步接口，传入整数参数作为希望设置的Badge数量，内部会缓存设置的Badge数量，
    同时同步信道服务器的Badge 数量，设置App Badge数量。
    若传入的为负数，最终Badge为当前Badge源（目前只有消息中心一个Badge源）数目减去此负数，若结果仍然为负，则设置为零。

    Background:
        Given 组件已正常初始化
        Given 推送消息数据初始化如下:
        |messageKey|messageObject|messageBase64| messageJsonStr|
        |message1|messageObject1  | eyJib2R5Ijp7ImV4dERhdGEiOnsiZGV2aWNlIjpbeyJ0eXBlSWQiOjEsImRldmljZU5hbWUiOiIiLCJkZXZpY2VJZCI6IiJ9XSwicGFnZXMiOlt7InVybCI6IiIsInBhcmFtcyI6e30sImNhbGxJZCI6MX1dLCJkZXZDb250cm9sIjp7ImNhbGxJZCI6MSwiZGV2aWNlSWQiOiIiLCJncm91cE5hbWUiOiIiLCJjbWRMaXN0Ijp7fX0sImFwaSI6eyJhcGlUeXBlIjoiIiwicGFyYW1zIjp7fSwiY2FsbElkIjoxfSwiY2xpZW50SWQiOiIiLCJleHBpcmVUaW1lIjoxLCJkZXZDb250cm9scyI6W3siY2FsbElkIjoxLCJkZXZpY2VJZCI6IiIsImdyb3VwTmFtZSI6IiIsImNtZExpc3QiOnt9fV0sInBhZ2UiOnsidXJsIjoiIiwicGFyYW1zIjp7fSwiY2FsbElkIjoxfSwiaXNNc2dDZW50ZXIiOjAsInRhcmdldFBhZ2UiOiIifSwidmlldyI6eyJ0aXRsZSI6IiIsImNvbnRlbnQiOiIiLCJzaG93VHlwZSI6LTEsImJ0bnMiOlt7InRleHQiOiIiLCJjYWxsSWQiOjEyM31dfSwiZXh0UGFyYW0iOnsicHVzaFJ1bGVJZCI6IiIsImFsbG93TWVyZ2UiOjEsImFjdFdpdGhOb3RpZnkiOmZhbHNlfX0sInRpbWVzdGFtcCI6MiwibXNnSGlzdG9yeUV4cGlyZXMiOjEsImV4cGlyZXMiOjEsIm1zZ1R5cGUiOjIsIm1zZ0Rlc2MiOiIiLCJtc2dOYW1lIjoiMSIsImJ1c2luZXNzVHlwZSI6MSwibXNnSWQiOiIxIiwiaXNMb25nIjowfQ==|{"body":{"extData":{"device":[{"typeId":1,"deviceName":"","deviceId":""}],"pages":[{"url":"","params":{},"callId":1}],"devControl":{"callId":1,"deviceId":"","groupName":"","cmdList":{}},"api":{"apiType":"","params":{},"callId":1},"clientId":"","expireTime":1,"devControls":[{"callId":1,"deviceId":"","groupName":"","cmdList":{}}],"page":{"url":"","params":{},"callId":1},"isMsgCenter":0,"targetPage":""},"view":{"title":"","content":"","showType":-1,"btns":[{"text":"","callId":123}]},"extParam":{"pushRuleId":"","allowMerge":1,"actWithNotify":false}},"timestamp":2,"msgHistoryExpires":1,"expires":1,"msgType":2,"msgDesc":"","msgName":"1","businessType":1,"msgId":"1","isLong":0}|
        |message2|messageObject2  | eyJib2R5Ijp7ImV4dERhdGEiOnsiZGV2aWNlIjpbeyJ0eXBlSWQiOjEsImRldmljZU5hbWUiOiIiLCJkZXZpY2VJZCI6IiJ9XSwicGFnZXMiOlt7InVybCI6IiIsInBhcmFtcyI6e30sImNhbGxJZCI6MX1dLCJkZXZDb250cm9sIjp7ImNhbGxJZCI6MSwiZGV2aWNlSWQiOiIiLCJncm91cE5hbWUiOiIiLCJjbWRMaXN0Ijp7fX0sImFwaSI6eyJhcGlUeXBlIjoiIiwicGFyYW1zIjp7fSwiY2FsbElkIjoxfSwiY2xpZW50SWQiOiIiLCJleHBpcmVUaW1lIjoxLCJkZXZDb250cm9scyI6W3siY2FsbElkIjoxLCJkZXZpY2VJZCI6IiIsImdyb3VwTmFtZSI6IiIsImNtZExpc3QiOnt9fV0sInBhZ2UiOnsidXJsIjoiIiwicGFyYW1zIjp7fSwiY2FsbElkIjoxfSwiaXNNc2dDZW50ZXIiOjAsInRhcmdldFBhZ2UiOiIifSwidmlldyI6eyJ0aXRsZSI6IiIsImNvbnRlbnQiOiIiLCJzaG93VHlwZSI6MCwiYnRucyI6W3sidGV4dCI6IiIsImNhbGxJZCI6MTIzfV19LCJleHRQYXJhbSI6eyJwdXNoUnVsZUlkIjoiIiwiYWxsb3dNZXJnZSI6MSwiYWN0V2l0aE5vdGlmeSI6ZmFsc2V9fSwidGltZXN0YW1wIjoyLCJtc2dIaXN0b3J5RXhwaXJlcyI6MSwiZXhwaXJlcyI6MSwibXNnVHlwZSI6MiwibXNnRGVzYyI6IiIsIm1zZ05hbWUiOiIyIiwiYnVzaW5lc3NUeXBlIjoxLCJtc2dJZCI6IjIiLCJpc0xvbmciOjB9|{"body":{"extData":{"device":[{"typeId":1,"deviceName":"","deviceId":""}],"pages":[{"url":"","params":{},"callId":1}],"devControl":{"callId":1,"deviceId":"","groupName":"","cmdList":{}},"api":{"apiType":"","params":{},"callId":1},"clientId":"","expireTime":1,"devControls":[{"callId":1,"deviceId":"","groupName":"","cmdList":{}}],"page":{"url":"","params":{},"callId":1},"isMsgCenter":0,"targetPage":""},"view":{"title":"","content":"","showType":0,"btns":[{"text":"","callId":123}]},"extParam":{"pushRuleId":"","allowMerge":1,"actWithNotify":false}},"timestamp":2,"msgHistoryExpires":1,"expires":1,"msgType":2,"msgDesc":"","msgName":"2","businessType":1,"msgId":"2","isLong":0}|
        |message3|messageObject3  | eyJib2R5Ijp7ImV4dERhdGEiOnsiZGV2aWNlIjpbeyJ0eXBlSWQiOjEsImRldmljZU5hbWUiOiIiLCJkZXZpY2VJZCI6IiJ9XSwicGFnZXMiOlt7InVybCI6IiIsInBhcmFtcyI6e30sImNhbGxJZCI6MX1dLCJkZXZDb250cm9sIjp7ImNhbGxJZCI6MSwiZGV2aWNlSWQiOiIiLCJncm91cE5hbWUiOiIiLCJjbWRMaXN0Ijp7fX0sImFwaSI6eyJhcGlUeXBlIjoiIiwicGFyYW1zIjp7fSwiY2FsbElkIjoxfSwiY2xpZW50SWQiOiIiLCJleHBpcmVUaW1lIjoxLCJkZXZDb250cm9scyI6W3siY2FsbElkIjoxLCJkZXZpY2VJZCI6IiIsImdyb3VwTmFtZSI6IiIsImNtZExpc3QiOnt9fV0sInBhZ2UiOnsidXJsIjoiIiwicGFyYW1zIjp7fSwiY2FsbElkIjoxfSwiaXNNc2dDZW50ZXIiOjAsInRhcmdldFBhZ2UiOiIifSwidmlldyI6eyJ0aXRsZSI6IiIsImNvbnRlbnQiOiIiLCJzaG93VHlwZSI6MiwiYnRucyI6W3sidGV4dCI6IiIsImNhbGxJZCI6MTIzfV19LCJleHRQYXJhbSI6eyJwdXNoUnVsZUlkIjoiIiwiYWxsb3dNZXJnZSI6MSwiYWN0V2l0aE5vdGlmeSI6ZmFsc2V9fSwidGltZXN0YW1wIjoyLCJtc2dIaXN0b3J5RXhwaXJlcyI6MSwiZXhwaXJlcyI6MSwibXNnVHlwZSI6MiwibXNnRGVzYyI6IiIsIm1zZ05hbWUiOiIzIiwiYnVzaW5lc3NUeXBlIjoxLCJtc2dJZCI6IjMiLCJpc0xvbmciOjB9|{"body":{"extData":{"device":[{"typeId":1,"deviceName":"","deviceId":""}],"pages":[{"url":"","params":{},"callId":1}],"devControl":{"callId":1,"deviceId":"","groupName":"","cmdList":{}},"api":{"apiType":"","params":{},"callId":1},"clientId":"","expireTime":1,"devControls":[{"callId":1,"deviceId":"","groupName":"","cmdList":{}}],"page":{"url":"","params":{},"callId":1},"isMsgCenter":0,"targetPage":""},"view":{"title":"","content":"","showType":2,"btns":[{"text":"","callId":123}]},"extParam":{"pushRuleId":"","allowMerge":1,"actWithNotify":false}},"timestamp":2,"msgHistoryExpires":1,"expires":1,"msgType":2,"msgDesc":"","msgName":"3","businessType":1,"msgId":"3","isLong":0}|
        |message4|messageObject4  | eyJib2R5Ijp7ImV4dERhdGEiOnsiZGV2aWNlIjpbeyJ0eXBlSWQiOjEsImRldmljZU5hbWUiOiIiLCJkZXZpY2VJZCI6IiJ9XSwicGFnZXMiOlt7InVybCI6IiIsInBhcmFtcyI6e30sImNhbGxJZCI6MX1dLCJkZXZDb250cm9sIjp7ImNhbGxJZCI6MSwiZGV2aWNlSWQiOiIiLCJncm91cE5hbWUiOiIiLCJjbWRMaXN0Ijp7fX0sImFwaSI6eyJhcGlUeXBlIjoiIiwicGFyYW1zIjp7fSwiY2FsbElkIjoxfSwiY2xpZW50SWQiOiIiLCJleHBpcmVUaW1lIjoxLCJkZXZDb250cm9scyI6W3siY2FsbElkIjoxLCJkZXZpY2VJZCI6IiIsImdyb3VwTmFtZSI6IiIsImNtZExpc3QiOnt9fV0sInBhZ2UiOnsidXJsIjoiIiwicGFyYW1zIjp7fSwiY2FsbElkIjoxfSwiaXNNc2dDZW50ZXIiOjAsInRhcmdldFBhZ2UiOiIifSwidmlldyI6eyJ0aXRsZSI6IiIsImNvbnRlbnQiOiIiLCJzaG93VHlwZSI6NCwiYnRucyI6W3sidGV4dCI6IiIsImNhbGxJZCI6MTIzfV19LCJleHRQYXJhbSI6eyJwdXNoUnVsZUlkIjoiIiwiYWxsb3dNZXJnZSI6MSwiYWN0V2l0aE5vdGlmeSI6ZmFsc2V9fSwidGltZXN0YW1wIjoyLCJtc2dIaXN0b3J5RXhwaXJlcyI6MSwiZXhwaXJlcyI6MSwibXNnVHlwZSI6MiwibXNnRGVzYyI6IiIsIm1zZ05hbWUiOiI0IiwiYnVzaW5lc3NUeXBlIjoxLCJtc2dJZCI6IjQiLCJpc0xvbmciOjF9|{"body":{"extData":{"device":[{"typeId":1,"deviceName":"","deviceId":""}],"pages":[{"url":"","params":{},"callId":1}],"devControl":{"callId":1,"deviceId":"","groupName":"","cmdList":{}},"api":{"apiType":"","params":{},"callId":1},"clientId":"","expireTime":1,"devControls":[{"callId":1,"deviceId":"","groupName":"","cmdList":{}}],"page":{"url":"","params":{},"callId":1},"isMsgCenter":0,"targetPage":""},"view":{"title":"","content":"","showType":4,"btns":[{"text":"","callId":123}]},"extParam":{"pushRuleId":"","allowMerge":1,"actWithNotify":false}},"timestamp":2,"msgHistoryExpires":1,"expires":1,"msgType":2,"msgDesc":"","msgName":"4","businessType":1,"msgId":"4","isLong":1}|

    Scenario Outline: [1060] 推送消息实体化,传入的原始消息体,拦截器接收到创建的消息对象。
        When 调用添加拦截器方法,参数为"fakeInterceptorAll"
        When 信道收到的推送消息为"<messageBase64>"
        Then 拦截器"fakeInterceptorAll"接收到的消息为"<messageObject>"
          Examples:
              | messageBase64 | messageObject |
              | message1.messageBase64 | messageObject1 |
              | message2.messageBase64 | messageObject2 |
              | message3.messageBase64 | messageObject3 |
              | message4.messageBase64 | messageObject4 |
    
    Scenario Outline: [1061] 设置系统Badge,传入Badge数量,App Badge被设置为相应数量。
        When 清除当前Badge数量
        When 设置当前Badge数量为"<BadgeNumber>"
        Then 获取当前Badge数量为"<ActualBadgeNumber>"
    Examples:
        | BadgeNumber | ActualBadgeNumber |
        | -10  | 0 |
        | 0  | 0 |
        | 1  | 1 |
        | 10  | 10 |
        | 21  | 21 |
        | 99  | 99 |
        | 100  | 100 |
        | -100  | 0 |
