//
//  UPPushMessage+Tool.m
//  UPPushUI
//
//  Created by osiris on 2020/11/11.
//

#import "UPPushMessage+Tool.h"
#import "UPPushMessage+Completion.m"
#import "UPPushStringTools.h"

@implementation NSDictionary (UPPushExtraParam)

- (BOOL)actWithNotify
{
    id actWithNotifyValue = self[@"actWithNotify"];
    if ([actWithNotifyValue respondsToSelector:@selector(boolValue)]) {
        return [actWithNotifyValue boolValue];
    }

    return NO;
}

- (NSString *)pushRuleID
{
    return self[@"pushRuleId"] ?: @"";
}

- (BOOL)allowMerge
{
    id allowMergeValue = self[@"allowMerge"];
    if ([allowMergeValue respondsToSelector:@selector(boolValue)]) {
        return [allowMergeValue boolValue];
    }

    return NO;
}

@end

@implementation UPPushMessage (Tool)

- (BOOL)isNeedTransformToLocolNotification
{
    if (self.receivePushState == ReceivePush_State_Active && !self.isLocal && !self.messageBody.extraParam.actWithNotify) {
        return YES;
    }

    return NO;
}

- (NSString *)jumpPage
{
    NSString *path = self.messageBody.extraData.targetPage;
    if (UPPush_isEmptyString(path)) {
        if (self.messageBody.extraData.page && self.messageBody.messageUI && self.messageBody.messageUI.showTypeAppearance == MessageUI_Jump) {
            path = self.messageBody.extraData.page.url;
        }
    }

    return path ?: @"";
}

- (BOOL)hasExtraData
{
    if (self.messageBody.extraData.api || (self.deviceControlsCompletion && self.deviceControlsCompletion.count > 0) || (self.pagesCompletion && self.pagesCompletion.count > 0)) {
        return YES;
    }

    return NO;
}

- (id<UPPushMessagePageProtocol>)findPage:(NSInteger)callId
{
    __block id<UPPushMessagePageProtocol> page = nil;
    [self.pagesCompletion enumerateObjectsUsingBlock:^(id<UPPushMessagePageProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (obj.callID == callId) {
          page = obj;
          *stop = YES;
      }
    }];

    return page;
}
@end

@implementation NSDictionary (UPPushExtraDataApiParams)

- (BOOL)isNeedRead
{
    id readStatus = self[@"readStatus"];
    if ([readStatus isKindOfClass:NSString.class]) {
        return ![readStatus isEqualToString:@"1"];
    }

    return YES;
}

@end
