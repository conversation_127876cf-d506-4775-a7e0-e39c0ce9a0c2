//
//  UPPushMessage+Tool.h
//  UPPushUI
//
//  Created by osiris on 2020/11/11.
//

#import "UPPushMessage.h"

NS_ASSUME_NONNULL_BEGIN

@interface NSDictionary (UPPushExtraParam)

@property (nonatomic, assign, readonly) BOOL actWithNotify;
@property (nonatomic, copy, readonly) NSString *pushRuleID;
@property (nonatomic, assign, readonly) BOOL allowMerge;

@end

@interface UPPushMessage (Tool)

- (BOOL)isNeedTransformToLocolNotification;
- (NSString *)jumpPage;
- (BOOL)hasExtraData;
- (id<UPPushMessagePageProtocol>)findPage:(NSInteger)callId;
@end

@interface NSDictionary (UPPushExtraDataApiParams)

@property (nonatomic, assign, readonly) BOOL isNeedRead;

@end

NS_ASSUME_NONNULL_END
