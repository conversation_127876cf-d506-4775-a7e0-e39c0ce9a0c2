//
//  UPPushMessage+Completion.m
//  UPPushUI
//
//  Created by osiris on 2020/11/10.
//

#import "UPPushMessage+Completion.h"

@implementation UPPushMessage (Completion)

- (NSArray<id<UPPushMessagePageProtocol>> *)pagesCompletion
{
    NSArray<id<UPPushMessagePageProtocol>> *tempPages = [self.messageBody.extraData.pages copy];
    if ((!tempPages || tempPages.count == 0) && self.messageBody.extraData.page) {
        tempPages = @[ self.messageBody.extraData.page ];
    }
    return tempPages ?: @[];
}

- (void)setPagesCompletion:(NSArray<id<UPPushMessagePageProtocol>> *)pagesCompletion
{
    /* 配合MJExtension属性解析 */
}

- (NSArray<id<UPPushMessageDeviceControlProtocol>> *)deviceControlsCompletion
{
    NSArray<id<UPPushMessageDeviceControlProtocol>> *tempDeviceControls = [self.messageBody.extraData.deviceControlList copy];
    if ((!tempDeviceControls || tempDeviceControls.count == 0) && self.messageBody.extraData.deviceControl) {
        tempDeviceControls = @[ self.messageBody.extraData.deviceControl ];
    }
    return tempDeviceControls ?: @[];
}

- (void)setDeviceControlsCompletion:(NSArray<id<UPPushMessageDeviceControlProtocol>> *)deviceControlsCompletion
{
    /* 配合MJExtension属性解析 */
}

@end
