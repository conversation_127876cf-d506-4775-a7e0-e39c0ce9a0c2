//
//  UPPushHandlerTool.m
//  mainbox
//
//  Created by osir<PERSON> on 2017/8/28.
//  Copyright © 2017年 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPPushHandlerTool.h"
#import <MJExtension/MJExtension.h>
#if __IPHONE_OS_VERSION_MAX_ALLOWED >= 100000
#import <UserNotifications/UserNotifications.h>
#import "UPPushManager.h"
#endif

#define DUPLICATE_NOTIFICATION_MESSAGE(msgId) [NSString stringWithFormat:@"duplicate_Message_%@", msgId]

NSString *const kUPNMessage_UserdefaultMessageShowingIds = @"kUPNMessage_UserdefaultMessageShowingIds";

extern NSString *const kUPNMessage_ChangeMessageState;

@implementation UPPushHandlerTool

static dispatch_semaphore_t sigin;
+ (BOOL)duplicateMessageHandle:(UPPushMessage *)message
{
    if (!message.messageID || message.messageID.length == 0) {
        return NO;
    }
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      sigin = dispatch_semaphore_create(1);
    });
    dispatch_semaphore_wait(sigin, DISPATCH_TIME_FOREVER);
    NSString *messageID = DUPLICATE_NOTIFICATION_MESSAGE(message.messageID);
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSArray<NSString *> *keys = userDefaults.dictionaryRepresentation.allKeys;
    if ([keys containsObject:messageID]) {
        if (message.isLocal) {
            [userDefaults removeObjectForKey:messageID];
            [userDefaults synchronize];
        }
        dispatch_semaphore_signal(sigin);
        return YES;
    }
    else {
        [userDefaults setObject:messageID forKey:messageID];
        [userDefaults synchronize];
        dispatch_semaphore_signal(sigin);
        return NO;
    }
}

+ (void)transformToLocolNotification:(UPPushMessage *)message
{
    UPPushMessage *locolMessage = [message copy];
    locolMessage.isLocal = YES;
    NSDictionary *userInfo = [locolMessage mj_keyValues];

    UNMutableNotificationContent *content = [[UNMutableNotificationContent alloc] init];
    NSString *soundName = userInfo[@"sound"];
    content.sound = soundName ? [UNNotificationSound soundNamed:soundName] : [UNNotificationSound defaultSound];
    content.title = locolMessage.alertTitle;
    content.subtitle = locolMessage.alertSubTitle;
    content.body = locolMessage.alertBody;
    content.userInfo = userInfo;

    UNTimeIntervalNotificationTrigger *trigger = [UNTimeIntervalNotificationTrigger triggerWithTimeInterval:1 repeats:NO]; //CGFLOAT_MIN
    UNNotificationRequest *request = [UNNotificationRequest requestWithIdentifier:locolMessage.messageID content:content trigger:trigger];
    [[UNUserNotificationCenter currentNotificationCenter] addNotificationRequest:request withCompletionHandler:nil];
}

+ (void)readMessage:(UPPushMessage *)message
{
    [[UPPushManager instance]
            .push report:message.messageID
                  status:Message_Status_read
                callBack:^(BOOL result){
                }];
}


+ (BOOL)isMessageShowing:(NSString *)messageId
{
    BOOL __block isShow = NO;
    NSString *ids = [[NSUserDefaults standardUserDefaults] objectForKey:kUPNMessage_UserdefaultMessageShowingIds];
    if (ids) {
        NSArray<NSString *> *idArr = [ids componentsSeparatedByString:@","];
        [idArr enumerateObjectsUsingBlock:^(NSString *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          if ([messageId isEqualToString:obj]) {
              isShow = YES;
              *stop = YES;
          }
        }];
    }
    return isShow;
}

+ (void)messageDidShowing:(NSString *)messageId
{
    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
    NSString *ids = [userDefault objectForKey:kUPNMessage_UserdefaultMessageShowingIds];

    if (ids != nil) {
        ids = [ids stringByAppendingString:[NSString stringWithFormat:@",%@", messageId]];
    }
    else {
        ids = messageId;
    }
    [userDefault setObject:ids forKey:kUPNMessage_UserdefaultMessageShowingIds];
    [userDefault synchronize];
}

+ (void)messageDidCancelShow:(NSString *)messageId
{
    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
    NSString *ids = [userDefault objectForKey:kUPNMessage_UserdefaultMessageShowingIds];
    if (ids == nil) {
        return;
    }
    __block BOOL isIn = NO;
    NSMutableArray<NSString *> *idArr = [NSMutableArray arrayWithArray:[ids componentsSeparatedByString:@","]];
    [idArr enumerateObjectsUsingBlock:^(NSString *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (messageId) {
          if ([obj isEqualToString:messageId]) {
              isIn = YES;
              *stop = YES;
          }
      }

    }];

    if (isIn) {
        [idArr removeObject:messageId];
        ids = [idArr componentsJoinedByString:@","];
        [userDefault setObject:ids forKey:kUPNMessage_UserdefaultMessageShowingIds];
        [userDefault synchronize];
    }
}

+ (BOOL)isMultiLanguageEnvironment
{
    return [UPPushManager instance].push.pushInitializer.provider.provideChannelType == UPPushChannel_Seasia;
}

@end
