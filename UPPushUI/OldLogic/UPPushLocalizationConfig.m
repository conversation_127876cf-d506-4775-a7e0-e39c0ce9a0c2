//
//  UPPushLocalizationConfig.m
//  UPPushUI
//
//  Created by whenwe on 2025/1/6.
//

#import "UPPushLocalizationConfig.h"

@implementation UPPushLocalizationConfig

+ (instancetype)shareInstance
{
    static UPPushLocalizationConfig *config = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      config = [[UPPushLocalizationConfig alloc] init];
    });
    return config;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.OK = @"OK";
        self.loading = @"Loading…";
        self.loadingTimeout = @"Loading timeout";
    }
    return self;
}

@end
