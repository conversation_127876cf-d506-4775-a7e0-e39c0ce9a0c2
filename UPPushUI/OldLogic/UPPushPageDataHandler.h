//
//  UPNToPageHandler.h
//  mainbox
//
//  Created by chi on 2017/8/18.
//  Copyright © 2017年 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPPushMessage.h"

@interface UPPushPageDataHandler : NSObject

+ (instancetype)shareInstance;

//通知栏点击或者直接跳转类型
- (void)directJumpHandlerWithMessage:(UPPushMessage *)message;

//点击按钮跳转的方法
- (void)gotoPageWithMessage:(id<UPPushMessageProtocol>)data callId:(NSInteger)callId;

@end
