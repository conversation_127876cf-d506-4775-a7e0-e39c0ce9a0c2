//
//  UPNLogoutObserver.h
//  mainbox
//
//  Created by ha<PERSON> on 2019/9/11.
//

#import <UIKit/UIKit.h>
#import "UPPushMessageProtocol.h"

NS_ASSUME_NONNULL_BEGIN

@protocol UPPushLogoutObserverDelegate;

@interface UPPushLogoutObserver : NSObject

@property (nonatomic, weak) id<UPPushLogoutObserverDelegate> delegate;

+ (instancetype)instance;

- (void)addLogoutObserver;
- (void)removeObserver;

@end

@protocol UPPushLogoutObserverDelegate <NSObject>

- (void)logoutForChangePassword:(id<UPPushMessageProtocol>)message;

@end
NS_ASSUME_NONNULL_END
