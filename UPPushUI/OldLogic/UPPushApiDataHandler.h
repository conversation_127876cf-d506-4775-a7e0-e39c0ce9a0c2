//
//  UPNApiDataHandler.h
//  mainbox
//
//  Created by osir<PERSON> on 2019/10/9.
//

#import <Foundation/Foundation.h>
#import "UPPushMessage.h"

NS_ASSUME_NONNULL_BEGIN

@interface UPPushApiDataHandler : NSObject

+ (instancetype)shareInstance;

//单纯分发api消息的方法
- (void)postNotificationWithMessage:(UPPushMessage *)message;

//点击按钮分发消息的方法
- (void)apiWithMessage:(UPPushMessage *)message callId:(NSInteger)callId;
@end

NS_ASSUME_NONNULL_END
