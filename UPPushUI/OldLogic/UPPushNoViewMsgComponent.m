//
//  UPNNoViewMsgComponent.m
//  mainbox
//
//  Created by osiris on 2019/9/29.
//

#import "UPPushNoViewMsgComponent.h"
#import <UPLog/UPLog.h>
#import "UPPushDevControlDataHandler.h"
#import "UPPushApiDataHandler.h"
#import "UPPushHandlerTool.h"
#import "UPPushMessageExtension.h"
#import "UPPushStringTools.h"

@implementation UPPushNoViewMsgComponent
- (void)handleMessage:(UPPushMessage *)message
{
    //设置消息已读的标记
    BOOL isNeedRead = NO;
    // AI发送小优异步消息时，V4模型向V3模型转换副作用，新增deliverData字段，目前也同api消息处理
    if (message.messageBody.extraData.api || !UPPush_isEmptyString(message.messageBody.extraData.deliverData)) {
        UPLogDebug(@"UPPush", @"<UPPush Debugging> messageUI不存在，有api时，作广播处理：%@", message.messageID);
        [[UPPushApiDataHandler shareInstance] postNotificationWithMessage:message];
        isNeedRead = YES;
    }

    if (message.deviceControlsCompletion && message.deviceControlsCompletion.count > 0) {
        UPLogDebug(@"UPPush", @"<UPPush Debugging> messageUI不存在，设备控制信息存在，作广播处理：%@", message.messageID);
        [[UPPushDevControlDataHandler shareInstance] postFristDevControlNotificationWithMessage:message];
        isNeedRead = YES;
    }
    if (!message.messageBody.extraData.api.params.isNeedRead) {
        isNeedRead = NO;
    }
    if (isNeedRead) {
        [UPPushHandlerTool readMessage:message];
    }
}

@end
