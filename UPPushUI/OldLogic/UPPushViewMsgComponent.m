//
//  UPNViewMsgComponent.m
//  mainbox
//
//  Created by Yu<PERSON><PERSON><PERSON><PERSON> on 2017/8/28.
//  Copyright © 2017年 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPPushViewMsgComponent.h"
#import <UPLog/UPLog.h>
#import "UPPushHandlerTool.h"
#import "UPPushApiDataHandler.h"
#import "UPPushPageDataHandler.h"
#import "UPPushDevControlDataHandler.h"
#import "UPPushProgressShow.h"
#import "UPPushMessageExtension.h"
#import "UPPushAlertManager.h"
#import "UPPushToastManager.h"
#import "UPPushGIOTrack.h"
#import "UPPushStringTools.h"

@implementation UPPushViewMsgComponent

#pragma mark - public methods
- (void)handleViewMessage:(UPPushMessage *)message
{
    if (![message hasExtraData] && UPPush_isEmptyString(message.messageBody.extraData.deliverData)) {
        [self handleMessageForNoExtDate:message];
    }
    else {
        [self handleMessage:message];
    }
}

#pragma mark - 无extdate有view的消息处理
- (void)handleMessageForNoExtDate:(UPPushMessage *)message
{
    switch (message.messageBody.messageUI.showTypeAppearance) {
        case MessageUI_Alert:
            [self showAlertWithMessage:message];
            UPLogDebug(@"UPPush", @"<UPPush Debugging> messageUI存在，无extraData时，作Alert处理：%@", message.messageID);
            break;
        case MessageUI_Toast:
            [self showToastWithMessage:message];
            UPLogDebug(@"UPPush", @"<UPPush Debugging> messageUI存在，无extraData时，作Toast处理：%@", message.messageID);
            break;
        case MessageUI_TapToast:
            UPLogDebug(@"UPPush", @"<UPPush Debugging> messageUI存在，有extraData时，作tap Toast处理：%@", message.messageID);
            [self handleTapToastMessage:message];
            break;
        default:
            break;
    }
}

#pragma mark--有extdate和view的消息处理
- (void)handleMessage:(UPPushMessage *)message
{
    switch (message.messageBody.messageUI.showTypeAppearance) {
        case MessageUI_Alert:
            UPLogDebug(@"UPPush", @"<UPPush Debugging> messageUI存在，有extraData时，作Alert处理：%@", message.messageID);
            [self handleAlertMessage:message];
            break;
        case MessageUI_Toast:
            UPLogDebug(@"UPPush", @"<UPPush Debugging> messageUI存在，有extraData时，作Toast处理：%@", message.messageID);
            [self handleToastMessage:message];
            break;
        case MessageUI_Jump:
            UPLogDebug(@"UPPush", @"<UPPush Debugging> messageUI存在，有extraData时，作跳转处理(实则为发广播)：%@", message.messageID);
            [self handleJumpMessage:message];
            break;
        case MessageUI_TapToast:
            UPLogDebug(@"UPPush", @"<UPPush Debugging> messageUI存在，有extraData时，作tap Toast处理：%@", message.messageID);
            [self handleTapToastMessage:message];
            break;
        default:
            break;
    }
}

- (void)handleJumpMessage:(UPPushMessage *)message
{
    if (!message.messageBody.extraData.api && UPPush_isEmptyString(message.messageBody.extraData.deliverData)) {
        return;
    }
    [[UPPushApiDataHandler shareInstance] postNotificationWithMessage:message];
    if (message.messageBody.extraData.api.params.isNeedRead) {
        [UPPushHandlerTool readMessage:message];
    }
}

- (void)handleAlertMessage:(UPPushMessage *)message
{
    [self showAlertWithMessage:message];
}

- (void)showAlertWithMessage:(UPPushMessage *)message
{
    //解决同一个消息多次弹窗
    if ([UPPushHandlerTool isMessageShowing:message.messageID]) {
        return;
    }
    [UPPushHandlerTool messageDidShowing:message.messageID];

    UPLogDebug(@"UPPush", @"<UPPush Debugging> message alert 处理：%@", message.messageID);
    [[UPPushAlertManager instance] showAlertWithMessage:message
                                           actionHandle:^(NSInteger callID) {
                                             [self alertHandler:message.messageBody.extraData callId:callID message:message];
                                             [UPPushHandlerTool readMessage:message];
                                             [UPPushHandlerTool messageDidCancelShow:message.messageID];
                                           }];
}

- (void)alertHandler:(id<UPPushMessageExtraDataProtocol>)data callId:(NSInteger)callId message:(UPPushMessage *)message
{
    if (!message.messageBody.extraData || ![message hasExtraData]) {
        return;
    }

    //添加一次判断，如果都不匹配，需要添加对api，devicecontrol的分发
    if (![self isTapCallIdFitExtDateCallId:callId message:message]) {
        [self tapCallIdNotFit:message];
    }
    else {
        [self tapCallIdFit:data callId:callId message:message];
    }
}

- (void)tapCallIdFit:(id<UPPushMessageExtraDataProtocol>)data callId:(NSInteger)callId message:(UPPushMessage *)message
{
    [self tapCallIdFitPage:data callId:callId message:message];
    [self tapCallIdFitDevControls:data callId:callId message:message];
    [self tapCallIdFitApi:data callId:callId message:message];
}

- (void)tapCallIdFitPage:(id<UPPushMessageExtraDataProtocol>)data callId:(NSInteger)callId message:(UPPushMessage *)message
{
    if (!message.pagesCompletion || message.pagesCompletion.count == 0) {
        return;
    }
    [[UPPushPageDataHandler shareInstance] gotoPageWithMessage:message callId:callId];
}

- (void)tapCallIdFitDevControls:(id<UPPushMessageExtraDataProtocol>)data callId:(NSInteger)callId message:(UPPushMessage *)message
{
    if (!message.deviceControlsCompletion || message.deviceControlsCompletion.count == 0) {
        return;
    }
    [[UPPushDevControlDataHandler shareInstance] devControlWithMessage:message callId:callId];
}

- (void)tapCallIdFitApi:(id<UPPushMessageExtraDataProtocol>)data callId:(NSInteger)callId message:(UPPushMessage *)message
{
    if (!message.messageBody.extraData.api) {
        return;
    }
    [[UPPushApiDataHandler shareInstance] apiWithMessage:message callId:callId];
}

- (void)tapCallIdNotFit:(UPPushMessage *)message
{
    if (!message.messageBody.extraData) {
        return;
    }

    if (message.deviceControlsCompletion && message.deviceControlsCompletion.count > 0) {
        [[UPPushDevControlDataHandler shareInstance] postFristDevControlNotificationWithMessage:message];
    }
    if (message.messageBody.extraData.api) {
        [[UPPushApiDataHandler shareInstance] postNotificationWithMessage:message];
    }
}

- (void)handleToastMessage:(UPPushMessage *)message
{
    [self showToastWithMessage:message];

    if (message.messageBody.extraData.api) {
        [[UPPushApiDataHandler shareInstance] postNotificationWithMessage:message];
    }

    if (message.deviceControlsCompletion && message.deviceControlsCompletion.count > 0) {
        [[UPPushDevControlDataHandler shareInstance] postFristDevControlNotificationWithMessage:message];
    }
}

- (void)handleTapToastMessage:(UPPushMessage *)message
{
    UPLogDebug(@"UPPush", @"<UPPush Debugging> message tap toast 处理：%@", message.messageID);
    [UPPushGIOTrack trackToastBeforeTap:message];
    [[UPPushToastManager instance] showToastWithMessage:message
                                           actionHandle:^(NSInteger callID) {
                                             [self alertHandler:message.messageBody.extraData callId:callID message:message];
                                             [UPPushGIOTrack trackToastAfterTap:message callID:callID];
                                             [UPPushHandlerTool readMessage:message];
                                           }];
}

- (void)showToastWithMessage:(UPPushMessage *)message
{
    if (message.messageBody.messageUI && message.messageBody.messageUI.content) {
        if (message.messageBody.messageUI) {
            UPLogDebug(@"UPPush", @"<UPPush Debugging> message toast 处理：%@", message.messageID);
            [UPPushProgressShow showInfo:message.messageBody.messageUI.content];
        }
    }
    [UPPushHandlerTool readMessage:message];
}

#pragma mark - private methods
- (BOOL)isTapCallIdFitExtDateCallId:(NSInteger)callId message:(UPPushMessage *)message
{
    __block BOOL isFit = NO;
    if (!message.messageBody.extraData) {
        return isFit;
    }
    [message.pagesCompletion enumerateObjectsUsingBlock:^(id<UPPushMessagePageProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (obj.callID == callId) {
          isFit = YES;
          *stop = YES;
      }
    }];
    [message.deviceControlsCompletion enumerateObjectsUsingBlock:^(id<UPPushMessageDeviceControlProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (obj.callID == callId) {
          isFit = YES;
          *stop = YES;
      }
    }];

    if (message.messageBody.extraData.api && message.messageBody.extraData.api.callID == callId) {
        isFit = YES;
    }
    return isFit;
}
@end
