//
//  UPPushGIOTrack.m
//  mainbox
//
//  Created by ha<PERSON> on 2020/10/10.
//  Copyright © 2020 路鹏. All rights reserved.
//

#import "UPPushGIOTrack.h"
#import <UPLog/uplog.h>
#import "UPPushStringTools.h"
#import "UPPushMessage+Tool.h"
#import <MJExtension/MJExtension.h>

@implementation UPPushGIOTrack

+ (instancetype)instance
{
    static dispatch_once_t onceToken;
    static UPPushGIOTrack *track = nil;
    dispatch_once(&onceToken, ^{
      track = [[UPPushGIOTrack alloc] init];
    });

    return track;
}

- (void)gioTrackWithEventId:(NSString *)eventId
                      msgID:(NSString *_Nullable)msgID
                 buttonName:(NSString *_Nullable)buttonName
                  hyperlink:(NSString *_Nullable)hyperlink
               contentTitle:(NSString *_Nullable)contentTitle
            contentSubtitle:(NSString *_Nullable)contentSubtitle
{
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:msgID ?: @"" forKey:@"msgID"];
    [param setObject:buttonName ?: @"" forKey:@"button_name"];
    [param setObject:UPPush_isEmptyString(hyperlink) ? @"null" : hyperlink forKey:@"hyperlink"];
    [param setObject:contentTitle ?: @"" forKey:@"content_title"];
    [param setObject:contentSubtitle ?: @"" forKey:@"content_subtitle"];

    [self.interceptorDelegate track:eventId param:param];
}

- (void)gioTrackWithEventID:(NSString *)eventId param:(NSDictionary *)param
{
    [self.interceptorDelegate track:eventId param:param];
}

/**
 打点
 @param eventName 事件标识，注意：eventName可能不是标准的事件id，因此不能直接传给GIO或Firebase进行埋点，需要在协议方法实现了进行具体的判断
 @param message 消息体
 @param extraInfo 其它附加参数
 */
- (void)trackEvent:(NSString *)eventName messageInfo:(id<UPPushMessageProtocol>)message extraInfo:(NSDictionary *)extraInfo
{
    NSDictionary *messageInfo = @{};
    if ([message isKindOfClass:[UPPushMessage class]]) {
        UPPushMessage *pushMessage = (UPPushMessage *)message;
        messageInfo = pushMessage.mj_keyValues;
    }
    [self.interceptorDelegate trackEvent:eventName messageInfo:messageInfo extraInfo:extraInfo];
}

@end

@implementation UPPushGIOTrack (Business)

+ (void)trackToastBeforeTap:(UPPushMessage *)message
{
    [UPPushGIOTrack.instance gioTrackWithEventID:@"MB16378"
                                           param:@{
                                               @"msgID" : message.messageID ?: [NSNull null],
                                               @"content_txt" : message.messageBody.messageUI.content ?: [NSNull null],
                                               @"hyperlink" : message.messageBody.extraData.pages.firstObject.url ?: @"null",
                                           }];
}

+ (void)trackToastAfterTap:(UPPushMessage *)message callID:(NSInteger)callID
{
    [UPPushGIOTrack.instance gioTrackWithEventID:@"MB16379"
                                           param:@{
                                               @"msgID" : message.messageID ?: [NSNull null],
                                               @"content_txt" : message.messageBody.messageUI.content ?: [NSNull null],
                                               @"hyperlink" : [message findPage:callID].url ?: @"null"
                                           }];
}

+ (void)trackJustReceive:(UPPushMessage *)message
{
    NSString *templateType = nil;
    BOOL notificationBar = !message.isCustom && !message.isLocal && !message.messageBody.extraParam.actWithNotify;
    BOOL isNotJump = UPPush_isEmptyString([message jumpPage]);
    BOOL isToast = [@[ @(MessageUI_Toast), @(MessageUI_TapToast) ] containsObject:@(message.messageBody.messageUI.showTypeAppearance)];
    BOOL isUI = isNotJump && message.messageBody.messageUI;
    BOOL isAlert = message.messageBody.messageUI.showTypeAppearance == MessageUI_Alert;

    if (isUI) {
        templateType = isAlert ? notificationBar ? @"通知-弹窗" : @"消息-弹窗" : isToast ? @"toast" : nil;
    }
    else if (notificationBar && !isNotJump) {
        templateType = @"通知-跳转";
    }

    [UPPushGIOTrack justReceiveTrackHandle:templateType];
    [UPPushGIOTrack.instance trackEvent:PUSH_EVENT_MESSAGE_SHOW
                            messageInfo:message
                              extraInfo:@{ @"title" : message.alertTitle,
                                           @"content" : message.alertBody }];
}

+ (void)justReceiveTrackHandle:(NSString *)templateType
{
    UPLogDebug(@"UPPush", @"<UPPush Debugging>trackJustReceive templateType：%@", templateType);
    if (UPPush_isEmptyString(templateType)) {
        return;
    }

    [UPPushGIOTrack.instance gioTrackWithEventID:@"MB17695"
                                           param:@{
                                               @"content_type" : templateType
                                           }];
}
@end
