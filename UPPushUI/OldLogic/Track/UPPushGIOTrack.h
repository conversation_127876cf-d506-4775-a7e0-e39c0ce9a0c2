//
//  UPPushGIOTrack.h
//  UPPush
//
//  Created by haier on 2020/10/10.
//  Copyright © 2020 路鹏. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPPushInterceptorProtocol.h"

#define PUSH_GIO_EVENTID @"MB10232"

#define PUSH_EVENT_MESSAGE_SHOW @"UpPushMessage_Recieve"
#define PUSH_EVENT_MESSAGE_CLICK @"UpPushMessage_Click"


@class UPPushMessage;
@protocol UPPushGIOTrackDelegate;

NS_ASSUME_NONNULL_BEGIN

@interface UPPushGIOTrack : NSObject

@property (nonatomic, weak) id<UPPushGIOTrackDelegate> interceptorDelegate;

+ (instancetype)instance;

- (void)gioTrackWithEventId:(NSString *)eventId
                      msgID:(NSString *_Nullable)msgID
                 buttonName:(NSString *_Nullable)buttonName
                  hyperlink:(NSString *_Nullable)hyperlink
               contentTitle:(NSString *_Nullable)contentTitle
            contentSubtitle:(NSString *_Nullable)contentSubtitle;

- (void)gioTrackWithEventID:(NSString *)eventId param:(NSDictionary *)param;

/**
 打点
 @param eventName 事件标识，注意：eventName可能不是标准的事件id，因此不能直接传给GIO或Firebase进行埋点，需要在协议方法实现里进行具体的判断
 @param message 消息体
 @param extraInfo 其它附加参数
 */
- (void)trackEvent:(NSString *)eventName messageInfo:(id<UPPushMessageProtocol>)message extraInfo:(NSDictionary *_Nullable)extraInfo;


@end

@protocol UPPushGIOTrackDelegate <NSObject>

- (void)track:(NSString *)eventID param:(NSDictionary *)param;

/**
 打点
 @param eventName 事件标识，注意：eventName可能不是标准的事件id，因此不能直接传给GIO或Firebase进行埋点，需要在协议方法实现了进行具体的判断
 @param messageInfo 消息体
 @param extraInfo 其它附加参数
 */
- (void)trackEvent:(NSString *)eventName messageInfo:(NSDictionary *)messageInfo extraInfo:(NSDictionary *)extraInfo;

@end

@interface UPPushGIOTrack (Business)

+ (void)trackToastBeforeTap:(UPPushMessage *)message;
+ (void)trackToastAfterTap:(UPPushMessage *)message callID:(NSInteger)callID;
+ (void)trackJustReceive:(UPPushMessage *)message;
@end
NS_ASSUME_NONNULL_END
