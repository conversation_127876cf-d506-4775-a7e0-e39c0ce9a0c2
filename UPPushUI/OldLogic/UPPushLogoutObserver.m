//
//  UPNLogoutObserver.m
//  mainbox
//
//  Created by ha<PERSON> on 2019/9/11.
//

#import "UPPushLogoutObserver.h"
#import "UPPushGIOTrack.h"
#import "UPPushMessage.h"
#import "UPPushAlertController.h"
#import <upnetwork/UPNetwork.h>
#import "UPPushStringTools.h"

#define UPLUS_USER_CHANGE_PWD @"UPLUS_USER_CHANGE_PWD"

@interface UPPushLogoutObserver ()
@end

@implementation UPPushLogoutObserver

+ (instancetype)instance
{
    static UPPushLogoutObserver *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[[self class] alloc] init];
    });
    return instance;
}

- (void)addLogoutObserver
{
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(changePassworkWithMessage:) name:UPLUS_USER_CHANGE_PWD object:nil];
}

- (void)removeObserver
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

//别的手机修改密码账号退出
- (void)changePassworkWithMessage:(NSNotification *)sender
{
    UPPushMessage *message = sender.object;
    if (!message) {
        return;
    }

    if (UPPush_isEmptyString(message.messageName) || ![message.messageName isEqualToString:UPLUS_USER_CHANGE_PWD]) {
        return;
    }

    if (!message.messageBody.extraData.api.params || message.messageBody.extraData.api.params.count == 0) {
        return;
    }

    NSString *clientID = [message.messageBody.extraData.api.params objectForKey:@"clientId"];
    if ([clientID isEqualToString:[UPNetworkSettings sharedSettings].clientID]) {
        return;
    }

    [self.delegate logoutForChangePassword:message];
}

@end
