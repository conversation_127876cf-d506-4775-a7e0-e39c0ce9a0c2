//
//  UPNDevControlDataHandler.m
//  mainbox
//
//  Created by YuMaoHua on 2017/8/28.
//  Copyright © 2017年 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPPushDevControlDataHandler.h"
#import <UPLog/UPLog.h>
#import "UPPushMessageExtension.h"

NSString *const kUPNMessage_DeviceControl = @"kUPNMessage_DeviceControl";

@interface UPPushDevControlDataHandler ()
@end

@implementation UPPushDevControlDataHandler

#pragma mark - Public Method
+ (instancetype)shareInstance
{
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[self alloc] init];
    });

    return instance;
}

- (void)postFristDevControlNotificationWithMessage:(UPPushMessage *)message
{
    if (!message.messageBody.extraData) {
        return;
    }

    if (!message.deviceControlsCompletion || message.deviceControlsCompletion.count == 0) {
        return;
    }

    NSInteger callId = message.messageBody.extraData.deviceControlList[0].callID;
    [self postNotificationWithMessage:message callId:callId];
}

- (void)devControlWithMessage:(UPPushMessage *)message callId:(NSInteger)callId
{
    BOOL __block isMatch = NO;
    [message.deviceControlsCompletion enumerateObjectsUsingBlock:^(id<UPPushMessageDeviceControlProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (obj.callID == callId) {
          isMatch = YES;
          *stop = YES;
      }
    }];

    if (!isMatch) {
        return;
    }

    [self postNotificationWithMessage:message callId:callId];
}

- (void)postNotificationWithMessage:(UPPushMessage *)message callId:(NSInteger)callId
{
    UPLogDebug(@"UPPush", @"<UPPush Debugging> message 广播处理：%@", message.messageID);
    dispatch_async(dispatch_get_main_queue(), ^{
      [[NSNotificationCenter defaultCenter] postNotificationName:kUPNMessage_DeviceControl
                                                          object:nil
                                                        userInfo:@{ @"message" : message,
                                                                    @"callId" : @(callId) }];
    });
}


@end
