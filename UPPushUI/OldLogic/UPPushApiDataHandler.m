//
//  UPNApiDataHandler.m
//  mainbox
//
//  Created by ha<PERSON> on 2019/10/9.
//

#import "UPPushApiDataHandler.h"
#import <UPLog/UPLog.h>
#import "UPPushStringTools.h"

@implementation UPPushApiDataHandler

+ (instancetype)shareInstance
{
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[self alloc] init];
    });

    return instance;
}

- (void)postNotificationWithMessage:(UPPushMessage *)message
{
    if (UPPush_isEmptyString(message.messageName)) {
        return;
    }
    UPLogDebug(@"UPPush", @"<UPPush Debugging> message 广播处理：%@", message.messageID);
    dispatch_async(dispatch_get_main_queue(), ^{
      [[NSNotificationCenter defaultCenter] postNotificationName:message.messageName
                                                          object:message];
    });
}

- (void)apiWithMessage:(UPPushMessage *)message callId:(NSInteger)callId
{
    if (message.messageBody.extraData.api.callID == callId) {
        [self postNotificationWithMessage:message];
    }
}

@end
