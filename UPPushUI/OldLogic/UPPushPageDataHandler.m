//
//  UPNToPageHandler.m
//  mainbox
//
//  Created by chi on 2017/8/18.
//  Copyright © 2017年 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPPushPageDataHandler.h"
#import <UPVDN/UPVDNManager.h>
#import <UPVDN/UPVDN.h>
#import "UPPushHandlerTool.h"
#import "UPPushMessageExtension.h"
#import "UPPushStringTools.h"

@interface UPPushPageDataHandler ()
@end

@implementation UPPushPageDataHandler

#pragma mark - Public Method
+ (instancetype)shareInstance
{
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[self alloc] init];
    });

    return instance;
}

- (void)directJumpHandlerWithMessage:(UPPushMessage *)message
{
    dispatch_async(dispatch_get_main_queue(), ^{
      [UPVDNManager.shareManager.vdnDomain goToPage:[message jumpPage]
                                               flag:VdnPageFlagPush
                                         parameters:@{ @"hidesBottomBarWhenPushed" : @"1" }
                                           complete:nil
                                              error:nil];
    });
}

- (void)gotoPageWithMessage:(UPPushMessage *)message callId:(NSInteger)callId
{
    if (message.messageBody.extraData && message.pagesCompletion) {
        __block id<UPPushMessagePageProtocol> page = nil;
        [message.pagesCompletion enumerateObjectsUsingBlock:^(id<UPPushMessagePageProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          if (obj.callID == callId) {
              page = obj;
              *stop = YES;
          }
        }];
        if (!page) {
            return;
        }

        NSString *url = page.url;
        if (!UPPush_isEmptyString(url)) {
            [self turnToPage:url params:page.params];
        }
    }
}
- (void)turnToPage:(NSString *)url params:(NSDictionary *)params
{
    NSMutableDictionary *paras = [params mutableCopy] ?: [@{} mutableCopy];
    [paras setObject:@"1" forKey:@"hidesBottomBarWhenPushed"];
    dispatch_async(dispatch_get_main_queue(), ^{
      [UPVDNManager.shareManager.vdnDomain goToPage:url flag:VdnPageFlagPush parameters:paras complete:nil error:NULL];
    });
}

@end
