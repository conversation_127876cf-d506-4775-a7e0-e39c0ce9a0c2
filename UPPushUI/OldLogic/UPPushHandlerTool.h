//
//  UPPushHandlerTool.h
//  mainbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/28.
//  Copyright © 2017年 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPPushMessage.h"

@interface UPPushHandlerTool : NSObject

+ (BOOL)duplicateMessageHandle:(UPPushMessage *)message;

+ (void)transformToLocolNotification:(UPPushMessage *)message;

+ (void)readMessage:(UPPushMessage *)message;

+ (BOOL)isMessageShowing:(NSString *)messageId;

+ (void)messageDidShowing:(NSString *)messageId;

+ (void)messageDidCancelShow:(NSString *)messageId;

+ (BOOL)isMultiLanguageEnvironment;
@end
