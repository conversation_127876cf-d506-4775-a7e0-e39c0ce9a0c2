//
//  UPNDevControlDataHandler.h
//  mainbox
//
//  Created by YuMaoHua on 2017/8/28.
//  Copyright © 2017年 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPPushMessage.h"

@interface UPPushDevControlDataHandler : NSObject

+ (instancetype)shareInstance;

//直接分发设备控制通知的方法
- (void)postFristDevControlNotificationWithMessage:(UPPushMessage *)message;

//点击按钮分发设备控制通知的方法
- (void)devControlWithMessage:(UPPushMessage *)message callId:(NSInteger)callId;

@end
