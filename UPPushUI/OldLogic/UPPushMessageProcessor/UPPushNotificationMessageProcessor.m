//
//  UPNNotificationMessageHander.m
//  mainbox
//
//  Created by haier on 2019/9/25.
//

#import "UPPushNotificationMessageProcessor.h"
#import <UPLog/UPLog.h>
#import "UPPushHandlerTool.h"
#import "UPPushCustomizedMessageProcessor.h"
#import "UPPushAppManager.h"
#import "UPPushMessageExtension.h"

@implementation UPPushNotificationMessageProcessor

- (void)handleMessage:(id<UPPushMessageProtocol>)message
{
    if ([(UPPushMessage *)message isNeedTransformToLocolNotification]) {
        UPLogDebug(@"UPPush", @"<UPPush Debugging> 将推送通知转换为本地通知：%@", message.messageID);
        [UPPushHandlerTool transformToLocolNotification:message];
        return;
    }

    UPPushCustomizedMessageProcessor *handler = [[UPPushCustomizedMessageProcessor alloc] init];
    handler.delegate = self.delegate;
    [handler handleMessage:message];
}

@end
