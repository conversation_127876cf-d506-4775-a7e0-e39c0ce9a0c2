//
//  UPPushMessageBaseHandle.h
//  UPPushUI
//
//  Created by 吴子航 on 2022/5/9.
//

#import <Foundation/Foundation.h>
#import "UPPushMessage.h"

NS_ASSUME_NONNULL_BEGIN

@protocol UPPushMessageProcessorDelegate;

@interface UPPushMessageProcessor : NSObject

@property (nonatomic, weak) id<UPPushMessageProcessorDelegate> delegate;

- (void)handleMessage:(id<UPPushMessageProtocol>)message;

@end

@protocol UPPushMessageProcessorDelegate <NSObject>

- (BOOL)customHandleDirectJumpMessage:(id<UPPushMessageProtocol>)message;
- (void)handleDirectJumpMessage:(id<UPPushMessageProtocol>)message;

@end

NS_ASSUME_NONNULL_END
