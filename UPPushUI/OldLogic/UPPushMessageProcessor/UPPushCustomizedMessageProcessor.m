//
//  UPNCustomizedMessageHander.m
//  mainbox
//
//  Created by haier on 2019/9/25.
//

#import "UPPushCustomizedMessageProcessor.h"
#import <UPLog/UPLog.h>
#import "UPPushHandlerTool.h"
#import "UPPushViewMsgComponent.h"
#import "UPPushNoViewMsgComponent.h"
#import "UPPushPageDataHandler.h"
#import "UPPushMessageExtension.h"
#import "UPPushStringTools.h"

@implementation UPPushCustomizedMessageProcessor

- (void)handleMessage:(id<UPPushMessageProtocol>)message
{
    if (!UPPush_isEmptyString([(UPPushMessage *)message jumpPage])) {
        [UPPushHandlerTool readMessage:message];
        [self handleDirectJumpMessage:message];
        UPLogDebug(@"UPPush", @"<UPPush Debugging> 消息处理为直接跳转页面：%@", message.messageID);
        return;
    }

    if (message.messageBody.messageUI) {
        UPPushViewMsgComponent *viewComponent = [[UPPushViewMsgComponent alloc] init];
        [viewComponent handleViewMessage:message];
    }
    else {
        UPPushNoViewMsgComponent *noViewComponent = [[UPPushNoViewMsgComponent alloc] init];
        [noViewComponent handleMessage:message];
    }
}

#pragma mark - parivate
- (void)handleDirectJumpMessage:(UPPushMessage *)message
{
    if ([self.delegate customHandleDirectJumpMessage:message]) {
        [self.delegate handleDirectJumpMessage:message];
    }
    else {
        [[UPPushPageDataHandler shareInstance] directJumpHandlerWithMessage:message];
    }
}
@end
