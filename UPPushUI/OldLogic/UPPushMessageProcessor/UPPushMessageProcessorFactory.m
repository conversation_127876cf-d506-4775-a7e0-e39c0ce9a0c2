//
//  UPPushMessageProcessorFactory.m
//  UPPushUI
//
//  Created by 吴子航 on 2022/5/9.
//

#import "UPPushMessageProcessorFactory.h"
#import <UPLog/UPLog.h>
#import "UPPushCustomizedMessageProcessor.h"
#import "UPPushNotificationMessageProcessor.h"

@implementation UPPushMessageProcessorFactory

+ (UPPushMessageProcessor *)messageProcessor:(id<UPPushMessageProtocol>)message
{
    UPPushMessageProcessor *handler = nil;
    if (message.isCustom) {
        UPLogDebug(@"UPPush", @"<UPPush Debugging> 开始处理自定义消息：%@", message.messageID);
        handler = [[UPPushCustomizedMessageProcessor alloc] init];
    }
    else {
        UPLogDebug(@"UPPush", @"<UPPush Debugging> 开始处理推送消息：%@", message.messageID);
        handler = [[UPPushNotificationMessageProcessor alloc] init];
    }

    return handler;
}
@end
