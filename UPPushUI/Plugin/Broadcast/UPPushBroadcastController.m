//
//  UPPushBroadcastController.m
//  UPPushUI
//
//  Created by osiris on 2020/11/12.
//

#import "UPPushBroadcastController.h"
#import "UPPushNotification.h"

@interface UPPushBroadcastController ()

@property (nonatomic, strong) NSNotificationQueue *notificationQueue;

@end

@implementation UPPushBroadcastController

+ (instancetype)instance
{
    static dispatch_once_t onceToken;
    static UPPushBroadcastController *controller = nil;
    dispatch_once(&onceToken, ^{
      controller = [[UPPushBroadcastController alloc] init];
    });

    return controller;
}

- (instancetype)init
{
    if (self = [super init]) {
        _notificationQueue = [NSNotificationQueue defaultQueue];
    }

    return self;
}

#pragma mark - public method
- (void)enqueueNotification:(UPPushNotification *)pushNotification
{
    if (!pushNotification) {
        return;
    }
    NSNotification *notifiction = [self transformNotification:pushNotification];
    dispatch_async(dispatch_get_main_queue(), ^{
      [self.notificationQueue enqueueNotification:notifiction
                                     postingStyle:[self transformPostingStyle:pushNotification.postingStyle]
                                     coalesceMask:[self transformCoalescingMode:pushNotification.coalescingMode]
                                         forModes:nil];
    });
}

- (void)dequeueNotification:(UPPushNotification *)pushNotification
{
    if (!pushNotification) {
        return;
    }
    NSNotification *notifiction = [self transformNotification:pushNotification];
    dispatch_async(dispatch_get_main_queue(), ^{
      [self.notificationQueue dequeueNotificationsMatching:notifiction
                                              coalesceMask:[self transformCoalescingMode:pushNotification.coalescingMode]];
    });
}

#pragma mark - private method
- (NSNotification *)transformNotification:(UPPushNotification *)pushNotification
{
    NSNotification *notification = [NSNotification notificationWithName:pushNotification.name object:pushNotification.object userInfo:pushNotification.userInfo];
    return notification;
}

- (NSPostingStyle)transformPostingStyle:(UPPushPostingStyle)pushPostingStyle
{
    switch (pushPostingStyle) {
        case UPPushPosting_Now:
            return NSPostNow;
        case UPPushPosting_Later:

            return NSPostASAP;
        case UPPushPosting_Idle:
            return NSPostWhenIdle;
        default:
            break;
    }
}

- (NSNotificationCoalescing)transformCoalescingMode:(UPPushCoalescingMode)coalescingMode
{
    switch (coalescingMode) {
        case UPPushCoalescing_None:
            return NSNotificationNoCoalescing;
        case UPPushCoalescing_Name:
            return NSNotificationCoalescingOnName;
        case UPPushCoalescing_Sender:
            return NSNotificationCoalescingOnSender;
        default:
            break;
    }
}
@end
