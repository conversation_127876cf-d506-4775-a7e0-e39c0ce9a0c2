//
//  UPPushBroadcastController.h
//  UPPushUI
//
//  Created by osiris on 2020/11/12.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class UPPushNotification;
@interface UPPushBroadcastController : NSObject

+ (instancetype)instance;

- (void)enqueueNotification:(UPPushNotification *)notification;
- (void)dequeueNotification:(UPPushNotification *)notification;
@end

NS_ASSUME_NONNULL_END
