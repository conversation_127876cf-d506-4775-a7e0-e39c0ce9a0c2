//
//  UPPushNotification.m
//  UPPushUI
//
//  Created by osiris on 2020/11/12.
//

#import "UPPushNotification.h"

@interface UPPushNotification ()

@property (nonatomic, strong) NSString *name;
@property (nonatomic, strong) id object;
@property (nonatomic, strong) NSDictionary *userInfo;
@property (nonatomic, assign) UPPushPostingStyle postingStyle;
@property (nonatomic, assign) UPPushCoalescingMode coalescingMode;

@end

@implementation UPPushNotification

- (instancetype)initWithName:(NSString *)name
{
    return [self initWithName:name object:nil userInfo:nil];
}

- (instancetype)initWithName:(NSString *)name object:(id)object
{
    return [self initWithName:name object:object userInfo:nil];
}

- (instancetype)initWithName:(NSString *)name userInfo:(NSDictionary *)userInfo
{
    return [self initWithName:name object:nil userInfo:userInfo];
}

- (instancetype)initWithName:(NSString *)name object:(id)object userInfo:(NSDictionary *)userInfo
{
    return [self initWithName:name object:object userInfo:userInfo posting:UPPushPosting_Now coalescing:UPPushCoalescing_Name];
}

- (instancetype)initWithName:(NSString *)name object:(id)object userInfo:(NSDictionary *)userInfo posting:(UPPushPostingStyle)postingStyle coalescing:(UPPushCoalescingMode)coalescingMode
{
    if (self = [super init]) {
        _name = name;
        _object = object;
        _userInfo = userInfo;
        _postingStyle = postingStyle;
        _coalescingMode = coalescingMode;
    }

    return self;
}
@end
