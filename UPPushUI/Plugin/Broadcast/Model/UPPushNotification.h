//
//  UPPushNotification.h
//  UPPushUI
//
//  Created by osiris on 2020/11/12.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSInteger, UPPushPostingStyle) {
    UPPushPosting_Now,
    UPPushPosting_Later,
    UPPushPosting_Idle
};
typedef NS_ENUM(NSInteger, UPPushCoalescingMode) {
    UPPushCoalescing_None,
    UPPushCoalescing_Name,
    UPPushCoalescing_Sender
};
NS_ASSUME_NONNULL_BEGIN

@interface UPPushNotification : NSObject

@property (nonatomic, strong, readonly) NSString *name;
@property (nonatomic, strong, readonly) id object;
@property (nonatomic, strong, readonly) NSDictionary *userInfo;
@property (nonatomic, assign, readonly) UPPushPostingStyle postingStyle;
@property (nonatomic, assign, readonly) UPPushCoalescingMode coalescingMode;

- (instancetype)initWithName:(NSString *)name;

- (instancetype)initWithName:(NSString *)name
                    userInfo:(nullable NSDictionary *)userInfo;

- (instancetype)initWithName:(NSString *)name
                      object:(nullable id)object;

- (instancetype)initWithName:(NSString *)name
                      object:(nullable id)object
                    userInfo:(nullable NSDictionary *)userInfo;

- (instancetype)initWithName:(NSString *)name
                      object:(nullable id)object
                    userInfo:(nullable NSDictionary *)userInfo
                     posting:(UPPushPostingStyle)postingStyle
                  coalescing:(UPPushCoalescingMode)coalescingMode;
@end

NS_ASSUME_NONNULL_END
