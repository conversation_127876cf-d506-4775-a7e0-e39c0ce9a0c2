//
//  UPPushProgressShow.m
//  Uplus
//
//  Created by 郑振兴 on 15/1/12.
//  Copyright (c) 2015年 北京海尔广科数字技术有限公司－郑振兴. All rights
//  reserved.
//

#import "UPPushProgressShow.h"
#import "UPPushToast.h"
#import "UPPushLoading.h"
#import "UPPushHandlerTool.h"
#import "UPPushLocalizationConfig.h"

@implementation UPPushProgressShow

+ (void)setUPPushStyle
{
    UPPushToast *toast = [UPPushToast shareManager];
    toast.canShake = YES;
    toast.duration = 1;
}

+ (void)showCustomLoading
{
    [self performSelectorOnMainThread:@selector(showLoding) withObject:nil waitUntilDone:NO];
}

+ (void)showLoding
{
    [[UPPushLoading shareManager] showLoading];
    [self performSelector:@selector(delayDismiss) withObject:nil afterDelay:30];
}

+ (void)showSuccess:(NSString *)successMessage
{
    [self performSelectorOnMainThread:@selector(showTextMsg:) withObject:successMessage waitUntilDone:NO];
}

+ (void)showError:(NSString *)errorMessage
{
    [self performSelectorOnMainThread:@selector(showTextMsg:) withObject:errorMessage waitUntilDone:NO];
}

+ (void)showInfo:(NSString *)infoMessage
{
    [self performSelectorOnMainThread:@selector(showTextMsg:) withObject:infoMessage waitUntilDone:NO];
}

+ (void)showText:(NSString *)message
{
    [self performSelectorOnMainThread:@selector(showTextMsg:) withObject:message waitUntilDone:NO];
}

+ (void)showTextMsg:(NSObject *)obj
{
    if ([obj isKindOfClass:NSString.class]) {
        NSString *msg = (NSString *)obj;
        [[UPPushToast shareManager] showWithText:msg];
    }
}

+ (void)dismiss
{
    [self performSelectorOnMainThread:@selector(dismissLoading) withObject:nil waitUntilDone:NO];
}

+ (void)dismissLoading
{
    [[UPPushLoading shareManager] dismissWithBlock:^(NSInteger loadingNum) {
      if (loadingNum == 0) {
          [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(delayDismiss) object:nil];
      }
    }];
}

+ (void)delayDismiss
{
    [self performSelectorOnMainThread:@selector(delayDismissMain) withObject:nil waitUntilDone:NO];
}

+ (void)delayDismissMain
{
    if ([[UPPushLoading shareManager] isVisible]) {
        NSString *timeoutText = @"操作超时";
        if (UPPushHandlerTool.isMultiLanguageEnvironment) {
            timeoutText = UPPushLocalizationConfig.shareInstance.loadingTimeout;
        }

        [[UPPushLoading shareManager] dismissWithBlock:nil];
        [[UPPushToast shareManager] showWithText:timeoutText];
    }
}


@end
