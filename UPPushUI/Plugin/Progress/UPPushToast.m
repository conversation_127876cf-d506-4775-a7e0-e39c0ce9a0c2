//
//  UPPushToast.m
//  AFNetworking
//
//  Created by osiris on 2019/5/20.
//

#import "UPPushToast.h"
#import <UHMasonry/UHMasonry.h>
#import "UPPushLoading.h"
#import "UPPushMacros.h"

@interface UPPushToast () <CAAnimationDelegate>

@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UILabel *contentLabel;
@property (nonatomic, assign) NSInteger stuate; //弹框个数

@end

@implementation UPPushToast

+ (instancetype)shareManager
{
    static UPPushToast *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      manager = [[UPPushToast alloc] init];
    });
    return manager;
}

- (id)init
{
    if (self == [super init]) {
        self.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
        [self createUI];
    }
    return self;
}

- (void)createUI
{
    _contentView = [[UIView alloc] init];
    _contentView.backgroundColor = RGBACOLOR(0, 0, 0, 0.6);
    _contentView.layer.cornerRadius = 5;
    _contentView.clipsToBounds = YES;
    [self addSubview:_contentView];
    [_contentView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.left.uh_equalTo(73.5);
      make.right.uh_equalTo(-73.5);
      //      make.width.uh_equalTo(228);
      make.height.uh_greaterThanOrEqualTo(48);
      make.centerX.centerY.uh_equalTo(0);
    }];

    _contentLabel = [[UILabel alloc] init];
    _contentLabel.textColor = [UIColor whiteColor];
    _contentLabel.font = [UIFont systemFontOfSize:17];
    _contentLabel.textAlignment = NSTextAlignmentCenter;
    _contentLabel.numberOfLines = 0;
    [_contentView addSubview:_contentLabel];
    [_contentLabel uh_makeConstraints:^(UHConstraintMaker *make) {
      make.left.uh_equalTo(12);
      make.right.uh_equalTo(-12);
      make.top.uh_equalTo(13);
      make.bottom.uh_equalTo(-13);
    }];

    self.duration = 1.0;
    self.canShake = NO;
    self.contentView.alpha = 0.0;
    _stuate = 0;
}

#pragma mark - private

- (void)showAction
{
    _stuate++;
    UIView *window = [UIApplication sharedApplication].keyWindow;
    [window addSubview:self];
    for (UIView *subView in window.subviews) {
        if ([subView isKindOfClass:[UPPushLoading class]]) {
            [[UPPushLoading shareManager] dismissForever];
            [self dismissAction];
            return;
        }
    }

    if (_stuate > 1) {
        [self dismissAction];
        return;
    }

    if (self.canShake) {

        self.contentView.alpha = 1;
        CABasicAnimation *shakeAnimation =
            [CABasicAnimation animationWithKeyPath:@"transform.scale"];
        shakeAnimation.duration = 0.2;
        shakeAnimation.fromValue = @(1.0);
        shakeAnimation.toValue = @(1.05);
        shakeAnimation.autoreverses = YES;
        shakeAnimation.delegate = self;
        [self.contentView.layer addAnimation:shakeAnimation forKey:@"toast"];
    }
    else {

        [UIView animateWithDuration:0.2
            animations:^{
              self.contentView.alpha = 1.0;
            }
            completion:^(BOOL finished) {
              [self dismissAction];
            }];
    }
}

- (void)dismissAction
{
    self.contentView.alpha = 1.0;
    [UIView animateWithDuration:0.3
        delay:self.duration
        options:UIViewAnimationOptionAllowUserInteraction
        animations:^{
          self.contentView.alpha = 0.0;
        }
        completion:^(BOOL finished) {
          [self dismiss];
        }];
}

#pragma mark - CAAnimationDelegate

- (void)animationDidStop:(CAAnimation *)anim finished:(BOOL)flag
{
    [self dismissAction];
}

#pragma mark - public

- (void)showWithText:(NSString *)text
{
    self.contentLabel.text = text;
    [self showAction];
}

- (void)dismiss
{
    if (self.stuate <= 1) {
        [self dismissForever];
    }
    else {
        self.stuate--;
    }
}

- (void)dismissForever
{
    self.stuate = 0;
    [self removeFromSuperview];
}

@end
