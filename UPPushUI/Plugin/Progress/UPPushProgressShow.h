//
//  UPPushProgressShow.h
//  Uplus
//
//  Created by 郑振兴 on 15/1/12.
//  Copyright (c) 2015年 北京海尔广科数字技术有限公司－郑振兴. All rights
//  reserved.
//

#import <Foundation/Foundation.h>

@interface UPPushProgressShow : NSObject

+ (void)setUPPushStyle;
+ (void)showCustomLoading;
/** 成功提示 */
+ (void)showSuccess:(NSString *)successMessage;
/** 失败提示 */
+ (void)showError:(NSString *)errorMessage;
/** 警告提示 */
+ (void)showInfo:(NSString *)infoMessage;
/** 文字提示 */
+ (void)showText:(NSString *)message;

+ (void)dismiss;

@end
