//
//  UPLoading.h
//  AFNetworking
//
//  Created by 訾玉洁 on 2019/5/21.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^DismissBlock)(NSInteger loadingNum);

@interface UPPushLoading : UIView

+ (instancetype)shareManager;

/** 弹出loading框 */
- (void)showLoading;

- (void)dismissWithBlock:(__nullable DismissBlock)block;
- (void)dismissForever;

/** loading有效 */
- (BOOL)isVisible;
@end

@interface UPPushLoadingActionView : UIView

@end

NS_ASSUME_NONNULL_END
