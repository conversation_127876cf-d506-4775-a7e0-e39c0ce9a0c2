//
//  UPPushLoading.m
//  AFNetworking
//
//  Created by 訾玉洁 on 2019/5/21.
//

#import "UPPushLoading.h"
#import <UHMasonry/UHMasonry.h>
#import "UPPushToast.h"
#import "UPPushMacros.h"
#import "UPPushHandlerTool.h"
#import "UPPushLocalizationConfig.h"

@interface UPPushLoading ()

@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UPPushLoadingActionView *actionView;
@property (nonatomic, strong) UILabel *loadingLabel;
@property (nonatomic, assign) NSInteger stuate;

@end

@implementation UPPushLoading

+ (instancetype)shareManager
{
    static UPPushLoading *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      manager = [[UPPushLoading alloc] init];
    });
    return manager;
}

- (id)init
{
    if (self == [super init]) {
        self.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
        [self createUI];
    }
    return self;
}

- (void)createUI
{
    _contentView = [[UIView alloc] init];
    _contentView.backgroundColor = RGBACOLOR(0, 0, 0, 0.6);
    _contentView.layer.cornerRadius = 5;
    _contentView.clipsToBounds = YES;
    [self addSubview:_contentView];
    [_contentView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.width.uh_equalTo(78);
      make.height.uh_equalTo(90);
      make.centerX.centerY.uh_equalTo(0);
    }];

    NSString *loadingText = @"加载中...";
    if (UPPushHandlerTool.isMultiLanguageEnvironment) {
        loadingText = UPPushLocalizationConfig.shareInstance.loading;
    }
    _loadingLabel = [[UILabel alloc] init];
    _loadingLabel.textColor = [UIColor whiteColor];
    _loadingLabel.font = [UIFont systemFontOfSize:14];
    _loadingLabel.textAlignment = NSTextAlignmentCenter;
    _loadingLabel.text = loadingText;
    [_contentView addSubview:_loadingLabel];
    [_loadingLabel uh_makeConstraints:^(UHConstraintMaker *make) {
      make.left.uh_equalTo(10);
      make.right.uh_equalTo(-10);
      make.bottom.uh_equalTo(-10);
      make.height.uh_equalTo(20);
    }];

    _actionView = [[UPPushLoadingActionView alloc] init];
    _actionView.backgroundColor = [UIColor clearColor];
    [_contentView addSubview:_actionView];
    [_actionView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.top.uh_equalTo(13);
      make.centerX.uh_equalTo(0);
      make.width.height.uh_equalTo(38);
    }];

    _stuate = 0;
}

- (void)showLoading
{
    _stuate++;

    UIView *window = [UIApplication sharedApplication].delegate.window;
    [window addSubview:self];
    for (UIView *subView in window.subviews) {
        if ([subView isKindOfClass:[UPPushToast class]]) {
            [[UPPushToast shareManager] dismissForever];
            [self becomeAction];
            return;
        }
    }

    if (_stuate > 1) {
        return;
    }

    [UIView animateWithDuration:0.2
        animations:^{
          self.contentView.alpha = 1.0;
        }
        completion:^(BOOL finished) {
          [self becomeAction];
        }];
}

- (void)becomeAction
{
    self.contentView.alpha = 1.0;
    CABasicAnimation *ringAnimation =
        [CABasicAnimation animationWithKeyPath:@"transform.rotation"];
    ringAnimation.duration = 2;
    ringAnimation.toValue = @(M_PI * 2.0 * 2.0f);
    ringAnimation.repeatCount = HUGE_VAL;
    [self.actionView.layer addAnimation:ringAnimation forKey:@"ring"];
}

- (void)dismissWithBlock:(__nullable DismissBlock)block
{
    if (self.stuate <= 1) {
        [UIView animateWithDuration:0.2
            animations:^{
              self.contentView.alpha = 0.0;
            }
            completion:^(BOOL finished) {
              if (block) {
                  block(0);
              }
              [self dismissForever];
            }];
    }
    else {
        self.stuate--;
        if (block) {
            block(self.stuate);
        }
    }
}

- (void)dismissForever
{
    self.stuate = 0;
    [self removeFromSuperview];
}

- (BOOL)isVisible
{
    return self.superview != nil ? YES : NO;
}

@end

#pragma mark - 中间的圈圈

@implementation UPPushLoadingActionView

- (void)drawRect:(CGRect)rect
{
    [super drawRect:rect];

    //画圆 38x38
    CGContextRef ctx = UIGraphicsGetCurrentContext();
    //绘制路径:ctx,中心x，中心y，半径、起点弧度、终点弧度、画的方向0逆1正
    CGContextAddArc(ctx, 19, 19, 18, 0, M_PI * 2, 1);
    //颜色
    CGContextSetRGBStrokeColor(ctx, 1, 1, 1, 1);
    //线条宽度
    CGContextSetLineWidth(ctx, 2);
    //渲染上下文
    CGContextStrokePath(ctx);

    //画弧线
    CGContextRef ctx1 = UIGraphicsGetCurrentContext();
    //绘制路径:ctx,中心x，中心y，半径、起点弧度、终点弧度、画的方向0正1逆
    CGContextAddArc(ctx1, 19, 19, 18, 0, M_PI / 4, 0);
    //颜色
    CGContextSetRGBStrokeColor(ctx1, 34.0 / 255, 131.0 / 255, 226.0 / 255, 1);
    //线条宽度
    CGContextSetLineWidth(ctx1, 2);
    //渲染上下文
    CGContextStrokePath(ctx1);
}

@end
