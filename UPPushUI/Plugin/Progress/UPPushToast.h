//
//  UPPushToast.h
//  AFNetworking
//
//  Created by osir<PERSON> on 2019/5/20.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPPushToast : UIView

+ (instancetype)shareManager;

/** 弹出停止时间 */
@property (nonatomic, assign) float duration;
/** 可以抖动 */
@property (nonatomic, assign) BOOL canShake;

/** 提示文字 */
- (void)showWithText:(NSString *)text;
- (void)dismiss;
- (void)dismissForever;

@end

NS_ASSUME_NONNULL_END
