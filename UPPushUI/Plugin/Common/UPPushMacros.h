//
//  UPPushProgressMacros.h
//  UPPush
//
//  Created by osiris on 2020/11/4.
//

#ifndef UPPushMacros_h
#define UPPushMacros_h

//屏幕尺寸相关
#define SCREEN_WIDTH ([UIScreen mainScreen].bounds.size.width)
#define SCREEN_HEIGHT ([UIScreen mainScreen].bounds.size.height)
#define IOS_VERSION [[[UIDevice currentDevice] systemVersion] floatValue]
#define SCREEN_SCALE [UIScreen mainScreen].bounds.size.width / 320 //屏幕适配比例
#define SCREEN_SCALE_375 [UIScreen mainScreen].bounds.size.width / 375 //屏幕适配比例

#define is_iPhoneXSerious @available(iOS 11.0, *) && UIApplication.sharedApplication.keyWindow.safeAreaInsets.bottom > 0.0
#define StatusBarHeight (is_iPhoneXSerious ? 44.0f : 20.0f)
#define StatusBarExtraHeight (is_iPhoneXSerious ? 24.0f : 0.0f)
#define TabbarHeight (is_iPhoneXSerious ? (49.f + 34.f) : 49.f)
#define TabbarSafeBottomMargin (is_iPhoneXSerious ? 34.f : 0.f)

// 颜色(RGB)
#define RGBCOLOR(r, g, b) [UIColor colorWithRed:(r) / 255.0f green:(g) / 255.0f blue:(b) / 255.0f alpha:1]
#define RGBACOLOR(r, g, b, a) [UIColor colorWithRed:(r) / 255.0f green:(g) / 255.0f blue:(b) / 255.0f alpha:(a)]
// RGB颜色转换（16进制->10进制）
#define UIColorFromRGB(rgbValue)                                         \
    [UIColor colorWithRed:((float)((rgbValue & 0xFF0000) >> 16)) / 255.0 \
                    green:((float)((rgbValue & 0xFF00) >> 8)) / 255.0    \
                     blue:((float)(rgbValue & 0xFF)) / 255.0             \
                    alpha:1.0]

#endif /* UPPushProgressMacros_h */
