//
//  UPPushAppManagerProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/19.
//

#import <UIKit/UIkit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 @enum 推送模块添加对系统通知观察者的观察方向
 */
typedef NS_ENUM(NSInteger, UPPushAppObserveAspect) {
    /**
     App生命周期
     */
    UPPushApp_Aspect_LifeCycle
};

/**
 推送模块App系统通知观察者协议
 */
@protocol UPPushAppManagerObserver <NSObject>

#pragma mark - Life Cycle
- (void)onAppDidBecomeActive;
- (void)onAppDidEnterBackground;
- (void)onAppWillEnterForeground;
- (void)onAppWillResignActive;
- (void)onAppWillTerminate;

@end

/**
 推送模块系统通知观察者管理器协议
 */
@protocol UPPushAppManagerProtocol <NSObject>

/**
 当前App系统声明周期状态
 */
@property (nonatomic, assign, readonly) UIApplicationState appState;

/**
 添加观察者
 @param observer 观察者
 @param aspect 观察方面
 */
- (void)addObserver:(id<UPPushAppManagerObserver>)observer aspect:(UPPushAppObserveAspect)aspect;

/**
 删除观察者
 @param observer 观察者
 @param aspect 观察方面
 */
- (void)removeObserver:(id<UPPushAppManagerObserver>)observer aspect:(UPPushAppObserveAspect)aspect;

@end

NS_ASSUME_NONNULL_END
