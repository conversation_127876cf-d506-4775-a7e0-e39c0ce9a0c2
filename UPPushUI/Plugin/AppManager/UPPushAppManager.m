//
//  UPPushAppManager.m
//  UPPush
//
//  Created by osir<PERSON> on 2020/10/19.
//

#import "UPPushAppManager.h"

@interface UPPushAppManager ()

@property (nonatomic, strong) NSDictionary<id<NSCopying>, NSHashTable<id<UPPushAppManagerObserver>> *> *observers;
@property (nonatomic, strong) NSDictionary<NSString *, NSString *> *appLifeCycleSelectorMap;

@end

@implementation UPPushAppManager

+ (instancetype)instance
{
    static dispatch_once_t onceToken;
    static UPPushAppManager *appManager;
    dispatch_once(&onceToken, ^{
      if (!appManager) {
          appManager = [[UPPushAppManager alloc] init];
      }
    });

    return appManager;
}

- (instancetype)init
{
    if (self = [super init]) {
        _observers = @{
            @(UPPushApp_Aspect_LifeCycle) : [NSHashTable weakObjectsHashTable]
        };
        [self assembleInvocationMap];
        [self registerAppLifeCycleNotification];
    }

    return self;
}


#pragma mark - property
- (UIApplicationState)appState
{
    return [UIApplication sharedApplication].applicationState;
}

#pragma mark - public method
- (void)addObserver:(id<UPPushAppManagerObserver>)observer aspect:(UPPushAppObserveAspect)aspect
{
    if ([_observers[@(aspect)].allObjects containsObject:observer]) {
        return;
    }
    [_observers[@(aspect)] addObject:observer];
}

- (void)removeObserver:(id<UPPushAppManagerObserver>)observer aspect:(UPPushAppObserveAspect)aspect
{
    if (![_observers[@(aspect)].allObjects containsObject:observer]) {
        return;
    }
    [_observers[@(aspect)] removeObject:observer];
}

#pragma mark - private methods
- (void)assembleInvocationMap
{
    _appLifeCycleSelectorMap = @{
        UIApplicationDidBecomeActiveNotification : NSStringFromSelector(@selector(onAppDidBecomeActive)),
        UIApplicationDidEnterBackgroundNotification : NSStringFromSelector(@selector(onAppDidEnterBackground)),
        UIApplicationWillEnterForegroundNotification : NSStringFromSelector(@selector(onAppWillEnterForeground)),
        UIApplicationWillResignActiveNotification : NSStringFromSelector(@selector(onAppWillResignActive)),
        UIApplicationWillTerminateNotification : NSStringFromSelector(@selector(onAppWillTerminate)),
    };
}

- (void)registerAppLifeCycleNotification
{
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appLifeCycleChangeHandler:) name:UIApplicationDidBecomeActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appLifeCycleChangeHandler:) name:UIApplicationDidEnterBackgroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appLifeCycleChangeHandler:) name:UIApplicationWillEnterForegroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appLifeCycleChangeHandler:) name:UIApplicationWillResignActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appLifeCycleChangeHandler:) name:UIApplicationWillTerminateNotification object:nil];
}

#pragma mark - actions
- (void)appLifeCycleChangeHandler:(NSNotification *)notification
{
    for (id<UPPushAppManagerObserver> observer in [_observers objectForKey:@(UPPushApp_Aspect_LifeCycle)]) {
        SEL selector = NSSelectorFromString(_appLifeCycleSelectorMap[notification.name]);
        if ([observer respondsToSelector:selector]) {
            ((void (*)(id, SEL))[(NSObject *)observer methodForSelector:selector])(observer, selector);
        }
    }
}

@end
