//
//  UPPushAlertManager.m
//  UPPushUI
//
//  Created by osiris on 2020/11/11.
//

#import "UPPushAlertController.h"
#import <UPVDN/UpVdnUtils.h>
#import "UPPushStringTools.h"
#import "UIAlertView+UPPushEx.h"
#import "UPPushAlertView.h"

@interface UPPushAlertController ()

@property (nonatomic, strong) UPPushAlertView *pushAlertView;

@property (nonatomic, strong) UPPushAlertModel *alertModel;

@end

@implementation UPPushAlertController

- (instancetype)initWithModel:(UPPushAlertModel *)model
{
    if (self = [super init]) {
        _alertModel = model;
        if (![self checkAlertModel]) {
            return nil;
        }
        _pushAlertView = [UPPushAlertController assembleAlertView:_alertModel];
    }

    return self;
}

#pragma mark - public method
- (void)show:(dispatch_block_t)complete
{
    dispatch_async(dispatch_get_main_queue(), ^{
      [self.pushAlertView show];
      complete ? complete() : nil;
    });
}

- (void)dismiss:(dispatch_block_t)complete
{
    dispatch_async(dispatch_get_main_queue(), ^{
      [self.pushAlertView dismiss];
      complete ? complete() : nil;
    });
}
#pragma mark - private method
- (BOOL)checkAlertModel
{
    if (UPPush_isEmptyString(_alertModel.title) && UPPush_isEmptyString(_alertModel.message)) {
        return NO;
    }

    if (!_alertModel.cancelAction && (!_alertModel.otherActions || _alertModel.otherActions.count == 0)) {
        return NO;
    }

    if (_alertModel.cancelAction && UPPush_isEmptyString(_alertModel.cancelAction.title)) {
        return NO;
    }

    for (UPPushAlertActionModel *actionModel in _alertModel.otherActions) {
        if (UPPush_isEmptyString(actionModel.title)) {
            return NO;
        }
    }
    return YES;
}

- (UIAlertController *)assembleAlertController
{
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:_alertModel.title message:_alertModel.message preferredStyle:UIAlertControllerStyleAlert];
    __weak typeof(self) weakSelf = self;
    if (_alertModel.cancelAction) {
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:_alertModel.cancelAction.title
                                                               style:UIAlertActionStyleCancel
                                                             handler:^(UIAlertAction *_Nonnull action) {
                                                               weakSelf.alertModel.cancelAction.actionHandler ? weakSelf.alertModel.cancelAction.actionHandler() : nil;
                                                             }];
        [alert addAction:cancelAction];
    }

    typeof(_alertModel.otherActions) tempOtherActions = [_alertModel.otherActions copy];
    for (UPPushAlertActionModel *actionModel in tempOtherActions) {
        UIAlertAction *otherAction = [UIAlertAction actionWithTitle:actionModel.title
                                                              style:UIAlertActionStyleDefault
                                                            handler:^(UIAlertAction *_Nonnull action) {
                                                              actionModel.actionHandler ? actionModel.actionHandler() : nil;
                                                            }];
        [alert addAction:otherAction];
    }
    return alert;
}

// ⚠️ 为防止UIAlertView delegate被释放，采用类对象做代理
+ (UPPushAlertView *)assembleAlertView:(UPPushAlertModel *)alertModel
{
    UPPushAlertView *pushAlertView = [[UPPushAlertView alloc] init];
    pushAlertView.alertModel = alertModel;
    return pushAlertView;
}

@end
