//
//  UIPushAlertModel.m
//  UPPushUI
//
//  Created by osiris on 2020/11/11.
//

#import "UPPushAlertModel.h"

@implementation UPPushAlertModel

- (instancetype)init
{
    if (self = [super init]) {
        _title = @"";
        _message = @"";
    }

    return self;
}

- (NSArray<dispatch_queue_t> *)actionList
{
    NSMutableArray<dispatch_block_t> *actions = [NSMutableArray array];
    if (self.cancelAction) {
        dispatch_block_t cancelBlock = ^() {
          self.cancelAction.actionHandler ? self.cancelAction.actionHandler() : nil;
        };
        [actions addObject:cancelBlock];
    }

    [self.otherActions enumerateObjectsUsingBlock:^(UPPushAlertActionModel *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      dispatch_block_t otherBlock = ^() {
        obj.actionHandler ? obj.actionHandler() : nil;
      };
      [actions addObject:otherBlock];
    }];

    return actions.copy;
}


@end
