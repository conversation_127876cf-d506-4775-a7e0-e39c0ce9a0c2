//
//  UIPushAlertView.m
//  UPPush
//
//  Created by 郑连乐 on 2025/6/11.
//

#import "UPPushAlertView.h"
#import <UHMasonry/UHMasonry.h>

@interface UPPushAlertView ()

@property (nonatomic, strong) UIView *coverView;

@property (nonatomic, strong) UIView *singleView;

@end

@implementation UPPushAlertView

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self addSubview:self.coverView];
        [self.coverView uh_makeConstraints:^(UHConstraintMaker *make) {
          make.edges.equalTo(self);
        }];
    }
    return self;
}

#pragma mark - public method
- (void)show
{
    UIWindow *window = [UIApplication sharedApplication].keyWindow;
    if (window == nil) {
        return;
    }
    [window addSubview:self];
    [self uh_makeConstraints:^(UHConstraintMaker *make) {
      make.edges.equalTo(window);
    }];
}

- (void)dismiss
{
    [self removeFromSuperview];
    [self clickWithIndex:0];
}

- (void)setAlertModel:(UPPushAlertModel *)alertModel
{
    _alertModel = alertModel;

    [self showSingleGuideView];
}

#pragma mark - private method

- (void)showSingleGuideView
{
    [self addSubview:self.singleView];

    // 定义弹窗的固定宽度和边距
    CGFloat UpPermissionViewFixedWidth = 420;
    CGFloat UpPermissionViewMargin = 12;

    // 获取屏幕宽度
    CGFloat screenWidth = [UIScreen mainScreen].bounds.size.width;
    CGFloat breakpoint = UpPermissionViewFixedWidth + UpPermissionViewMargin * 2;

    [self.singleView uh_makeConstraints:^(UHConstraintMaker *make) {
      if (screenWidth < breakpoint) {
          make.left.equalTo(@(UpPermissionViewMargin));
          make.right.equalTo(@(-UpPermissionViewMargin));
      }
      else {
          make.width.equalTo(@(UpPermissionViewFixedWidth));
          make.centerX.equalTo(self);
      }
      make.bottom.equalTo(@-34);
    }];
}

- (UIView *)coverView
{
    if (!_coverView) {
        _coverView = UIView.new;
        _coverView.backgroundColor = UIColor.blackColor;
        _coverView.alpha = 0.5;
    }
    return _coverView;
}

- (UIView *)singleView
{
    if (!_singleView) {
        _singleView = UIView.new;
        _singleView.backgroundColor = [UIColor colorWithRed:0.961 green:0.961 blue:0.961 alpha:1.0];
        _singleView.layer.cornerRadius = 32;
        _singleView.layer.masksToBounds = YES;

        NSString *title = self.alertModel.title;
        NSString *content = self.alertModel.message;

        UILabel *titleView = UILabel.new;
        titleView.text = title;
        titleView.textColor = [UIColor colorWithRed:0.067 green:0.067 blue:0.067 alpha:1.0];
        titleView.font = [UIFont fontWithName:@"PingFangSC-Medium" size:17.0f];
        [_singleView addSubview:titleView];
        [titleView uh_makeConstraints:^(UHConstraintMaker *make) {
          make.top.equalTo(@28);
          make.centerX.equalTo(_singleView);
        }];

        UILabel *contentView = UILabel.new;
        contentView.numberOfLines = 0;
        contentView.text = content;
        contentView.textColor = [UIColor colorWithRed:0.4 green:0.4 blue:0.4 alpha:1.0];
        ;
        contentView.font = [UIFont fontWithName:@"PingFangSC-Regular" size:14.0f];
        [self setContentTextAlignmentWithContentView:contentView];
        [_singleView addSubview:contentView];
        [contentView uh_makeConstraints:^(UHConstraintMaker *make) {
          make.top.equalTo(titleView.uh_bottom).offset(12);
          make.left.equalTo(@20);
          make.right.equalTo(@-20);
          make.centerX.equalTo(_singleView);
        }];

        UIView *lineOneView = UIView.new;
        [_singleView addSubview:lineOneView];
        [lineOneView uh_makeConstraints:^(UHConstraintMaker *make) {
          make.top.equalTo(contentView.uh_bottom);
          make.centerX.equalTo(_singleView);
          make.width.equalTo(@0.5);
          make.bottom.equalTo(@0);
        }];

        if (_alertModel.cancelAction) {
            // 取消按钮
            UIButton *btnCancel = UIButton.new;
            [btnCancel setTitle:self.alertModel.cancelAction.title forState:UIControlStateNormal];
            [btnCancel setTitleColor:[UIColor colorWithRed:0.067 green:0.067 blue:0.067 alpha:1.0] forState:UIControlStateNormal];
            btnCancel.backgroundColor = UIColor.whiteColor;
            btnCancel.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:16.0];
            btnCancel.layer.cornerRadius = 16.0;
            btnCancel.layer.masksToBounds = true;
            [_singleView addSubview:btnCancel];
            [btnCancel uh_makeConstraints:^(UHConstraintMaker *make) {
              make.top.equalTo(contentView.uh_bottom).offset(28);
              make.height.equalTo(@44);
              make.left.equalTo(@16);
              if (_alertModel.otherActions.count == 1) {
                  make.right.equalTo(lineOneView.uh_left).offset(-6);
                  make.bottom.equalTo(@-16);
              }
              else if (_alertModel.otherActions.count > 1) {
                  make.right.equalTo(@-16);
              }
              else {
                  make.right.equalTo(@-16);
                  make.bottom.equalTo(@-16);
              }
            }];
            [btnCancel addTarget:self action:@selector(cancelSingleClicked) forControlEvents:UIControlEventTouchUpInside];

            // 其它按钮
            if (_alertModel.otherActions.count == 1) {
                UPPushAlertActionModel *model = _alertModel.otherActions.firstObject;
                UIButton *btnSure = UIButton.new;
                btnSure.tag = 101;
                [btnSure setTitle:model.title forState:UIControlStateNormal];
                [btnSure setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
                btnSure.backgroundColor = [UIColor colorWithRed:0.0 green:0.506 blue:1.0 alpha:1.0];
                btnSure.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:16.0];
                btnSure.layer.cornerRadius = 16.0;
                btnSure.layer.masksToBounds = true;
                [_singleView addSubview:btnSure];
                [btnSure uh_makeConstraints:^(UHConstraintMaker *make) {
                  make.top.equalTo(contentView.uh_bottom).offset(28);
                  make.right.equalTo(@-16);
                  make.height.equalTo(@44);
                  make.left.equalTo(lineOneView.uh_right).offset(6);
                  make.bottom.equalTo(@-16);
                }];
                [btnSure addTarget:self action:@selector(sureSingleClicked:) forControlEvents:UIControlEventTouchUpInside];
            }
            else {
                NSUInteger count = _alertModel.otherActions.count;
                __weak __typeof(self) weakSelf = self;
                [_alertModel.otherActions enumerateObjectsUsingBlock:^(UPPushAlertActionModel *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
                  UIButton *btnSure = UIButton.new;
                  btnSure.tag = 100 + idx + 1;
                  [btnSure setTitle:obj.title forState:UIControlStateNormal];
                  [btnSure setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
                  btnSure.backgroundColor = [UIColor colorWithRed:0.0 green:0.506 blue:1.0 alpha:1.0];
                  btnSure.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:16.0];
                  btnSure.layer.cornerRadius = 16.0;
                  btnSure.layer.masksToBounds = true;
                  [_singleView addSubview:btnSure];
                  [btnSure uh_makeConstraints:^(UHConstraintMaker *make) {
                    make.top.equalTo(btnCancel.uh_bottom).offset((28 + 44) * idx + 28);
                    make.right.equalTo(@-16);
                    make.height.equalTo(@44);
                    make.left.equalTo(@16);
                    if (idx == count - 1) {
                        make.bottom.equalTo(@-16);
                    }
                  }];
                  [btnSure addTarget:weakSelf action:@selector(sureSingleClicked:) forControlEvents:UIControlEventTouchUpInside];
                }];
            }
        }
        else {
            if (_alertModel.otherActions.count == 2) {
                __weak __typeof(self) weakSelf = self;
                [_alertModel.otherActions enumerateObjectsUsingBlock:^(UPPushAlertActionModel *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
                  UIButton *btnSure = UIButton.new;
                  btnSure.tag = 100 + idx;
                  [btnSure setTitle:obj.title forState:UIControlStateNormal];
                  [btnSure setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
                  btnSure.backgroundColor = [UIColor colorWithRed:0.0 green:0.506 blue:1.0 alpha:1.0];
                  btnSure.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:16.0];
                  btnSure.layer.cornerRadius = 16.0;
                  btnSure.layer.masksToBounds = true;
                  [_singleView addSubview:btnSure];
                  if (idx == 0) {
                      [btnSure uh_makeConstraints:^(UHConstraintMaker *make) {
                        make.top.equalTo(contentView.uh_bottom).offset(28);
                        make.height.equalTo(@44);
                        make.left.equalTo(@16);
                        make.right.equalTo(lineOneView.uh_left).offset(-6);
                        make.bottom.equalTo(@-16);
                      }];
                  }
                  else {
                      [btnSure uh_makeConstraints:^(UHConstraintMaker *make) {
                        make.top.equalTo(contentView.uh_bottom).offset(28);
                        make.right.equalTo(@-16);
                        make.height.equalTo(@44);
                        make.left.equalTo(lineOneView.uh_right).offset(6);
                        make.bottom.equalTo(@-16);
                      }];
                  }
                  [btnSure addTarget:weakSelf action:@selector(sureSingleClicked:) forControlEvents:UIControlEventTouchUpInside];
                }];
            }
            else {
                __weak __typeof(self) weakSelf = self;
                NSUInteger count = _alertModel.otherActions.count;
                [_alertModel.otherActions enumerateObjectsUsingBlock:^(UPPushAlertActionModel *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
                  UIButton *btnSure = UIButton.new;
                  btnSure.tag = 100 + idx;
                  [btnSure setTitle:obj.title forState:UIControlStateNormal];
                  [btnSure setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
                  btnSure.backgroundColor = [UIColor colorWithRed:0.0 green:0.506 blue:1.0 alpha:1.0];
                  btnSure.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:16.0];
                  btnSure.layer.cornerRadius = 16.0;
                  btnSure.layer.masksToBounds = true;
                  [_singleView addSubview:btnSure];
                  [btnSure uh_makeConstraints:^(UHConstraintMaker *make) {
                    make.top.equalTo(contentView.uh_bottom).offset((28 + 44) * idx + 28);
                    make.right.equalTo(@-16);
                    make.height.equalTo(@44);
                    make.left.equalTo(@16);
                    if (idx == count - 1) {
                        make.bottom.equalTo(@-16);
                    }
                  }];
                  [btnSure addTarget:weakSelf action:@selector(sureSingleClicked:) forControlEvents:UIControlEventTouchUpInside];
                }];
            }
        }
    }
    return _singleView;
}

- (void)clickWithIndex:(NSInteger)index
{
    NSArray<dispatch_block_t> *actions = _alertModel.actionList;
    if (index < 0 || index > actions.count - 1) {
        return;
    }
    actions[index] ? actions[index]() : nil;
    [self removeFromSuperview];
}

- (void)cancelSingleClicked
{
    [self clickWithIndex:0];
}

- (void)sureSingleClicked:(UIButton *)button
{
    [self clickWithIndex:button.tag - 100];
}

- (void)setContentTextAlignmentWithContentView:(UILabel *)contentView
{

    CGRect contentRect = [self getTextRectWithContentView:contentView];
    CGFloat lineHeight = contentView.font.lineHeight;
    NSInteger lines = ceilf(contentRect.size.height / lineHeight);
    contentView.textAlignment = lines > 2 ? NSTextAlignmentLeft : NSTextAlignmentCenter;
}

- (CGRect)getTextRectWithContentView:(UILabel *)contentView
{
    // 定义弹窗的固定宽度和边距，与showSingleGuideView方法保持一致
    CGFloat UpPermissionViewFixedWidth = 420;
    CGFloat UpPermissionViewMargin = 12;

    // 获取屏幕宽度
    CGFloat screenWidth = [UIScreen mainScreen].bounds.size.width;
    CGFloat breakpoint = UpPermissionViewFixedWidth + UpPermissionViewMargin * 2;

    CGFloat contentWidth;
    if (screenWidth < breakpoint) {
        // 屏幕宽度小于临界值时，使用边距计算
        contentWidth = screenWidth - UpPermissionViewMargin * 2 - 20 * 2;
    } else {
        // 屏幕宽度大于等于临界值时，使用固定宽度计算
        contentWidth = UpPermissionViewFixedWidth - 20 * 2;
    }

    NSDictionary *attributes = @{NSFontAttributeName : contentView.font};
    return [contentView.text boundingRectWithSize:CGSizeMake(contentWidth, CGFLOAT_MAX)
                                          options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading
                                       attributes:attributes
                                          context:nil];
}

@end
