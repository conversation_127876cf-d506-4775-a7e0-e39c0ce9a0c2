//
//  UIAlertView+UPPushEx.m
//  UPPushUI
//
//  Created by 吴子航 on 2023/1/3.
//

#import "UIAlertView+UPPushEx.h"
#import <objc/runtime.h>

static char *const kUPPushAlertViewModel = "kUPPushAlertViewModel";

@implementation UIAlertView (UPPushEx)
- (UPPushAlertModel *)alertModel
{
    return objc_getAssociatedObject(self, kUPPushAlertViewModel);
}

- (void)setAlertModel:(UPPushAlertModel *)alertModel
{
    objc_setAssociatedObject(self, kUPPushAlertViewModel, alertModel, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}
@end
