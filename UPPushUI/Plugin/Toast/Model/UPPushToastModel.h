//
//  UPPushToastModel.h
//  UPPushUI
//
//  Created by osiris on 2021/7/5.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPPushToastTapModel : NSObject

@property (nonatomic, strong, nullable) NSString *text;
@property (nonatomic, copy) dispatch_block_t tap;

@end

@interface UPPushToastModel : NSObject

@property (nonatomic, assign) NSTimeInterval interval;
@property (nonatomic, strong) NSString *content;
@property (nonatomic, strong) NSArray<UPPushToastTapModel *> *tapModel;

@end

NS_ASSUME_NONNULL_END
