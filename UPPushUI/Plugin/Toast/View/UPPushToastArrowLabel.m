//
//  UPPushToastArrowLabel.m
//  UPPushUI
//
//  Created by osiris on 2021/7/12.
//

#import "UPPushToastArrowLabel.h"
#import <UHMasonry/UHMasonry.h>

@interface UPPushToastArrowLabel ()

@property (nonatomic, strong) NSString *text;

@property (nonatomic, strong) UILabel *textLabel;
@property (nonatomic, strong) UIImageView *arrowImageView;

@end

@implementation UPPushToastArrowLabel

- (instancetype)initWithText:(NSString *)text
{
    if (self = [super initWithFrame:CGRectZero]) {
        self.text = text;
        [self configView];
        [self configConstraint];
    }

    return self;
}

- (void)configView
{
    self.textLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.textColor = [UIColor colorWithRed:108.0 / 255.0 green:200.0 / 255.0 blue:255.0 / 255.0 alpha:1.0];
        label.font = [UIFont fontWithName:@"PingFang SC" size:13];
        label.text = self.text;
        label.textAlignment = NSTextAlignmentCenter;
        label.contentMode = UIViewContentModeCenter;
        [label setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
        label;
    });
    self.arrowImageView = ({
        [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"UPPushResource.bundle/Images/arrow"]];
    });
    [self addSubview:self.textLabel];
    [self addSubview:self.arrowImageView];
}

- (void)configConstraint
{
    [self.textLabel uh_makeConstraints:^(UHConstraintMaker *make) {
      make.left.and.top.and.bottom.uh_equalTo(self);
    }];

    [self.arrowImageView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.left.uh_equalTo(self.textLabel.uh_right).uh_offset(4.0);
      make.right.uh_equalTo(self);
      make.centerY.uh_equalTo(self.textLabel);
      make.height.uh_equalTo(12.0);
      make.width.uh_equalTo(12.0);
    }];
}

@end
