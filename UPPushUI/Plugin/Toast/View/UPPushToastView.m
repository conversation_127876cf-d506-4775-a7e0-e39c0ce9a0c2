//
//  UPPushToastView.m
//  UPPushUI
//
//  Created by osiris on 2021/7/5.
//

#import "UPPushToastView.h"
#import <UHMasonry/UHMasonry.h>
#import "UPPushToastArrowLabel.h"
#import "UPPushMacros.h"
#import "UPPushStringTools.h"

typedef NS_ENUM(NSInteger, UPPushToastType) {
    UPPushToastType_Type0, // 内容文字字数少于30&高亮字数少于4
    UPPushToastType_Type1, // 内容文字字数大于30&高亮字数少于4
    UPPushToastType_Type3, // 内容文字字数少于30&高亮字数大于4
    UPPushToastType_Type4, // 内容文字字数大于30&高亮字数大于4
};

#define UPPUSHTOAST_TEXT_MAXLENGTH 30
#define UPPUSHTOAST_TAPTEXT_MAXLENGTH 4

@interface UPPushToastView ()

@property (nonatomic, strong) NSString *text;
@property (nonatomic, strong) NSString *tapText;
@property (nonatomic, copy) dispatch_block_t tap;

@property (nonatomic, strong) UILabel *textLabel;
@property (nonatomic, strong) UPPushToastArrowLabel *arrowLabel;

@end

@implementation UPPushToastView

- (instancetype)initWithText:(NSString *)text tapText:(NSString *)tapText tap:(dispatch_block_t)tap
{
    if (self = [super initWithFrame:CGRectZero]) {
        self.text = text;
        self.tapText = tapText;
        self.tap = tap;
        [self configView];
        [self configConstraint];
    }

    return self;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
}

- (void)configView
{
    self.backgroundColor = RGBACOLOR(0, 0, 0, 0.6);
    self.layer.cornerRadius = 10;
    self.clipsToBounds = YES;
    self.alpha = 0.0;
    [self addTarget:self action:@selector(tapAction) forControlEvents:UIControlEventTouchUpInside];

    self.textLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.textColor = [UIColor whiteColor];
        label.font = [UIFont fontWithName:@"PingFang SC" size:14];
        label.text = [self foldText:self.text maxCount:UPPUSHTOAST_TEXT_MAXLENGTH];
        label.textAlignment = NSTextAlignmentLeft;
        label.numberOfLines = 0;
        label.lineBreakMode = NSLineBreakByWordWrapping;
        [label setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
        [label setContentHuggingPriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
        label;
    });
    self.arrowLabel = ({
        UPPushToastArrowLabel *label = [[UPPushToastArrowLabel alloc] initWithText:self.tapText];
        label.userInteractionEnabled = NO;
        label;
    });
    [self addSubview:self.textLabel];

    if (self.hasTapText) {
        [self addSubview:self.arrowLabel];
    }
}

- (void)configConstraint
{
    [self.textLabel uh_makeConstraints:^(UHConstraintMaker *make) {
      make.left.uh_equalTo(16);
      make.top.uh_equalTo(14);
    }];

    self.hasTapText ? [self.arrowLabel uh_makeConstraints:^(UHConstraintMaker *make) {
      make.right.uh_equalTo(-16);
    }] :
                      nil;
    switch ([self type]) {
        case UPPushToastType_Type0:
        case UPPushToastType_Type1: {
            [self.textLabel uh_updateConstraints:^(UHConstraintMaker *make) {
              make.bottom.uh_equalTo(-14);
              make.right.uh_equalTo(self.hasTapText ? self.arrowLabel.uh_left : self).uh_equalTo(-16);
            }];
            self.hasTapText ? [self.arrowLabel uh_updateConstraints:^(UHConstraintMaker *make) {
              make.centerY.uh_equalTo(self.textLabel);
            }] :
                              nil;
        } break;
        case UPPushToastType_Type3:
        case UPPushToastType_Type4:
            [self.textLabel uh_updateConstraints:^(UHConstraintMaker *make) {
              make.bottom.uh_equalTo(self.hasTapText ? self.arrowLabel.uh_top : self).uh_equalTo(-12);
              make.right.uh_equalTo(-16);
            }];
            self.hasTapText ? [self.arrowLabel uh_updateConstraints:^(UHConstraintMaker *make) {
              make.bottom.uh_equalTo(-14);
            }] :
                              nil;
            break;
    }
}

#pragma mark - public methods
- (void)show:(dispatch_block_t)complete
{
    [UIView animateWithDuration:0.3
        animations:^{
          self.alpha = 1.0;
        }
        completion:^(BOOL finished) {
          complete ? complete() : nil;
        }];
}

- (void)dismiss:(dispatch_block_t)complete
{
    [UIView animateWithDuration:0.3
        animations:^{
          self.alpha = 0.0;
        }
        completion:^(BOOL finished) {
          [self removeFromSuperview];
          complete ? complete() : nil;
        }];
}

#pragma mark - actions
- (void)tapAction
{
    self.tap ? self.tap() : nil;
    self.hasTapText ? [self dismiss:^{
                      }] :
                      nil;
}

#pragma mark - private methods
- (UPPushToastType)type
{
    BOOL textOverlength = self.text.length > UPPUSHTOAST_TEXT_MAXLENGTH;
    BOOL tapTextOverlength = self.tapText.length > UPPUSHTOAST_TAPTEXT_MAXLENGTH;
    if (!textOverlength && !tapTextOverlength) {
        return UPPushToastType_Type0;
    }
    else if (textOverlength && !tapTextOverlength) {
        return UPPushToastType_Type1;
    }
    else if (!textOverlength && tapTextOverlength) {
        return UPPushToastType_Type3;
    }
    else {
        return UPPushToastType_Type4;
    }
}

- (BOOL)hasTapText
{
    return !UPPush_isEmptyString(self.tapText);
}

- (NSString *)foldText:(NSString *)text maxCount:(NSInteger)maxLength
{
    if (text.length > maxLength) {
        return [NSString stringWithFormat:@"%@...", [text substringToIndex:maxLength]];
    }

    return text;
}
@end
