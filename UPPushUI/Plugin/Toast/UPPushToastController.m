//
//  UPPushToastController.m
//  UPPushUI
//
//  Created by osiris on 2021/7/5.
//

#import "UPPushToastController.h"
#import <UHMasonry/UHMasonry.h>
#import "UPPushToastView.h"

@interface UPPushToastController ()

@property (nonatomic, strong) UPPushToastModel *model;
@property (nonatomic, strong) UPPushToastView *toastView;
@property (nonatomic, strong) NSTimer *timer;

@end

@implementation UPPushToastController

- (instancetype)initWithModel:(UPPushToastModel *)model
{
    if (self = [super init]) {
        self.model = model;
    }

    return self;
}

- (UPPushToastView *)toastView
{
    if (!_toastView) {
        _toastView = [[UPPushToastView alloc] initWithText:self.model.content tapText:self.model.tapModel.firstObject.text tap:self.model.tapModel.firstObject.tap];
    }

    return _toastView;
}

- (NSTimer *)timer
{
    if (!_timer) {
        _timer = [NSTimer scheduledTimerWithTimeInterval:self.model.interval
                                                 repeats:NO
                                                   block:^(NSTimer *_Nonnull timer) {
                                                     [self invalidate];
                                                   }];
    }

    return _timer;
}

- (void)show:(dispatch_block_t)complete
{
    UIView *window = [UIApplication sharedApplication].keyWindow;
    [window addSubview:self.toastView];
    [self.toastView uh_makeConstraints:^(UHConstraintMaker *make) {
      make.left.uh_equalTo(16);
      make.right.uh_equalTo(-16);
      make.bottom.uh_equalTo(window.uh_bottomMargin).uh_offset(-100);
    }];
    [self.toastView show:^{
      [[NSRunLoop currentRunLoop] addTimer:self.timer forMode:NSRunLoopCommonModes];
      complete ? complete() : nil;
    }];
}

- (void)dismiss:(dispatch_block_t)complete
{
    [self.toastView dismiss:^{
      complete ? complete() : nil;
    }];
}

#pragma mark - private method
- (void)invalidate
{
    [self.timer invalidate];
    self.timer = nil;
    [self dismiss:nil];
}
@end
