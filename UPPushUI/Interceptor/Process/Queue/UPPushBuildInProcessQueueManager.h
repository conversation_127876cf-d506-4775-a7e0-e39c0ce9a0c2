//
//  UPPushBuildInProcessQueueManager.h
//  UPPushUI
//
//  Created by osiris on 2020/12/23.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPPushBuildInProcessQueueManager : NSObject

+ (instancetype)instance;
/**
 自定义队列中，同步执行任务
 @param task 任务
 */
- (void)syncExecute:(dispatch_block_t)task;
/**
 自定义队列中，异步执行任务
 @param task 任务
 */
- (void)asyncExecute:(dispatch_block_t)task;
/**
 自定义队列中，延时执行任务
 @param task 任务
 @param delay 延时
 */
- (void)asyncExecute:(dispatch_block_t)task delay:(NSTimeInterval)delay;


@end

NS_ASSUME_NONNULL_END
