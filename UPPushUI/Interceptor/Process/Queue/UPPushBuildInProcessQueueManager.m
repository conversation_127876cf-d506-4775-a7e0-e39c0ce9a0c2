//
//  UPPushBuildInProcessQueueManager.m
//  UPPushUI
//
//  Created by osiris on 2020/12/23.
//

#import "UPPushBuildInProcessQueueManager.h"

@interface UPPushBuildInProcessQueueManager ()

@property (nonatomic, strong) dispatch_queue_t queue;

@end

@implementation UPPushBuildInProcessQueueManager

+ (instancetype)instance
{
    static dispatch_once_t onceToken;
    static UPPushBuildInProcessQueueManager *manager;
    dispatch_once(&onceToken, ^{
      manager = [[UPPushBuildInProcessQueueManager alloc] init];
    });

    return manager;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        _queue = dispatch_queue_create("com.haier.UPPush.BuildInProcessQueue", DISPATCH_QUEUE_CONCURRENT);
    }
    return self;
}

#pragma mark - public methods
- (void)syncExecute:(dispatch_block_t)task
{
    dispatch_barrier_async(_queue, task);
}

- (void)asyncExecute:(dispatch_block_t)task
{
    dispatch_async(_queue, task);
}

- (void)asyncExecute:(dispatch_block_t)task delay:(NSTimeInterval)delay
{
    [self asyncExecute:^{
      [NSThread sleepForTimeInterval:delay];
      task();
    }];
}
@end
