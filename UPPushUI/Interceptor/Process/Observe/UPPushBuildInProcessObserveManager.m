//
//  UPPushBuildInProcessObserveManager.m
//  UPPushUI
//
//  Created by osir<PERSON> on 2020/12/23.
//

#import "UPPushBuildInProcessObserveManager.h"
#import "UPPushBuildInProcessQueueManager.h"

@interface UPPushBuildInProcessObserveManager ()

@property (nonatomic, strong) NSDictionary<id, NSString *> *eventDict;

@end

@implementation UPPushBuildInProcessObserveManager

+ (instancetype)instance
{
    static dispatch_once_t onceToken;
    static UPPushBuildInProcessObserveManager *manager;
    dispatch_once(&onceToken, ^{
      manager = [[UPPushBuildInProcessObserveManager alloc] init];
    });

    return manager;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self assembleEventMap];
    }
    return self;
}
#pragma mark - public methods
- (void)triggerEvent:(UPPushBuildInProcessEvent)event params:(id)param
{
    SEL selector = NSSelectorFromString(_eventDict[@(event)]);
    if ([_observer respondsToSelector:selector]) {
        [UPPushBuildInProcessQueueManager.instance syncExecute:^{
          ((void (*)(id, SEL, id))[(NSObject *)self->_observer methodForSelector:selector])(self->_observer, selector, param);
        }];
    }
}

#pragma mark - private methods
- (void)assembleEventMap
{
    _eventDict = @{
        @(UPPushBuildInProcess_willProcess) : NSStringFromSelector(@selector(willProcessMessage:)),
        @(UPPushBuildInProcess_didProcess) : NSStringFromSelector(@selector(didProcessMessage:))
    };
}
@end
