//
//  UPPushBuildInProcessObserveManager.h
//  UPPushUI
//
//  Created by osiris on 2020/12/23.
//

#import <Foundation/Foundation.h>
#import "UPPushInterceptorProtocol.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, UPPushBuildInProcessEvent) {
    UPPushBuildInProcess_willProcess,
    UPPushBuildInProcess_didProcess,
};

@interface UPPushBuildInProcessObserveManager : NSObject

@property (nonatomic, weak) id<UPPushBuildInInterceptorObserverProtocol> observer;

+ (instancetype)instance;
/**
 监听事件触发
 */
- (void)triggerEvent:(UPPushBuildInProcessEvent)event params:(id)param;
@end

NS_ASSUME_NONNULL_END
