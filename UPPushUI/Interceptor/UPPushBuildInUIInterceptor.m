//
//  UPPushDefaultInterceptor.m
//  UPPushUI
//
//  Created by osiris on 2020/11/2.
//

#import "UPPushBuildInUIInterceptor.h"
#import <UPLog/UPLog.h>
#import <UserNotifications/UserNotifications.h>
#import "UPPushHandlerTool.h"
#import "UPPushAppManager.h"
#import "UPPushGIOTrack.h"
#import "UPPushLogoutObserver.h"
#import "UPPushBuildInProcessObserveManager.h"
#import "UPPushBuildInProcessQueueManager.h"
#import "UPPushMessageProcessorFactory.h"

@interface UPPushBuildInUIInterceptor () <UPPushAppManagerObserver, UPPushGIOTrackDelegate, UPPushLogoutObserverDelegate, UPPushMessageProcessorDelegate>

@property (nonatomic, assign) BOOL reviceRemotePushActive;
@property (nonatomic, assign) BOOL reviceRemotePushBackground;
@end

@implementation UPPushBuildInUIInterceptor
@synthesize delegate;

- (instancetype)init
{
    if (self = [super init]) {
        [[UPPushAppManager instance] addObserver:self aspect:UPPushApp_Aspect_LifeCycle];
        UPPushGIOTrack.instance.interceptorDelegate = self;
        [UPPushLogoutObserver.instance addLogoutObserver];
        UPPushLogoutObserver.instance.delegate = self;
        _reviceRemotePushBackground = YES;
        _reviceRemotePushActive = NO;
        [self clearNotificationBar];
    }

    return self;
}

- (void)dealloc
{
    [[UPPushAppManager instance] removeObserver:self aspect:UPPushApp_Aspect_LifeCycle];
}
#pragma mark - UPPushInterceptorProtocol
- (BOOL)isFire
{
    return YES;
}

- (BOOL)check:(nonnull id<UPPushMessageProtocol>)pushMessage
{
    return YES;
}

- (void)intercept:(nonnull id<UPPushMessageProtocol>)pushMessage
{
    [self receivePushState];
    pushMessage.receivePushState = _reviceRemotePushActive ? ReceivePush_State_Active : ReceivePush_State_Inactive;
    UPLogDebug(@"UPPush", @"<UPPush Debugging> 消息接收时，App状态为：%d", pushMessage.receivePushState);
    if (![self messagePreprocessing:pushMessage]) {
        return;
    };

    [UPPushBuildInProcessObserveManager.instance triggerEvent:UPPushBuildInProcess_willProcess params:pushMessage];
    [UPPushBuildInProcessQueueManager.instance syncExecute:^{
      [self dealMessage:pushMessage];
    }];
    [UPPushBuildInProcessObserveManager.instance triggerEvent:UPPushBuildInProcess_didProcess params:pushMessage];
}

- (UPPushAbility)supportAbility
{
    return UPPushAbility_Action | UPPushAbility_Notification | UPPushAbility_Dialog | UPPushAbility_Toast | UPPushAbility_DeliverData | UPPushAbility_OpenApp | UPPushAbility_OpenPage | UPPushAbility_OpenLink | UPPushAbility_CallApi;
}
#pragma mark - 待重构
- (BOOL)messagePreprocessing:(id<UPPushMessageProtocol>)message
{
    if (!message) {
        return NO;
    }

    if ([UPPushHandlerTool duplicateMessageHandle:message] && !message.isLocal) {
        UPLogDebug(@"UPPush", @"<UPPush Debugging> 重复消息不处理：%@", message.messageID);
        return NO;
    }

    return YES;
}

- (void)dealMessage:(UPPushMessage *)message
{
    [UPPushGIOTrack trackJustReceive:message];
    UPPushMessageProcessor *handler = [UPPushMessageProcessorFactory messageProcessor:message];
    handler.delegate = self;
    [handler handleMessage:message];
}

- (void)receivePushState
{
    UIApplicationState state = [UPPushAppManager instance].appState;
    if ((state == UIApplicationStateActive || state == UIApplicationStateInactive) &&
        !_reviceRemotePushBackground) {
        _reviceRemotePushActive = YES;
    }
    else {
        _reviceRemotePushActive = NO;
    }
}

- (void)clearNotificationBar
{
    [UNUserNotificationCenter.currentNotificationCenter removeAllDeliveredNotifications];
}

#pragma mark - UPPushAppManagerObserver
- (void)onAppDidBecomeActive
{
    UPLogDebug(@"UPPush", @"<UPPush Debugging> onAppDidBecomeActive");
    _reviceRemotePushBackground = NO;
}

- (void)onAppDidEnterBackground
{
    UPLogDebug(@"UPPush", @"<UPPush Debugging> onAppDidEnterBackground");
    _reviceRemotePushBackground = YES;
}

- (void)onAppWillEnterForeground
{
}

- (void)onAppWillResignActive
{
    UPLogDebug(@"UPPush", @"<UPPush Debugging> onAppWillResignActive");
    _reviceRemotePushBackground = YES;
}

- (void)onAppWillTerminate
{
}

#pragma mark - UPPushGIOTrackDelegate
- (void)track:(NSString *)eventID param:(NSDictionary *)param
{
    if ([self.delegate respondsToSelector:@selector(track:param:)]) {
        [self.delegate track:eventID param:param];
    }
}

/**
 打点
 @param eventName 事件标识，注意：eventName可能不是标准的事件id，因此不能直接传给GIO或Firebase进行埋点，需要在协议方法实现了进行具体的判断
 @param messageInfo 消息体
 @param extraInfo 其它附加参数
 */
- (void)trackEvent:(NSString *)eventName messageInfo:(NSDictionary *)messageInfo extraInfo:(NSDictionary *)extraInfo
{
    if ([self.delegate respondsToSelector:@selector(trackEvent:messageInfo:extraInfo:)]) {
        [self.delegate trackEvent:eventName messageInfo:messageInfo extraInfo:extraInfo];
    }
}

#pragma mark - UPPushLogoutObserverDelegate
- (void)logoutForChangePassword:(id<UPPushMessageProtocol>)message;
{
    if ([self.delegate respondsToSelector:@selector(logoutForChangePassword:)]) {
        [self.delegate logoutForChangePassword:message];
    }
}

#pragma mark - UPPushBuildInInterceptorSubjectProtocol
- (void)setObserver:(id<UPPushBuildInInterceptorObserverProtocol>)observer
{
    UPPushBuildInProcessObserveManager.instance.observer = observer;
}

#pragma mark - UPPushMessageProcessorDelegate
- (BOOL)customHandleDirectJumpMessage:(id<UPPushMessageProtocol>)message
{
    if ([self.delegate respondsToSelector:@selector(customHandleDirectJumpMessage:)]) {
        return [self.delegate customHandleDirectJumpMessage:message];
    }

    return NO;
}

- (void)handleDirectJumpMessage:(id<UPPushMessageProtocol>)message
{
    if ([self.delegate respondsToSelector:@selector(handleDirectJumpMessage:)]) {
        [self.delegate handleDirectJumpMessage:message];
    }
}
@end
