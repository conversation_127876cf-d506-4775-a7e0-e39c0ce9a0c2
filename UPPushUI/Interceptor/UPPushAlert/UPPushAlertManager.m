//
//  UPPushAlertManager.m
//  UPPushUI
//
//  Created by osiris on 2020/11/11.
//

#import "UPPushAlertManager.h"
#import <MJExtension/MJExtension.h>
#import "UPPushAlertController.h"
#import "UPPushMessageExtension.h"
#import "UPPushMessageUIButton.h"
#import "UPPushGIOTrack.h"
#import "UPPushHandlerTool.h"
#import "UPPushLocalizationConfig.h"

@interface UPPushAlertManager ()

@property (nonatomic, strong) NSMutableDictionary<NSString *, UPPushAlertController *> *alertDict;
@property (nonatomic, strong) NSMutableDictionary<NSString *, UPPushMessage *> *messageDict;

@end

@implementation UPPushAlertManager

+ (instancetype)instance
{
    static dispatch_once_t onceToken;
    static UPPushAlertManager *alertManager;
    dispatch_once(&onceToken, ^{
      alertManager = [[UPPushAlertManager alloc] init];
    });

    return alertManager;
}

- (instancetype)init
{
    if (self = [super init]) {
        _alertDict = [NSMutableDictionary dictionary];
        _messageDict = [NSMutableDictionary dictionary];
    }

    return self;
}

#pragma mark - public method
- (void)showAlertWithMessage:(UPPushMessage *)message actionHandle:(nonnull void (^)(NSInteger))actionHandle
{
    __weak typeof(self) weakSelf = self;
    if (message.messageBody.extraParam.allowMerge) {
        UPPushMessage *cacheMessage = [self.messageDict objectForKey:message.messageBody.extraParam.pushRuleID];
        if (!cacheMessage) {
            [self assembleAlertWithMessage:message
                showHandle:^(UPPushAlertController *alertController) {
                  [self.alertDict setObject:alertController forKey:message.messageBody.extraParam.pushRuleID];
                  [self.messageDict setObject:message forKey:message.messageBody.extraParam.pushRuleID];
                }
                actionHandle:^(NSInteger callID) {
                  [weakSelf.alertDict removeObjectForKey:message.messageBody.extraParam.pushRuleID];
                  [weakSelf.messageDict removeObjectForKey:message.messageBody.extraParam.pushRuleID];
                  actionHandle ? actionHandle(callID) : nil;
                }];
            return;
        }
        if (message.timestamp >= cacheMessage.timestamp) {
            UPPushAlertController *cacheAlert = [self.alertDict objectForKey:message.messageBody.extraParam.pushRuleID];
            [self assembleAlertWithMessage:message
                showHandle:^(UPPushAlertController *alertController) {
                  if (alertController) {
                      [self.alertDict setObject:alertController forKey:message.messageBody.extraParam.pushRuleID];
                  }
                  [self.messageDict setObject:message forKey:message.messageBody.extraParam.pushRuleID];
                }
                actionHandle:^(NSInteger callID) {
                  NSString *pushRuleID = message.messageBody.extraParam.pushRuleID;
                  if (pushRuleID) {
                      [weakSelf.alertDict removeObjectForKey:pushRuleID];
                      [weakSelf.messageDict removeObjectForKey:pushRuleID];
                  }
                  actionHandle ? actionHandle(callID) : nil;
                }];
            [cacheAlert dismiss:^{
            }];
        }
    }
    else {
        [self assembleAlertWithMessage:message showHandle:nil actionHandle:actionHandle];
    }
}

#pragma mark - private method
- (void)assembleAlertWithMessage:(UPPushMessage *)message showHandle:(void (^)(UPPushAlertController *))showHandle actionHandle:(void (^)(NSInteger callID))actionHandle
{
    UPPushAlertModel *alertModel = [[UPPushAlertModel alloc] init];
    alertModel.title = message.messageBody.messageUI.title;
    alertModel.message = message.messageBody.messageUI.content;
    NSMutableArray<UPPushAlertActionModel *> *actionModelArray = [NSMutableArray array];
    NSArray<id<UPPushMessageUIButtonProtocol>> *buttons = [message.messageBody.messageUI.buttons copy];
    if (!buttons || buttons.count == 0) {
        NSString *buttonTitle = @"我知道了";
        if (UPPushHandlerTool.isMultiLanguageEnvironment) {
            buttonTitle = UPPushLocalizationConfig.shareInstance.OK;
        }

        buttons = @[ [UPPushMessageUIButton mj_objectWithKeyValues:@{ @"text" : buttonTitle,
                                                                      @"callId" : @"1" }] ];
        [UPPushGIOTrack.instance trackEvent:PUSH_EVENT_MESSAGE_CLICK
                                messageInfo:message
                                  extraInfo:@{
                                      @"label" : buttonTitle,
                                      @"title" : alertModel.title,
                                      @"content" : alertModel.message
                                  }];
    }
    UPPushAlertActionModel *actionModel = nil;
    for (id<UPPushMessageUIButtonProtocol> button in buttons) {
        actionModel = [[UPPushAlertActionModel alloc] init];
        actionModel.title = button.text;
        actionModel.actionHandler = ^{
          actionHandle ? actionHandle(button.callID) : nil;
          [self gioTrackWithMessage:message buttonName:button.text callID:button.callID];
          [UPPushGIOTrack.instance trackEvent:PUSH_EVENT_MESSAGE_CLICK
                                  messageInfo:message
                                    extraInfo:@{
                                        @"label" : button.text,
                                        @"title" : alertModel.title,
                                        @"content" : alertModel.message,
                                        @"callId" : @(button.callID)
                                    }];
        };
        [actionModelArray addObject:actionModel];
    }
    if (actionModelArray.count > 1) {
        alertModel.cancelAction = actionModelArray.firstObject;
        [actionModelArray removeObjectAtIndex:0];
    }
    alertModel.otherActions = [actionModelArray copy];
    dispatch_async(dispatch_get_main_queue(), ^{
      UPPushAlertController *alertController = [[UPPushAlertController alloc] initWithModel:alertModel];
      if (alertController) {
          [UPPushGIOTrack.instance trackEvent:PUSH_EVENT_MESSAGE_SHOW
                                  messageInfo:message
                                    extraInfo:@{ @"title" : alertModel.title,
                                                 @"content" : alertModel.message }];
          [alertController show:^{
            showHandle ? showHandle(alertController) : nil;
          }];
      }
    });
}

- (void)gioTrackWithMessage:(UPPushMessage *)message buttonName:(NSString *)buttonName callID:(NSInteger)callID
{
    __block NSString *hyperLink = nil;
    [message.pagesCompletion enumerateObjectsUsingBlock:^(id<UPPushMessagePageProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (callID == obj.callID) {
          hyperLink = obj.url;
          *stop = YES;
      }
    }];
    [UPPushGIOTrack.instance gioTrackWithEventId:PUSH_GIO_EVENTID
                                           msgID:message.messageID
                                      buttonName:buttonName
                                       hyperlink:hyperLink
                                    contentTitle:message.messageBody.messageUI.title
                                 contentSubtitle:message.messageBody.messageUI.content];
}


@end
