//
//  UPPushTapToastManager.m
//  UPPushUI
//
//  Created by osiris on 2021/7/5.
//

#import "UPPushToastManager.h"
#import "UPPushMessage.h"
#import "UPPushToastController.h"
#import "UPPushToastModel.h"
#import "UPPushMessage+Completion.h"
#import "UPPushStringTools.h"

@implementation UPPushToastManager

+ (instancetype)instance
{
    static dispatch_once_t onceToken;
    static UPPushToastManager *alertManager;
    dispatch_once(&onceToken, ^{
      alertManager = [[UPPushToastManager alloc] init];
    });

    return alertManager;
}

- (void)showToastWithMessage:(UPPushMessage *)message actionHandle:(void (^)(NSInteger))actionHandle
{
    UPPushToastModel *toastModel = [[UPPushToastModel alloc] init];
    toastModel.interval = 3.0;
    toastModel.content = message.messageBody.messageUI.content;

    NSMutableArray<UPPushToastTapModel *> *tapModels = [NSMutableArray array];
    [message.messageBody.messageUI.buttons enumerateObjectsUsingBlock:^(id<UPPushMessageUIButtonProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      UPPushToastTapModel *tapModel = [[UPPushToastTapModel alloc] init];
      __block BOOL hasUrl = NO;
      [message.pagesCompletion enumerateObjectsUsingBlock:^(id<UPPushMessagePageProtocol> _Nonnull page, NSUInteger i, BOOL *_Nonnull s) {
        if (page.callID == obj.callID) {
            hasUrl = !UPPush_isEmptyString(page.url);
            *s = YES;
        }
      }];
      tapModel.text = hasUrl ? obj.text : nil;
      tapModel.tap = ^{
        actionHandle ? actionHandle(obj.callID) : nil;
      };
      [tapModels addObject:tapModel];
    }];
    toastModel.tapModel = tapModels;
    UPPushToastController *toastController = [[UPPushToastController alloc] initWithModel:toastModel];
    dispatch_async(dispatch_get_main_queue(), ^{
      [toastController show:^{

      }];
    });
}

@end
