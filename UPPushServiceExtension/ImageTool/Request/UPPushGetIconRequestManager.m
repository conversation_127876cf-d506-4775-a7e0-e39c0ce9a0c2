//
//  UPPushGetIconRequestManager.m
//  UPPush
//
//  Created by 飛 on 2025/6/4.
//

#import "UPPushGetIconRequestManager.h"
#import "UPPushGetIconResultModel.h"
#import <UPLog/UPLog.h>
#import "UPPushResult.h"
//#import <os/log.h>

@implementation UPPushGetIconRequestManager
+ (void)request:(UPRequest *)api result:(UPPushGetIconRequestCallback)result
{
    UPRequestSuccess successBlock = ^(NSObject *_Nonnull responseObject) {
      UPLogInfo(@"UPLog", @"[%s][%d]getPlayUrlByFileIndex结果:%@ ",
                __PRETTY_FUNCTION__, __LINE__, responseObject);
      UPPushResult *resultObj = (UPPushResult *)responseObject;
      if (!resultObj.isSuccess ||
          ![resultObj.payload isKindOfClass:[NSDictionary class]]) {
          // os_log_t logger = os_log_create("com.haier.uhome.Uplus.NotificationServiceExtension", "NotificationService");
          // os_log(logger, "下载图片：错误resultObj: %{public}@---%{public}@---%{public}@", resultObj.info, resultObj.payload, resultObj.code);
          if (result)
              result(nil);
          return;
      }
      UPPushGetIconResultModel *model =
          [[UPPushGetIconResultModel alloc] initWithPayload:resultObj.payload];
      // os_log_t logger = os_log_create("com.haier.uhome.Uplus.NotificationServiceExtension", "NotificationService");
      // os_log(logger, "下载图片：model: %{public}@", model);
      if (result) {
          result(model);
      }

    };

    UPRequestFailure failureBlock =
        ^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          UPLogInfo(@"UPLog", @"[%s][%d]getPlayUrlByFileIndex错误:%@ ",
                    __PRETTY_FUNCTION__, __LINE__, error);
          // os_log_t logger = os_log_create("com.haier.uhome.Uplus.NotificationServiceExtension", "NotificationService");
          // os_log(logger, "下载图片：错误: %{public}@", error);
          if (result)
              result(nil);
        };

    [api startRequestWithSuccess:successBlock failure:failureBlock];
}
@end
