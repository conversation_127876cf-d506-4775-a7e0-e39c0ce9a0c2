//
//  UPPushGetIconRequestManager.h
//  UPPush
//
//  Created by 飛 on 2025/6/4.
//

#import <Foundation/Foundation.h>
#import <upnetwork/UPRequest.h>

@class UPPushGetIconResultModel;

NS_ASSUME_NONNULL_BEGIN

typedef void (^UPPushGetIconRequestCallback)(
    UPPushGetIconResultModel *_Nullable result);

@interface UPPushGetIconRequestManager : NSObject
+ (void)request:(UPRequest *)api result:(UPPushGetIconRequestCallback)result;
@end

NS_ASSUME_NONNULL_END
