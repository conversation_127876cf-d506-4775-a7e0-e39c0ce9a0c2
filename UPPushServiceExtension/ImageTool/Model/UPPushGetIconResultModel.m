//
//  UPPushGetIconResultModel.m
//  UPPushServiceExtension
//
//  Created by 飛 on 2025/6/4.
//

#import "UPPushGetIconResultModel.h"

@implementation UPPushGetIconResultModel
- (instancetype)initWithPayload:(NSDictionary *)payload
{
    if (self = [super init]) {
        if ([payload isKindOfClass:[NSDictionary class]]) {
            _fileIndex = [payload[@"fileIndex"] isKindOfClass:[NSString class]] ? payload[@"fileIndex"] : @"";
            _playUrl = [payload[@"playUrl"] isKindOfClass:[NSString class]] ? payload[@"playUrl"] : @"";
            _deviceId = [payload[@"deviceId"] isKindOfClass:[NSString class]] ? payload[@"deviceId"] : @"";
        }
    }
    return self;
}
@end
