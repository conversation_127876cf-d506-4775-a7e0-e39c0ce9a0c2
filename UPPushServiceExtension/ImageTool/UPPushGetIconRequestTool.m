//
//  UPPushGetIconRequestTool.m
//  UPPush
//
//  Created by 飛 on 2025/6/4.
//

#import "UPPushGetIconRequestTool.h"
#import "UPPushGetIconAPI.h"
#import "UPPushGetIconRequestManager.h"
#import "UPPushResponseParser.h"

@implementation UPPushGetIconRequestTool
+ (instancetype)sharedInstance
{
    static UPPushGetIconRequestTool *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      sharedInstance = [[super allocWithZone:NULL] init];
    });
    return sharedInstance;
}

+ (instancetype)allocWithZone:(struct _NSZone *)zone
{
    return [self sharedInstance];
}

- (instancetype)init
{
    self = [super init];
    if (self) {
    }
    return self;
}
- (instancetype)copyWithZone:(NSZone *)zone
{
    return self;
}

- (instancetype)mutableCopyWithZone:(NSZone *)zone
{
    return self;
}

- (void)requestImageRealPath:(NSString *)fileIndex
                    callBack:
                        (void (^)(UPPushGetIconResultModel *_Nullable result))
                            callBackResult
{
    UPPushGetIconAPI *notDisturbAPI =
        [[UPPushGetIconAPI alloc] initWithFileindex:fileIndex];

    notDisturbAPI.responseParser = [UPPushResponseParser new];
    [UPPushGetIconRequestManager
        request:notDisturbAPI
         result:^(UPPushGetIconResultModel *_Nullable result) {
           if (callBackResult) {
               callBackResult(result);
           }

         }];
}
@end
