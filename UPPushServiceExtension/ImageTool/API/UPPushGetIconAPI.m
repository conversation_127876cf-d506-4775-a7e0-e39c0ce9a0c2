//
//  UPPushGetIconAPI.m
//  UPPush
//
//  Created by 飛 on 2025/6/4.
//

#import "UPPushGetIconAPI.h"
#import <CommonCrypto/CommonDigest.h>
#import <upnetwork/UpXUDID.h>
#import <upnetwork/UPNetworkSettings.h>
//#import <os/log.h>

@interface UPPushGetIconAPI ()
@property (nonatomic, copy) NSString *fileIndex;
@property (nonatomic, copy, readonly) NSString *clientId;
@property (nonatomic, copy, readonly) NSString *timestamp;
@property (nonatomic, copy, readonly) NSString *accessToken;
@property (nonatomic, strong) NSUserDefaults *groupDefaults;
@property (nonatomic, copy, readonly) NSString *appId;
@property (nonatomic, copy, readonly) NSString *appKey;
@property (nonatomic, copy, readonly) NSString *appVersion;
@end


@implementation UPPushGetIconAPI
- (instancetype)initWithFileindex:(NSString *)fileIndex
{
    if (self = [super init]) {
        self.fileIndex = fileIndex;
    }
    return self;
}
- (NSString *)name
{
    return @"使用fileIndex获取播放地址";
}

- (NSString *)path
{
    return @"/rs-service/v1/getPlayUrlByFileIndex";
}

- (NSObject *)requestBody
{
    return @{
        @"fileIndex" : _fileIndex,
    };
}
- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSString *timestamp = self.timestamp;
    NSDictionary *dict = @{
        @"appId" : self.appId,
        @"appVersion" : self.appVersion,
        @"clientId" : self.clientId,
        @"timestamp" : timestamp,
        @"sign" : [self createSignByTimestamp:timestamp],
        @"Content-Type" : @"application/json;charset=UTF-8",
        @"language" : @"zh-CN",
        @"timezone" : @"Asia/Shanghai",
        @"accessToken" : self.accessToken,
        @"Content-Encoding" : @"utf-8",
    };
    // os_log_t logger = os_log_create("com.haier.uhome.Uplus.NotificationServiceExtension", "NotificationService");
    // os_log(logger, "requestHeaders打印: %{public}@", dict);
    return dict;
}
- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSString *)getSHA256:(NSString *)str
{
    const char *cstr = [str cStringUsingEncoding:NSUTF8StringEncoding];
    NSData *data = [NSData dataWithBytes:cstr length:strlen(cstr)];
    uint8_t digest[CC_SHA256_DIGEST_LENGTH];
    CC_SHA256(data.bytes, (unsigned int)data.length, digest);
    NSMutableString *result =
        [NSMutableString stringWithCapacity:CC_SHA256_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_SHA256_DIGEST_LENGTH; i++) {
        [result appendFormat:@"%02x", digest[i]];
    }
    return result;
}

- (NSString *)timestamp
{
    NSTimeInterval millisecondTimeInterval =
        [[NSDate date] timeIntervalSince1970] * 1000;
    NSString *millisecondString =
        [NSString stringWithFormat:@"%lld", (long long)millisecondTimeInterval];
    return millisecondString;
}
- (NSString *)createSignByTimestamp:(NSString *)timestamp
{
    NSString *bodyStr = @"";
    NSDictionary *arguments = (NSDictionary *)self.requestBody;
    if ([arguments isKindOfClass:[NSDictionary class]]) {
        NSData *bodyData =
            [NSJSONSerialization dataWithJSONObject:arguments
                                            options:0
                                              error:nil];
        bodyStr =
            [[NSString alloc] initWithData:bodyData
                                  encoding:NSUTF8StringEncoding];
        bodyStr =
            [bodyStr stringByReplacingOccurrencesOfString:@"\n"
                                               withString:@""];
        bodyStr =
            [bodyStr stringByReplacingOccurrencesOfString:@"\r"
                                               withString:@""];
        bodyStr =
            [bodyStr stringByReplacingOccurrencesOfString:@" "
                                               withString:@""];
        bodyStr =
            [bodyStr stringByReplacingOccurrencesOfString:@"\b"
                                               withString:@""];
        bodyStr =
            [bodyStr stringByReplacingOccurrencesOfString:@"\t"
                                               withString:@""];
    }
    NSString *appID = self.appId;
    NSString *appKey = self.appKey;
    NSString *urlStr = self.path;
    if (![urlStr isKindOfClass:[NSString class]]) {
        urlStr = @"";
    }
    if (![urlStr hasPrefix:@"/"]) {
        urlStr = [NSString stringWithFormat:@"/%@", urlStr];
    }
    NSString *originString =
        [NSString stringWithFormat:@"%@%@%@%@%@", urlStr, bodyStr, appID, appKey,
                                   timestamp];
    NSString *signString = [[self getSHA256:originString] lowercaseString];
    return signString;
}

- (NSString *)safeStringFromPrimary:(NSString *)primary
                     fallbackSource:(NSDictionary *)fallbackDict
                        fallbackKey:(NSString *)key
{
    if ([primary isKindOfClass:NSString.class] && primary.length > 0) {
        return primary;
    }

    NSString *fallback = fallbackDict[key];
    if ([fallback isKindOfClass:NSString.class] && fallback.length > 0) {
        return fallback;
    }

    return @""; // 最终兜底
}

- (NSString *)accessToken
{
    NSDictionary *loginInfo = [self.groupDefaults objectForKey:@"loginInfo"];
    // os_log_t logger = os_log_create("com.haier.uhome.Uplus.NotificationServiceExtension", "NotificationService");
    // os_log(logger, "loginInfo打印: %@", loginInfo);
    return [self safeStringFromPrimary:nil
                        fallbackSource:loginInfo
                           fallbackKey:@"accessToken"];
}

- (NSString *)appId
{
    NSDictionary *appInfo = [self.groupDefaults objectForKey:@"appInfo"];
    // os_log_t logger = os_log_create("com.haier.uhome.Uplus.NotificationServiceExtension", "NotificationService");

    // os_log(logger, "appInfo打印: %@", appInfo);
    return [self safeStringFromPrimary:nil
                        fallbackSource:appInfo
                           fallbackKey:@"appId"];
}
- (NSString *)appKey
{
    NSDictionary *appInfo = [self.groupDefaults objectForKey:@"appInfo"];
    // os_log_t logger = os_log_create("com.haier.uhome.Uplus.NotificationServiceExtension", "NotificationService");
    // os_log(logger, "appInfo打印: %@", appInfo);
    return [self safeStringFromPrimary:nil fallbackSource:appInfo fallbackKey:@"appKey"];
}
- (NSString *)clientId
{
    NSDictionary *appInfo = [self.groupDefaults objectForKey:@"appInfo"];

    return [self safeStringFromPrimary:nil fallbackSource:appInfo fallbackKey:@"clientId"];
    ;
}
- (NSString *)appVersion
{
    NSDictionary *appInfo = [self.groupDefaults objectForKey:@"appInfo"];

    return [self safeStringFromPrimary:nil fallbackSource:appInfo fallbackKey:@"appVersion"];
    ;
}
- (NSUserDefaults *)groupDefaults
{
    if (!_groupDefaults) {
        _groupDefaults = [[NSUserDefaults alloc]
            initWithSuiteName:@"group.com.haier.uhome.Uplus"];
    }
    return _groupDefaults;
}
@end
