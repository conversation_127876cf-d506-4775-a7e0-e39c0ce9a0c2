//
//  UPPushServiceExtensionProtocol.h
//  UPPushServiceExtension
//
//  Created by 吴子航 on 2022/2/21.
//

#import <Foundation/Foundation.h>
#import <UserNotifications/UNNotificationRequest.h>
#import <UserNotifications/UNNotificationContent.h>

NS_ASSUME_NONNULL_BEGIN

@protocol UPPushServiceExtensionProtocol <NSObject>

- (void)didReceiveNotificationRequest:(UNNotificationRequest *)request withContentHandler:(void (^)(UNNotificationContent *contentToDeliver))contentHandler;

- (void)serviceExtensionTimeWillExpire;

@end

NS_ASSUME_NONNULL_END
