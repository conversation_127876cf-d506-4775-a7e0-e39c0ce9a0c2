//
//  UPPushPersistenceManager.h
//  UPPushServiceExtension
//
//  Created by 吴子航 on 2022/2/22.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPPushPersistenceManager : NSObject

+ (instancetype)shareInstance;

- (NSString *)domainCachePath;
- (BOOL)saveData:(NSData *)data path:(NSURL *)path;
- (BOOL)removeData:(NSURL *)path;
- (BOOL)containsData:(NSURL *)path;

@end

NS_ASSUME_NONNULL_END
