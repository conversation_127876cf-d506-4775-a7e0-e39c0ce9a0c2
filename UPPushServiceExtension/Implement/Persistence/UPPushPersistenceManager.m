//
//  UPPushPersistenceManager.m
//  UPPushServiceExtension
//
//  Created by 吴子航 on 2022/2/22.
//

#import "UPPushPersistenceManager.h"

@interface UPPushPersistenceManager ()

@property (nonatomic, strong) NSFileManager *fileManager;

@end

@implementation UPPushPersistenceManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static UPPushPersistenceManager *persistenceManager;
    dispatch_once(&onceToken, ^{
      persistenceManager = [UPPushPersistenceManager new];
    });

    return persistenceManager;
}

- (instancetype)init
{
    if (self = [super init]) {
        _fileManager = [NSFileManager defaultManager];
    }

    return self;
}

- (NSString *)domainCachePath
{
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    NSString *cacheDirectory = [paths objectAtIndex:0];
    return cacheDirectory ?: @"";
}

- (BOOL)saveData:(NSData *)data path:(NSURL *)path
{
    return [data writeToURL:path atomically:YES];
}

- (BOOL)removeData:(NSURL *)path
{
    return [self.fileManager removeItemAtURL:path error:nil];
}

- (BOOL)containsData:(NSURL *)path
{
    return [self.fileManager fileExistsAtPath:path.absoluteString];
}
@end
