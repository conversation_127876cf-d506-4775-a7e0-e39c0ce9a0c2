//
//  UPPushDownloadManager.m
//  UPPushServiceExtension
//
//  Created by 吴子航 on 2022/2/21.
//

#import "UPPushDownloadManager.h"

@implementation UPPushDownloadManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static UPPushDownloadManager *downloadManager;
    dispatch_once(&onceToken, ^{
      downloadManager = [UPPushDownloadManager new];
    });

    return downloadManager;
}

- (void)downloadData:(NSURL *)url success:(void (^)(NSData *))success failure:(void (^)(NSError *))failure
{
    NSURLSessionTask *task = [[NSURLSession sharedSession] dataTaskWithURL:url
                                                         completionHandler:^(NSData *_Nullable data, NSURLResponse *_Nullable response, NSError *_Nullable error) {
                                                           if (error) {
                                                               failure ? failure(error) : nil;
                                                               return;
                                                           }
                                                           success ? success(data) : nil;
                                                         }];

    [task resume];
}

@end
