//
//  Declaration.m
//  UPPushServiceExtension
//
//  Created by 吴子航 on 2022/3/8.
//

#import "UPPushSEDeclaration.h"

NSString *const UPPushServiceExtension_Image = @"UPPushServiceExtension_Image";
NSString *const UPPushServiceExtension_Image_Extension = @"jpg";
NSString *const kUPPushServiceExtension_Attachement = @"large_icon";
NSString *const kUPPushServiceExtension_AttachementID =
    @"com.haier.UPPush.attachement.image";

NSString *const UPPushServiceExtension_ErrorDomain =
    @"com.haier.uplus.uppush.extension";
NSInteger UPPushServiceExtension_SaveData_Error = 10000;
NSInteger UPPushServiceExtension_IllegalPath_Error = 10001;
NSInteger UPPushServiceExtension_InvalidPath_Error = 10002;
NSInteger UPPushServiceExtension_IllegalCachePath_Error = 10003;
NSInteger UPPushServiceExtension_EmptyImageData_Error = 10004;
NSInteger UPPushServiceExtension_RemoveData_Error = 10005;
NSInteger UPPushServiceExtension_TransformData_Error = 10006;
