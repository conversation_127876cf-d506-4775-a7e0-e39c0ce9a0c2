//
//  Declaration.h
//  UPPushServiceExtension
//
//  Created by 吴子航 on 2022/3/8.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString *const UPPushServiceExtension_Image;
extern NSString *const UPPushServiceExtension_Image_Extension;
extern NSString *const kUPPushServiceExtension_Attachement;
extern NSString *const kUPPushServiceExtension_AttachementID;

#pragma mark - error
extern NSString *const UPPushServiceExtension_ErrorDomain;
extern NSInteger UPPushServiceExtension_SaveData_Error;
extern NSInteger UPPushServiceExtension_IllegalPath_Error;
extern NSInteger UPPushServiceExtension_InvalidPath_Error;
extern NSInteger UPPushServiceExtension_IllegalCachePath_Error;
extern NSInteger UPPushServiceExtension_EmptyImageData_Error;
extern NSInteger UPPushServiceExtension_RemoveData_Error;
extern NSInteger UPPushServiceExtension_TransformData_Error;
NS_ASSUME_NONNULL_END
