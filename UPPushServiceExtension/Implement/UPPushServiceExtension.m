//
//  UPPushServiceExtension.m
//  UPPushServiceExtension
//
//  Created by 吴子航 on 2022/2/18.
//

#import "UPPushServiceExtension.h"
#import <UserNotifications/UNNotificationAttachment.h>
#import <UserNotifications/UNNotificationServiceExtension.h>
#import "UPPushDownloadManager.h"
#import "UPPushPersistenceManager.h"
#import "UPPushSEDeclaration.h"
#import "UPPushGetIconResultModel.h"
#import "UPPushGetIconRequestTool.h"
#import <UPLog/UPLog.h>
//#import <os/log.h>
@interface UPPushServiceExtension ()

@property (nonatomic, weak) UNNotificationServiceExtension *serviceExtension;
@property (nonatomic, strong) void (^contentHandler)
    (UNNotificationContent *contentToDeliver);
@property (nonatomic, strong) UNMutableNotificationContent *bestAttemptContent;

@end

@implementation UPPushServiceExtension

- (instancetype)initWithExtension:
    (UNNotificationServiceExtension *)serviceExtension
{
    if (self = [super init]) {
        _serviceExtension = serviceExtension;
    }

    return self;
}

#pragma mark - UPPushServiceExtensionProtocol
- (void)didReceiveNotificationRequest:(nonnull UNNotificationRequest *)request
                   withContentHandler:
                       (nonnull void (^)(UNNotificationContent *_Nonnull))
                           contentHandler
{
    self.contentHandler = contentHandler;
    self.bestAttemptContent = [request.content mutableCopy];

    NSDictionary *userInfo = request.content.userInfo;
    UPLogInfo(@"UPLog", @"[%s][%d]接收到的消息:%@ ", __PRETTY_FUNCTION__,
              __LINE__, userInfo);
    NSString *imageURL = userInfo[kUPPushServiceExtension_Attachement];
    UPLogInfo(@"UPLog", @"[%s][%d]getPlayUrlByFileIndex图片:%@ ",
              __PRETTY_FUNCTION__, __LINE__, imageURL);
    // os_log_t logger = os_log_create("com.haier.uhome.Uplus.NotificationServiceExtension", "NotificationService");
    // os_log(logger, "下载图片：得到的imageUrl: %@", imageURL);
    if ([self isValidURL:imageURL]) { // 原来的图片加载逻辑
        [self startImageAttachement:imageURL];
    }
    else {

        [self requestImageRealPath:imageURL];
    }
}

- (void)serviceExtensionTimeWillExpire
{
    self.contentHandler(self.bestAttemptContent);
}
// 进行网络请求 /rs-service/v1/getPlayUrlByFileIndex
- (void)requestImageRealPath:(NSString *)fileIndex
{
    if ([fileIndex containsString:@"&showInBrowser=true"]) {
        fileIndex = [fileIndex stringByReplacingOccurrencesOfString:@"&showInBrowser=true" withString:@""];
    }
    __weak typeof(self) weakSelf = self;
    [[UPPushGetIconRequestTool sharedInstance]
        requestImageRealPath:fileIndex
                    callBack:^(UPPushGetIconResultModel *_Nullable result) {
                      __strong __typeof(weakSelf) strongSelf = weakSelf;
                      // os_log_t logger = os_log_create("com.haier.uhome.Uplus.NotificationServiceExtension", "NotificationService");
                      // os_log(logger, "下载图片：最终结果: %@", result);
                      if (result && result.playUrl) {
                          [strongSelf startImageAttachement:result.playUrl];
                      }
                    }];
}
// 原来的图片加载逻辑
- (void)startImageAttachement:(NSString *)imageURL
{
    [self imageAttachement:imageURL
        success:^(UNNotificationAttachment *attachement) {
          if (attachement) {
              self.bestAttemptContent.attachments = @[ attachement ];
          }
          self.contentHandler(self.bestAttemptContent);
        }
        failure:^(NSError *error) {
          self.contentHandler(self.bestAttemptContent);
        }];
}

#pragma mark - private
- (void)imagePathExtractor:(NSString *)imagePath
                   success:(void (^)(NSURL *, NSURL *))success
                   failure:(void (^)(NSError *))failure
{
    if (![imagePath isKindOfClass:NSString.class] || imagePath.length == 0) {
        failure ? failure([NSError
                      errorWithDomain:UPPushServiceExtension_ErrorDomain
                                 code:UPPushServiceExtension_IllegalPath_Error
                             userInfo:nil]) :
                  nil;
        return;
    }

    NSURL *imageURL = [NSURL URLWithString:imagePath];
    if (!imageURL) {
        failure ? failure([NSError
                      errorWithDomain:UPPushServiceExtension_ErrorDomain
                                 code:UPPushServiceExtension_InvalidPath_Error
                             userInfo:nil]) :
                  nil;
        return;
    }

    NSURL *imageCacheURL = [[[NSURL
        fileURLWithPath:UPPushPersistenceManager.shareInstance.domainCachePath]
        URLByAppendingPathComponent:UPPushServiceExtension_Image]
        URLByAppendingPathExtension:UPPushServiceExtension_Image_Extension];
    if (!imageCacheURL) {
        failure ? failure([NSError
                      errorWithDomain:UPPushServiceExtension_ErrorDomain
                                 code:UPPushServiceExtension_IllegalCachePath_Error
                             userInfo:nil]) :
                  nil;
        return;
    }

    success ? success(imageURL, imageCacheURL) : nil;
}

- (void)imageAttachement:(NSString *)imagePath
                 success:(void (^)(UNNotificationAttachment *))success
                 failure:(void (^)(NSError *))failure
{
    [self
        imagePathExtractor:imagePath
                   success:^(NSURL *imageURL, NSURL *localCacheURL) {
                     if ([UPPushPersistenceManager.shareInstance
                             containsData:localCacheURL]) {
                         BOOL result = [UPPushPersistenceManager.shareInstance
                             removeData:localCacheURL];
                         if (!result) {
                             failure ? failure([NSError
                                           errorWithDomain:
                                               UPPushServiceExtension_ErrorDomain
                                                      code:
                                                          UPPushServiceExtension_RemoveData_Error
                                                  userInfo:nil]) :
                                       nil;
                             return;
                         }
                     }

                     [self
                          persistImage:imageURL
                         localCacheURL:localCacheURL
                               success:^{
                                 NSError *attachError = nil;
                                 UNNotificationAttachment *attachement =
                                     [UNNotificationAttachment
                                         attachmentWithIdentifier:
                                             kUPPushServiceExtension_AttachementID
                                                              URL:localCacheURL
                                                          options:nil
                                                            error:&attachError];
                                 if (attachError) {
                                     failure ? failure(attachError) : nil;
                                 }
                                 else {
                                     success ? success(attachement) : nil;
                                 }
                               }
                               failure:failure];
                   }
                   failure:failure];
}

- (void)persistImage:(NSURL *)imageURL
       localCacheURL:(NSURL *)localCacheURL
             success:(void (^)(void))success
             failure:(void (^)(NSError *))failure
{
    [UPPushDownloadManager.shareInstance
        downloadData:imageURL
             success:^(NSData *data) {
               if (!data || data.length == 0) {
                   failure ? failure([NSError
                                 errorWithDomain:UPPushServiceExtension_ErrorDomain
                                            code:
                                                UPPushServiceExtension_EmptyImageData_Error
                                        userInfo:nil]) :
                             nil;
                   return;
               }

               NSData *transformData = [self transfromToJPEG:data];
               if (!transformData || transformData.length == 0) {
                   failure ? failure([NSError
                                 errorWithDomain:UPPushServiceExtension_ErrorDomain
                                            code:
                                                UPPushServiceExtension_TransformData_Error
                                        userInfo:nil]) :
                             nil;
                   return;
               }

               BOOL saveSuccess = [UPPushPersistenceManager.shareInstance
                   saveData:transformData
                       path:localCacheURL];
               if (saveSuccess) {
                   success ? success() : nil;
               }
               else {
                   failure ? failure([NSError
                                 errorWithDomain:UPPushServiceExtension_ErrorDomain
                                            code:UPPushServiceExtension_SaveData_Error
                                        userInfo:nil]) :
                             nil;
               }
             }
             failure:failure];
}

- (NSData *)transfromToJPEG:(NSData *)data
{
    UIImage *image = [UIImage imageWithData:data];
    NSData *transfromData = UIImageJPEGRepresentation(image, 1.0);
    return transfromData;
}
/// 判断是否是链接
- (BOOL)isValidURL:(NSString *)string
{
    if (string == nil || string.length == 0)
        return NO;

    // 去除首尾空格和换行符，防止误判
    NSString *trimmedString = [string
        stringByTrimmingCharactersInSet:[NSCharacterSet
                                            whitespaceAndNewlineCharacterSet]];

    // 使用 NSDataDetector 来识别字符串中的链接类型
    NSError *error = nil;
    NSDataDetector *linkDetector =
        [NSDataDetector dataDetectorWithTypes:NSTextCheckingTypeLink
                                        error:&error];
    if (error || !linkDetector)
        return NO; // 如果 detector 创建失败，则不判断，直接返回 NO

    NSTextCheckingResult *match =
        [linkDetector firstMatchInString:trimmedString
                                 options:0
                                   range:NSMakeRange(0, trimmedString.length)];

    // 判断是否是链接类型
    if (match.resultType != NSTextCheckingTypeLink)
        return NO;

    // 确保整个字符串都被识别为链接
    if (!NSEqualRanges(match.range, NSMakeRange(0, trimmedString.length)))
        return NO;

    // 获取 NSURL 并判断协议是否为 http 或 https
    NSURL *url = match.URL;
    NSString *scheme = url.scheme.lowercaseString;
    if (![scheme isEqualToString:@"http"] && ![scheme isEqualToString:@"https"])
        return NO;

    // 检查 host 是否存在，避免无效链接如 “http://”
    if (url.host.length == 0)
        return NO;

    return YES;
}
@end
