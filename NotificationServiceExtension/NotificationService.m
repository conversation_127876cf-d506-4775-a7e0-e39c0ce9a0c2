//
//  NotificationService.m
//  NotificationServiceExtension
//
//  Created by 吴子航 on 2022/3/3.
//

#import "NotificationService.h"
#import "UPPushServiceExtension.h"

@interface NotificationService ()

@property (nonatomic, strong) id<UPPushServiceExtensionProtocol> serviceExtension;

@end

@implementation NotificationService

- (void)didReceiveNotificationRequest:(UNNotificationRequest *)request withContentHandler:(void (^)(UNNotificationContent *_Nonnull))contentHandler
{
    [self.serviceExtension didReceiveNotificationRequest:request with<PERSON>ontent<PERSON>and<PERSON>:contentHandler];
}

- (void)serviceExtensionTimeWillExpire
{
    [self.serviceExtension serviceExtensionTimeWillExpire];
}

- (id<UPPushServiceExtensionProtocol>)serviceExtension
{
    if (!_serviceExtension) {
        _serviceExtension = [[UPPushServiceExtension alloc] initWithExtension:self];
    }

    return _serviceExtension;
}
@end
