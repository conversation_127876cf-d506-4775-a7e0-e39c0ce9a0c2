#
#  Be sure to run `pod spec lint UPPush.podspec' to ensure this is a
#  valid spec and to remove all comments including this before submitting the spec.
#
#  To learn more about Podspec attributes see http://docs.cocoapods.org/specification.html
#  To see working Podspecs in the CocoaPods repo see https://github.com/CocoaPods/Specs/
#git clone http://limi@144.123.47.148:8091/uplus/ios/UPPush

Pod::Spec.new do |s|

  s.name         = "UPPush"
  s.version      = "1.5.4"

  s.author       = { 'wuzihang' => '<EMAIL>' }
  s.license      = { :type => 'MIT', :file => 'LICENSE' }
  s.homepage     = "http://EXAMPLE/UPPush"
  s.source       = { :git => "https://git.haier.net/uplus/ios/UPPush", :tag => s.version.to_s}
  s.summary      = "U+App UPPush component library for iOS platfrom"
  s.platform     = :ios, "10.0"
  s.requires_arc = true
  s.module_name  = "UPPush"
  s.subspec 'UPPush' do |ss|
    ss.source_files = 'UPPush/*.{h,m}','UPPush/**/*.{h,m}'
  end
  s.subspec 'UPPushCore' do |ss|
    ss.source_files = 'UPPushCore/*.{h,m}','UPPushCore/**/*.{h,m}'
  end
  s.subspec 'UPPushUI' do |ss|
    ss.source_files = 'UPPushUI/*.{h,m}','UPPushUI/**/*.{h,m}'
  end
  s.subspec 'UPPushServiceExtension' do |ss|
    ss.source_files = 'UPPushServiceExtension/*.{h,m}','UPPushServiceExtension/**/*.{h,m}'
  end
  s.ios.resource_bundle = { 'UPPushResource' => 'UPPushResource/*' }
  s.script_phases = [{
    :name => 'migrate service extension',
    :script => '"${PODS_TARGET_SRCROOT}/UPPushResource/ServiceExtension/MigrateServiceExtension.sh"',
    :execution_position => :before_compile
  },
  { 
    :name => 'clean redundant service extension',
    :script => '"${PODS_TARGET_SRCROOT}/UPPushResource/ServiceExtension/CleanRedundantExtension.sh"',
    :execution_position => :after_compile
  }]
  s.dependency  'AFNetworking','>=3.1.0'
  s.dependency  'upnetwork','>= 3.2.3'
  s.dependency  'uplog','>= 1.1.9'
  s.dependency  'MJExtension','>=3.2.1'
  s.dependency  'UPVDN','>= 2.5.9'
  s.dependency  'UHMasonry','>= 1.1.1'
  s.dependency  'UPTools/ModuleLanguage','>= 0.1.33'
  
end
