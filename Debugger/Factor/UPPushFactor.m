//
//  UPPushFactor.m
//  Debugger
//
//  Created by 吴子航 on 2022/11/26.
//

#import "UPPushFactor.h"

@interface UPPushFactor ()

@property (nonatomic, strong) UPPushRegisterFactor *registerFactor;

@end

@implementation UPPushFactor

- (instancetype)initWithRegisterFactor:(UPPushRegisterFactor *)registerFactor
{
    if (self = [super init]) {
        _registerFactor = registerFactor;
    }

    return self;
}

@end
