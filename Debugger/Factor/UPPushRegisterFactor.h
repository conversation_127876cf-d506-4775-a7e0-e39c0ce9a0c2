//
//  UPPushRegisterFactor.h
//  Debugger
//
//  Created by 吴子航 on 2022/11/26.
//

#import <Foundation/Foundation.h>
#import "UPPushManager.h"

NS_ASSUME_NONNULL_BEGIN

@interface UPPushRegisterFactor : NSObject

@property (nonatomic, strong, readonly) NSSet<NSString *> *abilities;
@property (nonatomic, assign, readonly) UPPushAbility pushAbility;

- (instancetype)initWithAbilities:(NSSet<NSString *> *)abilities;

@end

NS_ASSUME_NONNULL_END
