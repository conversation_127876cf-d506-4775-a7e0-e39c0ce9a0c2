//
//  UPPushRegisterFactor.m
//  Debugger
//
//  Created by 吴子航 on 2022/11/26.
//

#import "UPPushRegisterFactor.h"

@interface UPPushRegisterFactor ()

@property (nonatomic, strong) NSSet<NSString *> *abilities;
@property (nonatomic, assign) UPPushAbility pushAbility;
@property (nonatomic, strong) NSDictionary<NSString *, NSNumber *> *abilitySource;

@end

@implementation UPPushRegisterFactor

- (instancetype)initWithAbilities:(NSSet<NSString *> *)abilities
{
    if (self = [super init]) {
        _abilities = abilities;
        _abilitySource = @{
            @"Action" : @(UPPushAbility_Action),
            @"Notification" : @(UPPushAbility_Notification),
            @"Dialog" : @(UPPushAbility_Dialog),
            @"Voice" : @(UPPushAbility_Voice),
            @"Toast" : @(UPPushAbility_Toast),
            @"DownloadFile" : @(UPPushAbility_DownloadFile),
            @"DeliverData" : @(UPPushAbility_DeliverData),
            @"OpenApp" : @(UPPushAbility_OpenApp),
            @"OpenPage" : @(UPPushAbility_OpenPage),
            @"OpenLink" : @(UPPushAbility_OpenLink),
            @"CallApi" : @(UPPushAbility_CallApi),
            @"ControlDevice" : @(UPPushAbility_ControlDevice),
            @"ActivateApp" : @(UPPushAbility_ActivateApp),
            @"MakeVideoCall" : @(UPPushAbility_MakeVideoCall),
            @"ControlScene" : @(UPPushAbility_ControlScene)
        };
        _pushAbility = [self transform2Ability];
    }

    return self;
}

#pragma mark - private method
- (UPPushAbility)transform2Ability
{
    __block UPPushAbility ability = UPPushAbility_None;
    [_abilities enumerateObjectsUsingBlock:^(NSString *_Nonnull obj, BOOL *_Nonnull stop) {
      ability |= _abilitySource[obj].unsignedIntegerValue;
    }];

    return ability;
}

@end
