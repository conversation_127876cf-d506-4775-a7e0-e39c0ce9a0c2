//
//  UPJPushChannel.m
//  UPPush
//
//  Created by osiris on 2020/10/15.
//

#import "UPJPushChannel.h"
#import <JPush/JPUSHService.h>
#import <UserNotifications/UserNotifications.h>
#import "UPPushOriginalMessage.h"

NSString *const kUPPush_JPush_AppKey = @"2e3ac5fa492b670366aaac2a";

@interface UPJPushChannel () <JPUSHRegisterDelegate>

@end

@implementation UPJPushChannel
@synthesize delegate, dataSource;
- (instancetype)init
{
    if (self = [super init]) {
        [self configJPush];
    }

    return self;
}

#pragma mark - UPPushChannelProtocol
- (void)launch
{
    [JPUSHService registerDeviceToken:[self.dataSource pushTokenOfAPNs]];
}

- (NSString *)platformPushToken
{
    return JPUSHService.registrationID;
}

- (void)syncBadgeNumber:(NSInteger)badgeNumber
{
    [JPUSHService setBadge:badgeNumber];
    [[UIApplication sharedApplication] setApplicationIconBadgeNumber:badgeNumber];
}

- (NSInteger)badgeNumber
{
    return [UIApplication sharedApplication].applicationIconBadgeNumber;
}

- (void)didRecieveLocalNotification:(nonnull NSDictionary *)notification
{
    UPPushOriginalMessage *originalMessage = [[UPPushOriginalMessage alloc] init];
    originalMessage.originalMessage = notification;
    originalMessage.isCustom = NO;
    originalMessage.isLocal = YES;
    [self.delegate recieveMessage:originalMessage];
}


- (void)didRecieveRemoteNotification:(nonnull NSDictionary *)notification
{
    UPPushOriginalMessage *originalMessage = [[UPPushOriginalMessage alloc] init];
    originalMessage.originalMessage = notification;
    originalMessage.isCustom = NO;
    originalMessage.isLocal = NO;
    [self.delegate recieveMessage:originalMessage];
}

#pragma mark - JPUSHRegisterDelegate
- (void)jpushNotificationAuthorization:(JPAuthorizationStatus)status withInfo:(NSDictionary *)info
{
}

- (void)jpushNotificationCenter:(UNUserNotificationCenter *)center willPresentNotification:(UNNotification *)notification withCompletionHandler:(void (^)(NSInteger))completionHandler
{
    if ([notification.request.trigger isKindOfClass:[UNPushNotificationTrigger class]]) {
        UPPushOriginalMessage *originalMessage = [[UPPushOriginalMessage alloc] init];
        originalMessage.originalMessage = notification.request.content.userInfo;
        [self.delegate recieveMessage:originalMessage];
    }
    else {
        if (@available(iOS 14.0, *)) {
            completionHandler(UNNotificationPresentationOptionSound | UNNotificationPresentationOptionBanner | UNNotificationPresentationOptionList);
        }
        else {
            completionHandler(UNNotificationPresentationOptionAlert | UNNotificationPresentationOptionSound | UNNotificationPresentationOptionBadge);
        }
    }
}

- (void)jpushNotificationCenter:(UNUserNotificationCenter *)center didReceiveNotificationResponse:(UNNotificationResponse *)response withCompletionHandler:(void (^)(void))completionHandler
{
    UPPushOriginalMessage *originalMessage = [[UPPushOriginalMessage alloc] init];
    originalMessage.originalMessage = response.notification.request.content.userInfo;
    if ([response.notification.request.trigger isKindOfClass:[UNPushNotificationTrigger class]]) {
        originalMessage.isCustom = NO;
        originalMessage.isLocal = NO;
        [JPUSHService handleRemoteNotification:originalMessage.originalMessage];
    }
    else {
        originalMessage.isCustom = NO;
        originalMessage.isLocal = YES;
    }
    [self.delegate recieveMessage:originalMessage];
    completionHandler();
}

- (void)jpushNotificationCenter:(UNUserNotificationCenter *)center openSettingsForNotification:(UNNotification *)notification
{
    //iOS 12
}

#pragma mark - JPush Custom Notification
- (void)networkDidReceiveMessage:(NSNotification *)notification
{
    UPPushOriginalMessage *originalMessage = [[UPPushOriginalMessage alloc] init];
    originalMessage.originalMessage = notification.userInfo;
    originalMessage.isCustom = YES;
    originalMessage.isLocal = NO;
    [self.delegate recieveMessage:originalMessage];
}

#pragma mark - private
- (void)configJPush
{
    { /* JPush APNs消息配置 */
        JPUSHRegisterEntity *entity = [[JPUSHRegisterEntity alloc] init];
        entity.types = JPAuthorizationOptionAlert | JPAuthorizationOptionBadge | JPAuthorizationOptionSound;
        [JPUSHService registerForRemoteNotificationConfig:entity delegate:self];
        [JPUSHService setupWithOption:[self.dataSource launchingOption] appKey:kUPPush_JPush_AppKey channel:nil apsForProduction:YES];
        [JPUSHService setDebugMode];
    }

    { /* JPush 自定义消息配置 */
        NSNotificationCenter *defaultCenter = [NSNotificationCenter defaultCenter];
        [defaultCenter addObserver:self selector:@selector(networkDidReceiveMessage:) name:kJPFNetworkDidReceiveMessageNotification object:nil];
    }
}
@end
