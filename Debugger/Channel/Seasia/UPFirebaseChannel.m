//
//  UPFirebaseChannel.m
//  UPPush
//
//  Created by osiris on 2020/10/15.
//

#import "UPFirebaseChannel.h"
#import <UserNotifications/UNUserNotificationCenter.h>
#import <UIKit/UIKit.h>
#import "UPPushOriginalMessage.h"
@import Firebase;

@interface UPFirebaseChannel () <UNUserNotificationCenterDelegate, FIRMessagingDelegate>

@property (nonatomic, copy) NSString *currentFCMToken;

@end

@implementation UPFirebaseChannel
@synthesize delegate, dataSource;

- (instancetype)init
{
    if (self = [super init]) {
        [self configAPNsPush];
        [self configFirebase];
    }

    return self;
}
#pragma mark - UPPushChannelProtocol
- (void)launch
{
    /* 需设置FirebaseAppDelegateProxyEnabled = NO */
    [[FIRMessaging messaging] setAPNSToken:[self.dataSource pushTokenOfAPNs]];
}

- (NSString *)platformPushToken
{
    self.currentFCMToken = [FIRMessaging messaging].FCMToken;
    return [FIRMessaging messaging].FCMToken;
}

- (void)syncBadgeNumber:(NSInteger)badgeNumber
{
    [[UIApplication sharedApplication] setApplicationIconBadgeNumber:badgeNumber];
}

- (NSInteger)badgeNumber
{
    return [UIApplication sharedApplication].applicationIconBadgeNumber;
}

- (void)didRecieveLocalNotification:(nonnull NSDictionary *)notification
{
    UPPushOriginalMessage *originalMessage = [[UPPushOriginalMessage alloc] init];
    originalMessage.originalMessage = notification;
    originalMessage.isCustom = NO;
    originalMessage.isLocal = YES;
    [self.delegate recieveMessage:originalMessage];
}


- (void)didRecieveRemoteNotification:(nonnull NSDictionary *)notification
{
    UPPushOriginalMessage *originalMessage = [[UPPushOriginalMessage alloc] init];
    originalMessage.originalMessage = notification;
    originalMessage.isCustom = NO;
    originalMessage.isLocal = NO;
    [self.delegate recieveMessage:originalMessage];
}

#pragma mark - UNUserNotificationCenterDelegate
- (void)userNotificationCenter:(UNUserNotificationCenter *)center willPresentNotification:(UNNotification *)notification withCompletionHandler:(void (^)(UNNotificationPresentationOptions))completionHandler
{
    if ([notification.request.trigger isKindOfClass:[UNPushNotificationTrigger class]]) {
        UPPushOriginalMessage *originalMessage = [[UPPushOriginalMessage alloc] init];
        originalMessage.originalMessage = notification.request.content.userInfo;
        [self.delegate recieveMessage:originalMessage];
    }
    else {
        if (@available(iOS 14.0, *)) {
            completionHandler(UNNotificationPresentationOptionBadge | UNNotificationPresentationOptionList | UNNotificationPresentationOptionBanner);
        }
        else {
            completionHandler(UNNotificationPresentationOptionBadge | UNNotificationPresentationOptionAlert | UNNotificationPresentationOptionSound);
        }
    }
}

- (void)userNotificationCenter:(UNUserNotificationCenter *)center didReceiveNotificationResponse:(UNNotificationResponse *)response withCompletionHandler:(void (^)(void))completionHandler
{
    UPPushOriginalMessage *originalMessage = [[UPPushOriginalMessage alloc] init];
    originalMessage.originalMessage = response.notification.request.content.userInfo;
    if ([response.notification.request.trigger isKindOfClass:[UNPushNotificationTrigger class]]) {
        originalMessage.isCustom = NO;
        originalMessage.isLocal = NO;
    }
    else {
        originalMessage.isCustom = NO;
        originalMessage.isLocal = YES;
    }
    [self.delegate recieveMessage:originalMessage];
    completionHandler();
}

#pragma mark - private methods
- (void)configFirebase
{
    [FIRApp configure];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pushTokenRefresh) name:FIRMessagingRegistrationTokenRefreshedNotification object:nil];
}

- (void)configAPNsPush
{
    UNAuthorizationOptions authOptions = UNAuthorizationOptionAlert | UNAuthorizationOptionSound | UNAuthorizationOptionBadge;
    [[UNUserNotificationCenter currentNotificationCenter] requestAuthorizationWithOptions:authOptions
                                                                        completionHandler:^(BOOL granted, NSError *_Nullable error){
                                                                        }];
    [UNUserNotificationCenter currentNotificationCenter].delegate = self;
    [[UIApplication sharedApplication] registerForRemoteNotifications];
}

- (void)pushTokenRefresh
{
    if (![self.delegate respondsToSelector:@selector(pushTokenRefresh)] || [self.currentFCMToken isEqualToString:[self platformPushToken]]) {
        return;
    }

    [self.delegate pushTokenRefresh];
}
@end
