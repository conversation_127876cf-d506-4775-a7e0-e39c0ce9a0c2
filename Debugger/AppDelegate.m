//
//  AppDelegate.m
//  Debugger
//
//  Created by osiris on 2020/10/15.
//

#import "AppDelegate.h"
#import <MJExtension/MJExtension.h>
#import "UPPushManager.h"
#import "UPJPushChannel.h"
#import "UPFirebaseChannel.h"
#import "UPPushBuildInUIInterceptor.h"
#import "UPPushMessage.h"

const NSNotificationName SendMessage2Flutter = @"Flutter";

@interface AppDelegate () <UPPushProviderProtocol, UPPushBuildInInterceptorDelegate, UPPushBuildInInterceptorObserverProtocol>

@property (nonatomic, strong) NSDictionary *launchOptions;
@property (nonatomic, strong) NSData *deviceToken;
@property (nonatomic, strong) id<UPPushChannelProtocol> channel;

@end

@implementation AppDelegate
@synthesize window;

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
    _launchOptions = launchOptions;
    if ([self provideChannelType] == UPPushChannel_Seasia) {
        _channel = [UPFirebaseChannel new];
    }
    else {
        _channel = [UPJPushChannel new];
    }

    [[UPPushManager instance] initializeUPPush:self channel:_channel];
    UPPushBuildInUIInterceptor *buildInInterceptor = [[UPPushBuildInUIInterceptor alloc] init];
    buildInInterceptor.delegate = self;
    [buildInInterceptor setObserver:self];
    [[UPPushManager instance].push registerInterceptor:buildInInterceptor];
    return YES;
}

- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken
{
    NSUInteger dataLength = deviceToken.length;
    if (dataLength == 0) {
        return;
    }
    const unsigned char *dataBuffer = (const unsigned char *)deviceToken.bytes;
    NSMutableString *hexTokenString = [NSMutableString stringWithCapacity:(dataLength * 2)];
    for (int i = 0; i < dataLength; ++i) {
        [hexTokenString appendFormat:@"%02x", dataBuffer[i]];
    }
    _deviceToken = deviceToken;
    [[UPPushManager instance]
            .push registerPush:UPPushAbility_MakeVideoCall
                      callback:^(BOOL result){

                      }];
}
- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error
{
}

#pragma mark - UPPushProviderProtocol
- (UPPushEnvironment)environment
{
    return UPPushEnvironment_Production;
}

- (nonnull NSDictionary *)launchingOption
{
    return _launchOptions;
}

- (UPPushChannelType)provideChannelType
{
    return UPPushChannel_Home;
}

- (nonnull NSData *)providePushToken
{
    return _deviceToken;
}

#pragma mark - UPPushBuildInInterceptorDelegate

- (void)logoutForChangePassword
{
}

- (void)track:(NSString *)eventID param:(NSDictionary *)param
{
}

- (void)handleDirectJumpMessage:(id<UPPushMessageProtocol>)message
{
}

- (BOOL)customHandleDirectJumpMessage:(id<UPPushMessageProtocol>)message
{
    return NO;
}

- (void)confirmLogoutForOthersLogin
{
}

#pragma mark - UPPushBuildInInterceptorObserverProtocol
- (void)willProcessMessage:(id<UPPushMessageProtocol>)message
{
    if (!message.isLocal) {
        NSString *messageJson = [message.decodeContent mj_JSONString];
        [NSNotificationCenter.defaultCenter postNotificationName:SendMessage2Flutter
                                                          object:self
                                                        userInfo:@{
                                                            @"messageName" : @"PushMessageApi",
                                                            @"messageData" : messageJson
                                                        }];
    }
}

- (void)didProcessMessage:(id<UPPushMessageProtocol>)message
{
}
@end
