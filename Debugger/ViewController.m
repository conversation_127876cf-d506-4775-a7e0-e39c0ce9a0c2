//
//  ViewController.m
//  Debugger
//
//  Created by osiris on 2020/10/15.
//

#import "ViewController.h"
#import <upnetwork/UPNetwork.h>
#import <upuserdomain/UpUserDomainHolder.h>
#import "UPUserLoginApi.h"
#import "SEUserLoginApi.h"
#import "UPPushSeasiaDataSource.h"
#import "UPPushHomeDataSource.h"
#import "AppDelegate.h"
#import "UPPushChannelProtocol.h"

@import UserNotifications;

@interface ViewController () <UpUserDomainObserver>

@property (nonatomic, weak) IBOutlet UITextField *userName;
@property (nonatomic, weak) IBOutlet UITextField *password;
@property (nonatomic, weak) IBOutlet UISegmentedControl *platformControl;
@property (nonatomic, weak) IBOutlet UILabel *resultLabel;

@end

@implementation ViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    [self initUserDomain];
    [self senderinitNetworkEnvironment];
}


- (IBAction)login:(id)sender
{
    void (^success)(NSObject *) = ^(NSObject *responseObject) {
      NSDictionary *oauthDataDic = [responseObject valueForKey:@"data"];
      NSDictionary *tokenDic = [oauthDataDic objectForKey:@"tokenInfo"];
      NSLog(@"%@", tokenDic);
      [self updateOauthDataWithDict:tokenDic];
      dispatch_async(dispatch_get_main_queue(), ^{
        self.resultLabel.text = @"登录成功";
      });
    };
    void (^failure)(NSError *, NSDictionary *) = ^(NSError *error, NSDictionary *info) {
      dispatch_async(dispatch_get_main_queue(), ^{
        self.resultLabel.text = @"登录失败";
      });
    };
    if (_platformControl.selectedSegmentIndex == 0) {
        [[[UPUserLoginApi alloc] initWithUsername:_userName.text password:_password.text] startRequestWithSuccess:success failure:failure];
    }
    else {
        [[[SEUserLoginApi alloc] initWithUsername:_userName.text password:_password.text] startRequestWithSuccess:success failure:failure];
    }
}

- (IBAction)logout:(id)sender
{
    [[UpUserDomainHolder instance]
            .userDomain logOut:^(UserDomainSampleResult *_Nonnull result) {
      dispatch_async(dispatch_get_main_queue(), ^{
        self.resultLabel.text = @"已登出";
      });
    }];
}

- (IBAction)resignResponder:(id)sender
{
    [self.view endEditing:YES];
}

- (IBAction)senderinitNetworkEnvironment
{
    [self clear];
    if (_platformControl.selectedSegmentIndex == 0) {
        [UPNetworkSettings sharedSettings].appID = @"MB-UZHSH-0001";
        [UPNetworkSettings sharedSettings].appKey = @"5dfca8714eb26e3a776e58a8273c8752";
        [UPNetworkSettings sharedSettings].appVersion = @"6.17.0";
    }
    else {
        //<EMAIL> Haier202007
        //7303251263 haierisddemo1
        [UPNetworkSettings sharedSettings].appID = @"MB-SHEYJDNYB-0000";
        [UPNetworkSettings sharedSettings].appKey = @"5959a2a4679f7990ba3cc557daa53986";
        [UPNetworkSettings sharedSettings].appVersion = @"2.1.2";
    }
}

- (IBAction)registerPush:(id)sender
{
    if (_platformControl.selectedSegmentIndex == 0) {
        UPPushHomeDataSource *homeDataSource = [[UPPushHomeDataSource alloc] init];
        [homeDataSource registerPush:@"1114a89792122af7fc4"
                             ability:UPPushAbility_Action | UPPushAbility_Notification
                              result:^(id<UPPushResultProtocol> _Nonnull result) {
                                dispatch_async(dispatch_get_main_queue(), ^{
                                  if (result.isSuccess) {
                                      self.resultLabel.text = @"注册推送成功";
                                  }
                                  else {
                                      self.resultLabel.text = @"注册推送失败";
                                  }
                                });
                              }];
    }
    else {
        UPPushSeasiaDataSource *seasiaDataSource = [[UPPushSeasiaDataSource alloc] init];
        [seasiaDataSource registerPush:@"1114a89792122af7fc4"
                               ability:UPPushAbility_Action | UPPushAbility_Notification
                                result:^(id<UPPushResultProtocol> _Nonnull result) {
                                  dispatch_async(dispatch_get_main_queue(), ^{
                                    if (result.isSuccess) {
                                        self.resultLabel.text = @"注册推送成功";
                                    }
                                    else {
                                        self.resultLabel.text = @"注册推送失败";
                                    }
                                  });
                                }];
    }
}

- (IBAction)unregisterPush:(id)sender
{
    if (_platformControl.selectedSegmentIndex == 0) {
        UPPushHomeDataSource *homeDataSource = [[UPPushHomeDataSource alloc] init];
        [homeDataSource unregisterPush:^(id<UPPushResultProtocol> _Nonnull result) {
          dispatch_async(dispatch_get_main_queue(), ^{
            if (result.isSuccess) {
                self.resultLabel.text = @"注销推送成功";
            }
            else {
                self.resultLabel.text = @"注销推送失败";
            }
          });
        }];
    }
    else {
        UPPushSeasiaDataSource *seasiaDataSource = [[UPPushSeasiaDataSource alloc] init];
        [seasiaDataSource unregisterPush:^(id<UPPushResultProtocol> _Nonnull result) {
          dispatch_async(dispatch_get_main_queue(), ^{
            if (result.isSuccess) {
                self.resultLabel.text = @"注销推送成功";
            }
            else {
                self.resultLabel.text = @"注销推送失败";
            }
          });
        }];
    }
}

- (IBAction)reportMessage:(id)sender
{
    if (_platformControl.selectedSegmentIndex == 0) {
        UPPushHomeDataSource *homeDataSource = [[UPPushHomeDataSource alloc] init];
        [homeDataSource reportMessage:@"2251901966662332"
                               result:^(id<UPPushResultProtocol> _Nonnull result) {
                                 dispatch_async(dispatch_get_main_queue(), ^{
                                   if (result.isSuccess) {
                                       self.resultLabel.text = @"上报状态成功";
                                   }
                                   else {
                                       self.resultLabel.text = @"上报状态失败";
                                   }
                                 });
                               }];
    }
    else {
        UPPushSeasiaDataSource *seasiaDataSource = [[UPPushSeasiaDataSource alloc] init];
        [seasiaDataSource reportMessage:@"2251901966662332"
                                 result:^(id<UPPushResultProtocol> _Nonnull result) {
                                   dispatch_async(dispatch_get_main_queue(), ^{
                                     if (result.isSuccess) {
                                         self.resultLabel.text = @"上报状态成功";
                                     }
                                     else {
                                         self.resultLabel.text = @"上报状态失败";
                                     }
                                   });
                                 }];
    }
}

- (IBAction)clearData:(id)sender
{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSArray<NSString *> *keys = userDefaults.dictionaryRepresentation.allKeys;
    for (NSString *key in keys) {
        if ([key hasPrefix:@"duplicate_Message_"]) {
            [userDefaults removeObjectForKey:key];
            [userDefaults synchronize];
        }
    }
}

- (IBAction)createMessage
{
    /*
     eyJib2R5Ijp7InZpZXciOnsic2hvd1R5cGUiOiIyIiwiYnRucyI6W3siY2FsbElkIjotMTAwMCwidGV4dCI6IuaIkeWGjeaDs+aDsyJ9LHsiY2FsbElkIjoxMzAwMDEyMDAxLCJ0ZXh0Ijoi5ZCM5oSPIn1dLCJ0aXRsZSI6Iua0l+iho+a2sueJqeiBlOaUr+S7mOaPkOmGkiIsImNvbnRlbnQiOiLmtJfooaPmtrLljbPlsIbkuI3otrPvvIzlt7LkuLrmgqjliqDotK3vvIzmmK/lkKbkuIvljZXvvJ8ifSwiZXh0RGF0YSI6eyJleHBpcmVUaW1lIjo4NjQwMCwicGFnZSI6eyJjYWxsSWQiOiIxMzAwMDEyMDAxIiwidXJsIjoiaHR0cDovL3Vob21lLmhhaWVyLm5ldC93YXNoU2hhcmUvY2FydHJpZGdlUHVyY2hhc2UvaW5kZXguaHRtbD9tYWM9MkMzN0M1M0Y2MEY5JnR5cGVpZD0yMDFjNTE4OTBjMzFjMzA4MDUwMTAwMjE4MDAxOTE0NDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDQwJm5lZWRBdXRoTG9naW49MSMvb3JkZXJCb3gifSwicmV2aWV3UGFnZSI6Imh0dHA6Ly91aG9tZS5oYWllci5uZXQvd2FzaFNoYXJlL2NhcnRyaWRnZVB1cmNoYXNlL2luZGV4Lmh0bWw/bWFjPTJDMzdDNTNGNjBGOSZ0eXBlaWQ9MjAxYzUxODkwYzMxYzMwODA1MDEwMDIxODAwMTkxNDQwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDA0MCZuZWVkQXV0aExvZ2luPTEjL29yZGVyQm94In19LCJtc2dOYW1lIjoiREVWX1dNX0lOU1VGRklDSUVOVF9ERVRFUkdFTlQiLCJtc2dJZCI6IjcxODQxNDE0Nzk4MzA3NzM3NyIsImJ1c2luZXNzVHlwZSI6MSwicHJpb3JpdHkiOjJ9
     */

    /*
     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
     */

    /*
     eyJib2R5Ijp7InZpZXciOnsic2hvd1R5cGUiOi0xLCJ0aXRsZSI6Iua0l+iho+WujOaIkCIsImNvbnRlbnQiOiLooaPmnI3mtJflrozkuobvvIzngrnlh7vmlLbog73ph4/lkKfvvIE+PiJ9LCJleHREYXRhIjp7ImV4cGlyZVRpbWUiOjg2NDAwLCJwYWdlIjp7ImNhbGxJZCI6IjEiLCJ1cmwiOiJodHRwczovL3NtYXJ0d2FzaGVyLmhhaWVyLm5ldC93YXNoU2hhcmUvd2FzaGVkUmVwb3J0L2luZGV4Lmh0bWw/Y29udGFpbmVyX3R5cGU9MyZoaWRlc0JvdHRvbUJhcldoZW5QdXNoZWQ9MSZkZXZpY2VJZD1EQzMzMEQyNkM4OEUmdHlwZUlkPTExMWMxMjAwMjQwMDA4MTAwNDAxMDAzMTgwMDA5NjAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAmd2FzaEN5Y2xlPTUmZGV2aWNlVHlwZT0wNDAwMiZzdXBwb3J0X2RyeT0xJnN1cHBvcnRfZGlzcGVyc2U9MSZzZWF0PTAmbmVlZEF1dGhMb2dpbj0xJmZyb21QdXNoPTEjL3JlcG9ydENvbnRlbnQifSwicmV2aWV3UGFnZSI6Imh0dHBzOi8vc21hcnR3YXNoZXIuaGFpZXIubmV0L3dhc2hTaGFyZS93YXNoZWRSZXBvcnQvaW5kZXguaHRtbD9jb250YWluZXJfdHlwZT0zJmhpZGVzQm90dG9tQmFyV2hlblB1c2hlZD0xJmRldmljZUlkPURDMzMwRDI2Qzg4RSZ0eXBlSWQ9MTExYzEyMDAyNDAwMDgxMDA0MDEwMDMxODAwMDk2MDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMCZ3YXNoQ3ljbGU9NSZkZXZpY2VUeXBlPTA0MDAyJnN1cHBvcnRfZHJ5PTEmc3VwcG9ydF9kaXNwZXJzZT0xJnNlYXQ9MCZuZWVkQXV0aExvZ2luPTEmZnJvbVB1c2g9MSMvcmVwb3J0Q29udGVudCJ9fSwibXNnTmFtZSI6IkRFVl9XTV9XQVNIX0ZJTklTSCIsIm1zZ0lkIjoiNzI1MTQ5NDYwOTY1MTg3NTg1IiwiYnVzaW5lc3NUeXBlIjoxLCJwcmlvcml0eSI6Mn0=
     */

    NSDictionary *message = @{
        @"content" : @"eyJib2R5Ijp7InZpZXciOnsic2hvd1R5cGUiOiItMSJ9LCJleHREYXRhIjp7InRhcmdldFBhZ2UiOiJtcGFhczovL21lc3NhZ2VDZW50ZXI/Iy9jbGFzc2lmeS8xP3NvdXJjZT0xIn0sImV4dFBhcmFtIjp7InRhcmdldFBhZ2UiOnsiYWN0V2l0aE5vdGlmeSI6ZmFsc2UsInB1c2hSdWxlSWQiOm51bGx9fX0sIm1zZ05hbWUiOiJERVZfV01fSU5TVUZGSUNJRU5UX0RFVEVSR0VOVCIsIm1zZ0lkIjoiNzE4NDE0MTQ3OTgzMDc3Mzc3IiwiYnVzaW5lc3NUeXBlIjoxLCJwcmlvcml0eSI6Mn0="
    };
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [(id<UPPushChannelProtocol>)[(AppDelegate *)UIApplication.sharedApplication.delegate valueForKey:@"channel"] didRecieveRemoteNotification:message];
    });
}

- (IBAction)push:(id)sender
{
    [self.navigationController pushViewController:[ViewController new] animated:YES];
}
#pragma mark - private

- (void)initUserDomain
{
    [[UpUserDomainHolder instance] initializeUserDomainForHomeland];
    [[UpUserDomainHolder instance].userDomain addObserver:self];
}

- (void)updateOauthDataWithDict:(NSDictionary *)tokenDic
{
    [[UpUserDomainHolder instance].userDomain updateOauthData:tokenDic[@"accountToken"] refreshToken:tokenDic[@"refreshToken"] uhome_access_token:tokenDic[@"uhomeAccessToken"] expires_in:[NSString stringWithFormat:@"%@", tokenDic[@"expiresIn"]] scope:@"" token_type:tokenDic[@"tokenType"] uhome_user_id:tokenDic[@"uhomeUserId"]];
}

- (void)clear
{
    _password.text = @"";
    _userName.text = @"";
    _resultLabel.text = @"Result";
}
@end
