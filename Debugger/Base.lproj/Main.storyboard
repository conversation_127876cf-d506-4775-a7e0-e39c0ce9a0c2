<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="18122" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="u4D-0D-nWD">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="18093"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="ViewController" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC" customClass="UIControl">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="用户名：" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tLs-BJ-8dF">
                                <rect key="frame" x="35" y="360" width="70" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="密    码：" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Fnm-2B-YLH">
                                <rect key="frame" x="35" y="411" width="70" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="ezO-0p-I2m">
                                <rect key="frame" x="113" y="353.5" width="281" height="34"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" keyboardType="phonePad"/>
                            </textField>
                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="EGV-Ju-43x">
                                <rect key="frame" x="113" y="404.5" width="281" height="34"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits"/>
                            </textField>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Fug-EG-r45">
                                <rect key="frame" x="113" y="498.5" width="31" height="30"/>
                                <state key="normal" title="登录"/>
                                <connections>
                                    <action selector="login:" destination="BYZ-38-t0r" eventType="touchUpInside" id="TpU-rF-2be"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sSa-CZ-LqL">
                                <rect key="frame" x="219" y="498.5" width="31" height="30"/>
                                <state key="normal" title="注销"/>
                                <connections>
                                    <action selector="logout:" destination="BYZ-38-t0r" eventType="touchUpInside" id="5uH-XL-i9r"/>
                                </connections>
                            </button>
                            <segmentedControl opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="top" segmentControlStyle="plain" selectedSegmentIndex="0" translatesAutoresizingMaskIntoConstraints="NO" id="xa4-JJ-nmm">
                                <rect key="frame" x="147.5" y="269" width="119" height="32"/>
                                <segments>
                                    <segment title="Home"/>
                                    <segment title="Seasia"/>
                                </segments>
                                <connections>
                                    <action selector="senderinitNetworkEnvironment" destination="BYZ-38-t0r" eventType="valueChanged" id="GZ2-1i-5KM"/>
                                </connections>
                            </segmentedControl>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="zgx-si-gsP">
                                <rect key="frame" x="27" y="578.5" width="62" height="30"/>
                                <state key="normal" title="注册推送"/>
                                <connections>
                                    <action selector="registerPush:" destination="BYZ-38-t0r" eventType="touchUpInside" id="XNm-hV-i9h"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="XD3-kt-9zk">
                                <rect key="frame" x="176" y="578.5" width="62" height="30"/>
                                <state key="normal" title="注销推送"/>
                                <connections>
                                    <action selector="unregisterPush:" destination="BYZ-38-t0r" eventType="touchUpInside" id="AAf-pm-XC1"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="QUn-aZ-2Hm">
                                <rect key="frame" x="323" y="578.5" width="62" height="30"/>
                                <state key="normal" title="上报状态"/>
                                <connections>
                                    <action selector="reportMessage:" destination="BYZ-38-t0r" eventType="touchUpInside" id="JCL-4c-YAN"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Result" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eNe-x1-FlM">
                                <rect key="frame" x="183" y="168" width="48" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" systemColor="systemPinkColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cmG-wc-CXc">
                                <rect key="frame" x="192" y="724" width="31" height="30"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="跳转"/>
                                <connections>
                                    <action selector="push:" destination="BYZ-38-t0r" eventType="touchUpInside" id="8N5-Z4-atc"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="EGV-Ju-43x" firstAttribute="centerY" secondItem="Fnm-2B-YLH" secondAttribute="centerY" id="1He-IJ-sgu"/>
                            <constraint firstItem="Fnm-2B-YLH" firstAttribute="leading" secondItem="tLs-BJ-8dF" secondAttribute="leading" id="1ef-mu-oAW"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="ezO-0p-I2m" secondAttribute="trailing" constant="20" id="6Ij-Fl-VQb"/>
                            <constraint firstItem="EGV-Ju-43x" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="Fnm-2B-YLH" secondAttribute="trailing" constant="8" symbolic="YES" id="7Gk-6H-NmF"/>
                            <constraint firstItem="XD3-kt-9zk" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="zgx-si-gsP" secondAttribute="trailing" symbolic="YES" id="I0d-Ry-yrm"/>
                            <constraint firstItem="tLs-BJ-8dF" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="35" id="LIS-Ls-Rgq"/>
                            <constraint firstItem="tLs-BJ-8dF" firstAttribute="top" secondItem="xa4-JJ-nmm" secondAttribute="bottom" constant="60" id="M9d-cr-apQ"/>
                            <constraint firstItem="EGV-Ju-43x" firstAttribute="trailing" secondItem="ezO-0p-I2m" secondAttribute="trailing" id="MP7-DY-nk2"/>
                            <constraint firstItem="xa4-JJ-nmm" firstAttribute="top" secondItem="eNe-x1-FlM" secondAttribute="bottom" constant="80" id="MU9-tA-P1y"/>
                            <constraint firstItem="ezO-0p-I2m" firstAttribute="leading" secondItem="tLs-BJ-8dF" secondAttribute="trailing" constant="8" symbolic="YES" id="Rxh-2j-Oja"/>
                            <constraint firstItem="tLs-BJ-8dF" firstAttribute="centerY" secondItem="ezO-0p-I2m" secondAttribute="centerY" id="S78-kq-HpC"/>
                            <constraint firstItem="Fug-EG-r45" firstAttribute="centerY" secondItem="sSa-CZ-LqL" secondAttribute="centerY" id="Ugo-lN-Kjb"/>
                            <constraint firstItem="Fug-EG-r45" firstAttribute="leading" secondItem="EGV-Ju-43x" secondAttribute="leading" id="VRC-lj-5BR"/>
                            <constraint firstItem="sSa-CZ-LqL" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="Fug-EG-r45" secondAttribute="trailing" constant="8" symbolic="YES" id="Zn4-Iy-wvc"/>
                            <constraint firstItem="eNe-x1-FlM" firstAttribute="centerX" secondItem="6Tk-OE-BBY" secondAttribute="centerX" id="ac1-GN-GSS"/>
                            <constraint firstItem="XD3-kt-9zk" firstAttribute="top" secondItem="Fug-EG-r45" secondAttribute="bottom" constant="50" id="d5G-Wr-WRQ"/>
                            <constraint firstItem="Fnm-2B-YLH" firstAttribute="top" secondItem="tLs-BJ-8dF" secondAttribute="bottom" constant="30" id="gaB-1e-Luj"/>
                            <constraint firstItem="XD3-kt-9zk" firstAttribute="centerY" secondItem="zgx-si-gsP" secondAttribute="centerY" id="ifn-qM-0wd"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="QUn-aZ-2Hm" secondAttribute="trailing" constant="29" id="kKd-1Y-Yxt"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="sSa-CZ-LqL" secondAttribute="trailing" constant="164" id="khO-od-8TT"/>
                            <constraint firstItem="XD3-kt-9zk" firstAttribute="centerX" secondItem="6Tk-OE-BBY" secondAttribute="centerX" id="nKe-ub-Z46"/>
                            <constraint firstItem="QUn-aZ-2Hm" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="XD3-kt-9zk" secondAttribute="trailing" symbolic="YES" id="pB7-fM-cnF"/>
                            <constraint firstItem="Fug-EG-r45" firstAttribute="top" secondItem="EGV-Ju-43x" secondAttribute="bottom" constant="60" id="pjK-4S-71Q"/>
                            <constraint firstItem="EGV-Ju-43x" firstAttribute="leading" secondItem="ezO-0p-I2m" secondAttribute="leading" id="sJ6-TX-RDu"/>
                            <constraint firstItem="xa4-JJ-nmm" firstAttribute="centerX" secondItem="6Tk-OE-BBY" secondAttribute="centerX" id="v48-Ss-uhK"/>
                            <constraint firstItem="XD3-kt-9zk" firstAttribute="centerY" secondItem="QUn-aZ-2Hm" secondAttribute="centerY" id="vzf-ES-8r8"/>
                            <constraint firstItem="eNe-x1-FlM" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" constant="80" id="y67-QH-qx9"/>
                            <constraint firstItem="zgx-si-gsP" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="27" id="zki-6U-w0w"/>
                        </constraints>
                        <connections>
                            <action selector="resignResponder:" destination="BYZ-38-t0r" eventType="touchUpInside" id="vVd-4U-4eG"/>
                        </connections>
                    </view>
                    <navigationItem key="navigationItem" id="7jR-lS-tnK">
                        <barButtonItem key="leftBarButtonItem" title="清数据" id="tEJ-Yr-ABb">
                            <connections>
                                <action selector="clearData:" destination="BYZ-38-t0r" id="Ki2-jY-8Pp"/>
                            </connections>
                        </barButtonItem>
                        <barButtonItem key="rightBarButtonItem" title="测试消息" id="Lvv-kp-jsw">
                            <connections>
                                <action selector="createMessage" destination="BYZ-38-t0r" id="Qqo-vF-WqG"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="password" destination="EGV-Ju-43x" id="cTt-xv-n2N"/>
                        <outlet property="platformControl" destination="xa4-JJ-nmm" id="zEk-DM-BtO"/>
                        <outlet property="resultLabel" destination="eNe-x1-FlM" id="REP-yU-v5m"/>
                        <outlet property="userName" destination="ezO-0p-I2m" id="Zs2-IE-ync"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="300" y="-526"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="uwy-G1-10e">
            <objects>
                <navigationController id="u4D-0D-nWD" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="Dv6-OD-6SB">
                        <rect key="frame" x="0.0" y="44" width="414" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="BYZ-38-t0r" kind="relationship" relationship="rootViewController" id="yxP-cG-2yX"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="5Zt-Zb-Zwd" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-655" y="-526"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemPinkColor">
            <color red="1" green="0.17647058823529413" blue="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
