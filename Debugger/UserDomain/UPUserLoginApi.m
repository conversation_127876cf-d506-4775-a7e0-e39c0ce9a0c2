//
//  UPUserLoginApi.m
//  UserDomainAPIs
//
//  Created by 闫达 on 2020/8/6.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPUserLoginApi.h"

@interface UPUserLoginApi ()
@property (nonatomic, copy) NSString *username;
@property (nonatomic, copy) NSString *password;
@end

@implementation UPUserLoginApi
- (instancetype)initWithUsername:(NSString *)username password:(NSString *)password
{
    if (self = [super init]) {
        self.username = username;
        self.password = password;
    }
    return self;
}
- (NSString *)name
{
    return @"用户登录";
}

- (NSString *)baseURL
{
    return @"https://zj.haier.net";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSString *)path
{
    return @"/oauthserver/account/v1/login";
}

- (NSObject *)requestBody
{
    return @{ @"username" : self.username ?: @"",
              @"password" : self.password ?: @"" };
}


- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *dict = [UPCommonServerHeader uwsHeaderWithUrlString:self.path body:self.requestBody].mutableCopy;
    [dict removeObjectForKey:@"accessToken"];
    return dict;
}
@end
