source 'https://git.haier.net/uplus/shell/cocoapods/Specs.git'
source 'https://github.com/CocoaPods/Specs.git'


# Uncomment the next line to define a global platform for your project
platform :ios, '10.0'

abstract_target 'UPPush_abstract_pod' do
  pod 'AFNetworking','4.0.1'
  pod 'upnetwork','4.0.6'
  pod 'uplog','1.5.1'
  pod 'MJExtension','3.2.1'
  target 'UPPush' do
    
  end
  target 'UPPushCore' do
  
  end  
  target 'UPPushUI' do
    pod 'UPVDN','2.7.1.1.2023032102'
    pod 'UHMasonry','1.1.2.2023060801'
    pod 'UPTools/ModuleLanguage','0.1.33'
  end
  target 'Debugger' do
    pod 'JPush', '4.8.1'
    pod 'JCore', '3.2.9'
    pod 'Firebase/Analytics', '8.12.1'
    pod 'Firebase/Messaging', '8.12.1'
    pod 'upuserdomain', '3.15.1'
  end
  target 'UPPushTests' do
    pod 'Cucumberish','1.4.0'
    pod 'OCMock','3.8.1'
  end
  target 'UPPushServiceExtension' do
    inherit! :search_paths
    pod 'upnetwork', '4.0.6'
  end
  target 'NotificationServiceExtension' do
    inherit! :search_paths
    pod 'upnetwork', '4.0.6'
  end
  use_modular_headers!
  
end
