//
//  UPFirebaseChannel.m
//  UPPush
//
//  Created by osiris on 2020/10/15.
//

#import "UPFirebaseChannel.h"
#import <UserNotifications/UNUserNotificationCenter.h>
#import <UIKit/UIKit.h>
#import <UPPushCore/UPPushOriginalMessage.h>
@import Firebase;

@interface UPFirebaseChannel () <UNUserNotificationCenterDelegate, FIRMessagingDelegate>

@end

@implementation UPFirebaseChannel
@synthesize delegate, dataSource;

- (instancetype)init
{
    if (self = [super init]) {
        [self configAPNsPush];
        [self configFirebase];
    }

    return self;
}
#pragma mark - UPPushChannelProtocol
- (void)launch
{
    /* firebase 不需要设置 */
}

- (NSString *)platformPushToken
{
    return [FIRMessaging messaging].FCMToken;
}

- (void)syncBadgeNumber:(NSInteger)badgeNumber
{
    [[UIApplication sharedApplication] setApplicationIconBadgeNumber:badgeNumber];
}

- (NSInteger)badgeNumber
{
    return [UIApplication sharedApplication].applicationIconBadgeNumber;
}
#pragma mark - UNUserNotificationCenterDelegate
- (void)userNotificationCenter:(UNUserNotificationCenter *)center willPresentNotification:(UNNotification *)notification withCompletionHandler:(void (^)(UNNotificationPresentationOptions))completionHandler
{
    if ([notification.request.trigger isKindOfClass:[UNPushNotificationTrigger class]]) {
        UPPushOriginalMessage *originalMessage = [[UPPushOriginalMessage alloc] init];
        originalMessage.originalMessage = notification.request.content.userInfo;
        [self.delegate reciveMessage:originalMessage];
    }
    else {
        if (@available(iOS 14.0, *)) {
            completionHandler(UNNotificationPresentationOptionBadge | UNNotificationPresentationOptionList | UNNotificationPresentationOptionBanner);
        }
        else {
            completionHandler(UNNotificationPresentationOptionBadge | UNNotificationPresentationOptionAlert);
        }
    }
}

- (void)userNotificationCenter:(UNUserNotificationCenter *)center didReceiveNotificationResponse:(UNNotificationResponse *)response withCompletionHandler:(void (^)(void))completionHandler
{
    UPPushOriginalMessage *originalMessage = [[UPPushOriginalMessage alloc] init];
    originalMessage.originalMessage = response.notification.request.content.userInfo;
    if ([response.notification.request.trigger isKindOfClass:[UNPushNotificationTrigger class]]) {
        originalMessage.isCustom = NO;
        originalMessage.isLocal = NO;
    }
    else {
        originalMessage.isCustom = NO;
        originalMessage.isLocal = YES;
    }
    [self.delegate reciveMessage:originalMessage];
    completionHandler();
}

#pragma mark - FIRMessagingDelegate
- (void)messaging:(FIRMessaging *)messaging didReceiveRegistrationToken:(NSString *)fcmToken
{
}

#pragma mark - private methods
- (void)configFirebase
{
    [FIRApp configure];
    [FIRMessaging messaging].delegate = self;
}

- (void)configAPNsPush
{
    UNAuthorizationOptions authOptions = UNAuthorizationOptionAlert | UNAuthorizationOptionSound | UNAuthorizationOptionBadge;
    [[UNUserNotificationCenter currentNotificationCenter] requestAuthorizationWithOptions:authOptions
                                                                        completionHandler:^(BOOL granted, NSError *_Nullable error){
                                                                        }];
    [UNUserNotificationCenter currentNotificationCenter].delegate = self;
    [[UIApplication sharedApplication] registerForRemoteNotifications];
}
@end
