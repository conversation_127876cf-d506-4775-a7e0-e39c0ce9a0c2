//
//  UPPushResponseParser.m
//  AFNetworking
//
//  Created by  on 2019/3/1.
//

#import "UPPushResponseParser.h"
#import "UPPushResult.h"

@implementation UPPushResponseParser
- (NSObject *)parseResponseObject:(NSObject *)object
{
    if (!object || ![object isKindOfClass:NSDictionary.class]) {
        return [UPPushResult responseDataError];
    }
    UPPushResult *result = [[UPPushResult alloc] initWithResponseObject:object];
    return result;
}

@end
