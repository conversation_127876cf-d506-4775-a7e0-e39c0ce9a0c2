//
//  UPPushDataSourceRequest.m
//  UPPush
//
//  Created by osiris on 2020/10/31.
//

#import "UPPushDataSourceRequest.h"
#import <upnetwork/UPRequest.h>
#import "UPPushResult.h"
@implementation UPPushDataSourceRequest

+ (void)request:(UPRequest *)api result:(UPPushResultCallback)result
{
    UPRequestSuccess successBlock = ^(NSObject *_Nonnull responseObject) {
      UPPushResult *response = (UPPushResult *)responseObject;
      result ? result(response) : nil;
    };
    UPRequestFailure failureBlock = ^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
      UPPushResult *response = [[UPPushResult alloc] initWithResponseError:error];
      result ? result(response) : nil;
    };
    [api startRequestWithSuccess:successBlock failure:failureBlock];
}

@end
