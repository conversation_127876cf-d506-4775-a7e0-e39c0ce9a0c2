//
//  UPPushHomeDataSource.m
//  UPPushCore
//
//  Created by osiris on 2020/10/29.
//

#import "UPPushHomeDataSource.h"
#import "UPPushRegisterAPI.h"
#import "UPPushUnregisterAPI.h"
#import "UPPushReportStatusAPI.h"
#import "UPPushNotDisturbAPI.h"
#import "UPPushDataSourceRequest.h"
#import "UPPushResponseParser.h"
#import "UPPushAbilityTools.h"

@implementation UPPushHomeDataSource

#pragma mark - UPPushDataSourceProtocol

- (void)registerPush:(nonnull NSString *)pushToken clientType:(nonnull NSString *)clientType ability:(nonnull NSArray<NSString *> *)ability deviceAlias:(nonnull NSString *)deviceAlias version:(nonnull NSString *)version channel:(nonnull NSString *)channel result:(nonnull UPPushResultCallback)result
{
    [UPPushRegisterAPI getToken];
    UPPushRegisterAPI *registerAPI = [[UPPushRegisterAPI alloc] initWithPushToken:pushToken
                                                                       clientType:clientType
                                                                          ability:ability
                                                                      deviceAlias:deviceAlias
                                                                          version:version
                                                                          channel:channel];
    registerAPI.responseParser = [UPPushResponseParser new];
    [UPPushDataSourceRequest request:registerAPI
                              result:^(id<UPPushResultProtocol> _Nonnull apiResult) {

                                NSString *token = [NSUserDefaults.standardUserDefaults stringForKey:@"flag_sync_token"];
                                BOOL isDisturb = [[registerAPI currentToken] isEqualToString:token];
                                if (!isDisturb) {
                                    [self pushNotDisturb:pushToken];
                                }
                                result ? result(apiResult) : nil;
                              }];
}

- (void)unregisterPush:(nonnull UPPushResultCallback)result
{
    UPPushUnregisterAPI *unregisterAPI = [[UPPushUnregisterAPI alloc] init];
    unregisterAPI.responseParser = [UPPushResponseParser new];
    [UPPushDataSourceRequest request:unregisterAPI
                              result:^(id<UPPushResultProtocol> _Nonnull apiResult) {
                                result ? result(apiResult) : nil;
                                [UPPushUnregisterAPI clearToken];
                              }];
}

- (void)reportMessage:(nonnull NSString *)messageID status:(nonnull NSString *)status result:(nonnull UPPushResultCallback)result
{
    UPPushReportStatusAPI *reportAPI = [[UPPushReportStatusAPI alloc] initWithMessageID:messageID
                                                                                 status:status];
    reportAPI.responseParser = [UPPushResponseParser new];
    [UPPushDataSourceRequest request:reportAPI result:result];
}

- (void)configAPIEnvironment:(UPPushEnvironment)environment
{
    [UPPushRequestBase setUPPushAPIEnvironment:environment];
}

- (void)configAPILanguage:(nonnull NSString *)language
{
    // TODO
}

#pragma mark - UPPushDataSourceFacadeProtocol
- (void)registerPush:(NSString *)pushToken ability:(UPPushAbility)ability result:(UPPushResultCallback)result
{
    [self registerPush:pushToken
            clientType:@"2"
               ability:ability2String(ability, UPPushDataSourceVersion_3)
           deviceAlias:[[UIDevice currentDevice] name] ?: @""
               version:@"v3"
               channel:pushToken && pushToken.length > 0 ? @"0" : @"4"
                result:result];
}

- (void)reportMessage:(nonnull NSString *)messageID result:(nonnull UPPushResultCallback)result
{
    [self reportMessage:messageID status:/*暂时无用，只做已读操作*/ @"" result:result];
}

#pragma mark Privite Method

- (void)pushNotDisturb:(NSString *)pushId;
{
    UPPushNotDisturbAPI *notDisturbAPI = [[UPPushNotDisturbAPI alloc] initWithPushId:pushId];

    notDisturbAPI.responseParser = [UPPushResponseParser new];

    [UPPushDataSourceRequest request:notDisturbAPI
                              result:^(id<UPPushResultProtocol> _Nonnull result) {
                                if (result.isSuccess) {
                                    [NSUserDefaults.standardUserDefaults setValue:[notDisturbAPI currentToken] forKey:@"flag_sync_token"];
                                }
                              }];
}

@end
