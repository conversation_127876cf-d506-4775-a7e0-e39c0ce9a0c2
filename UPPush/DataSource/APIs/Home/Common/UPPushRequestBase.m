//
//  UPPushRequestBase.m
//  UPPushAPIs
//
//  Created by osiris on 2020/10/20.
//

#import "UPPushRequestBase.h"
#import <upnetwork/UPNetwork.h>
#import "UPPushAPIUtil.h"

NSString *const kUPPushAPIURL_Development = @"https://uws.haier.net";
NSString *const kUPPushAPIURL_Acceptance = @"https://uws-ys.haier.net";
NSString *const kUPPushAPIURL_Production = @"https://uws.haier.net";

static UPPushEnvironment UPPush_APIEnvironment = UPPushEnvironment_Production;
static NSString *UPPush_APIToken = @"";

@implementation UPPushRequestBase

- (NSString *)baseURL
{
    switch (UPPush_APIEnvironment) {
        case UPPushEnvironment_Development:
            return kUPPushAPIURL_Development;
        case UPPushEnvironment_Acceptance:
            return kUPPushAPIURL_Acceptance;
        case UPPushEnvironment_Production:
            return kUPPushAPIURL_Production;
    }

    return kUPPushAPIURL_Production;
}
- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSTimeInterval)timeoutInterval
{
    return 15.0;
}

- (NSUInteger)retryTimes
{
    return 0;
}

- (NSTimeInterval)retryDelay
{
    return 0.0;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *header = [[UPPushAPIUtil requestHeader:(NSDictionary *)self.requestBody url:self.path] mutableCopy];
    [header addEntriesFromDictionary:@{
        @"language" : @"zh-cn",
        @"timezone" : @"Asia/Shanghai",
    }];
    NSString *token = self.currentToken;
    if (token.length > 0) {
        header[@"accessToken"] = token;
    }
    return header;
}

- (NSString *)currentToken
{
    NSString *token = [UPNetworkSettings sharedSettings].accessToken;
    if (![token isKindOfClass:NSString.class] || token.length == 0) {
        token = UPPush_APIToken;
    }
    return token;
}

#pragma mark - public
+ (void)setUPPushAPIEnvironment:(UPPushEnvironment)environment
{
    UPPush_APIEnvironment = environment;
}

+ (void)getToken
{
    UPPush_APIToken = [UPNetworkSettings sharedSettings].accessToken ?: @"";
}

+ (void)clearToken
{
    UPPush_APIToken = @"";
}

@end
