//
//  UPPushRegisterAPI.m
//  UPPushAPIs
//
//  Created by osiris on 2020/10/21.
//

#import "UPPushRegisterAPI.h"

@interface UPPushRegisterAPI ()

@property (nonatomic, strong) NSString *pushToken;
@property (nonatomic, strong) NSString *deviceAlias;
@property (nonatomic, strong) NSString *version;
@property (nonatomic, strong) NSString *channel;
@property (nonatomic, strong) NSString *clientType;
@property (nonatomic, strong) NSArray<NSString *> *ability;

@end

@implementation UPPushRegisterAPI

- (instancetype)initWithPushToken:(NSString *)pushToken clientType:(NSString *)clientType ability:(NSArray<NSString *> *)ability deviceAlias:(nonnull NSString *)deviceAlias version:(nonnull NSString *)version channel:(nonnull NSString *)channel
{
    if (self = [super init]) {
        _pushToken = pushToken;
        _deviceAlias = deviceAlias;
        _version = version;
        _channel = channel;
        _clientType = clientType;
        _ability = ability;
    }

    return self;
}

- (NSString *)name
{
    return @"注册国内推送功能";
}
- (NSString *)path
{
    return @"/ums/v3/account/register";
}

- (NSObject *)requestBody
{
    return @{
        @"devAlias" : _deviceAlias ?: @"",
        @"msgVersion" : _version ?: @"",
        @"channel" : _channel ?: @"",
        @"pushId" : _pushToken ?: @"",
        @"clientType" : _clientType ?: @"",
        @"ability" : _ability ?: @[]
    };
}

@end
