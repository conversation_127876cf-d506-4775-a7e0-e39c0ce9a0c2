//
//  UPPushRegisterAPI.h
//  UPPushAPIs
//
//  Created by osir<PERSON> on 2020/10/21.
//

#import "UPPushRequestBase.h"

NS_ASSUME_NONNULL_BEGIN

/**
 国内推送注册接口
 */
@interface UPPushRegisterAPI : UPPushRequestBase

- (instancetype)initWithPushToken:(NSString *)pushToken
                       clientType:(NSString *)clientType
                          ability:(NSArray<NSString *> *)ability
                      deviceAlias:(NSString *)deviceAlias
                          version:(NSString *)version
                          channel:(NSString *)channel;
@end

NS_ASSUME_NONNULL_END
