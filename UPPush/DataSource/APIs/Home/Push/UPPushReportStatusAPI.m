//
//  UPPushReportStatusAPI.m
//  UPPushAPIs
//
//  Created by osiris on 2020/10/21.
//

#import "UPPushReportStatusAPI.h"

@interface UPPushReportStatusAPI ()

@property (nonatomic, strong) NSString *messageID;
@property (nonatomic, strong) NSString *status;

@end

@implementation UPPushReportStatusAPI

- (instancetype)initWithMessageID:(NSString *)messageID status:(NSString *)status
{
    if (self = [super init]) {
        _messageID = messageID;
        _status = status;
    }

    return self;
}

- (NSString *)name
{
    return @"国内环境上报消息状态";
}

- (NSString *)path
{
    return @"/ums/v3/msg/reportStatus";
}

- (NSObject *)requestBody
{
    return @{
        @"taskId" : _messageID ?: @""
    };
}
@end
