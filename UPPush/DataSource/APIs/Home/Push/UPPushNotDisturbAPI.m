//
//  UPPushNotDisturb.m
//  UPPush
//
//  Created by ByteBai on 2023/10/11.
//

#import "UPPushNotDisturbAPI.h"

@interface UPPushNotDisturbAPI ()

@property (nonatomic, strong) NSString *pushId;

@end

@implementation UPPushNotDisturbAPI

- (instancetype)initWithPushId:(NSString *)pushId
{
    if (self = [super init]) {
        _pushId = pushId;
    }
    return self;
}

- (NSString *)name
{
    return @"消息免打扰";
}

- (NSString *)path
{
    return @"/ums/v4/config/syncNotDisturb";
}

- (NSObject *)requestBody
{
    return @{
        @"pushId" : _pushId ?: @"",
    };
}

@end
