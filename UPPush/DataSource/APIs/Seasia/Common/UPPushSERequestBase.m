//
//  SERequestBase.m
//  UserDomainAPIs
//
//  Created by <PERSON> on 2019/7/2.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPPushSERequestBase.h"
#import "UPPushEncryption.h"
#import "UPPushAPIUtil.h"
#import "UPPushSettings.h"

NSString *const kUPPushSeasiaAPIURL_Development = @"https://uws-sea.haieriot.net";
NSString *const kUPPushSeasiaAPIURL_Acceptance = @"https://uws-sea.haieriot.net";
NSString *const kUPPushSeasiaAPIURL_Production = @"https://uws-sea.haieriot.net";

static UPPushEnvironment UPPush_Seasia_APIEnvironment = UPPushEnvironment_Production;
static NSString *UPPush_Seasia_APILanguage = @"";

@interface UPPushSERequestBase ()

@end

@implementation UPPushSERequestBase
#pragma mark - Overrides
- (NSString *)baseURL
{
    switch (UPPush_Seasia_APIEnvironment) {
        case UPPushEnvironment_Development:
            return kUPPushSeasiaAPIURL_Development;
        case UPPushEnvironment_Acceptance:
            return kUPPushSeasiaAPIURL_Acceptance;
        case UPPushEnvironment_Production:
            return kUPPushSeasiaAPIURL_Production;
    }

    return kUPPushSeasiaAPIURL_Production;
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSTimeInterval)timeoutInterval
{
    return 15.0;
}

- (NSUInteger)retryTimes
{
    return 0;
}

- (NSTimeInterval)retryDelay
{
    return 0.0;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *header = [[UPPushAPIUtil requestHeader:(NSDictionary *)self.requestBody url:self.path] mutableCopy];
    [header addEntriesFromDictionary:@{
        @"language" : [self language],
        @"timezone" : [self timeZone],
        @"accessToken" : [UPNetworkSettings sharedSettings].uhomeAccessToken ?: @"",
    }];
    return header;
}

#pragma mark - public methods
+ (void)setUPPushAPIEnvironment:(UPPushEnvironment)environment
{
    UPPush_Seasia_APIEnvironment = environment;
}

+ (void)setUPPushAPILanguage:(NSString *)language
{
    UPPush_Seasia_APILanguage = language;
}
#pragma mark - private methods
- (NSString *)language
{
    return UPPush_Seasia_APILanguage ?: @"";
}

- (NSString *)timeZone
{
    return [NSTimeZone defaultTimeZone].name ?: @"";
}
@end
