//
//  UPPushAPIUtil.m
//  UPPushAPIs
//
//  Created by osiris on 2020/11/2.
//

#import "UPPushAPIUtil.h"
#import <upnetwork/UPNetwork.h>
#import "UPPushEncryption.h"

@implementation UPPushAPIUtil

+ (NSDictionary *)requestHeader:(NSDictionary *)requestBody url:(nonnull NSString *)url
{
    NSString *tempTimestamp = self.timestamp;
    return @{
        @"proVersion" : @"v1.0",
        @"appVersion" : [UPNetworkSettings sharedSettings].appVersion,
        @"appId" : [UPNetworkSettings sharedSettings].appID,
        @"appKey" : [UPNetworkSettings sharedSettings].appKey,
        @"clientId" : [UPNetworkSettings sharedSettings].clientID,
        @"sign" : [UPPushAPIUtil signWithBody:requestBody url:url timestamp:tempTimestamp],
        @"sequenceId" : self.sequenceID,
        @"timestamp" : tempTimestamp
    };
}

#pragma mark - private
+ (NSString *)sequenceID
{
    NSDate *nowDate = [NSDate date];
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    dateFormatter.dateFormat = @"yyyyMMddHHmmssSSS";
    NSString *dateString = [dateFormatter stringFromDate:nowDate];
    NSString *sequenceidStr = [NSString stringWithFormat:@"%@000001", dateString];
    return sequenceidStr;
}

+ (NSString *)timestamp
{
    NSTimeInterval interval = [[NSDate date] timeIntervalSince1970];
    long long result = interval * 1000;
    return [NSString stringWithFormat:@"%.0lld", result];
}

+ (NSData *)requestBodyJson:(NSDictionary *)requestBody
{
    if (requestBody) {
        NSData *bodyJson = [NSJSONSerialization dataWithJSONObject:requestBody options:0 error:nil];
        return bodyJson;
    }
    else {
        return nil;
    }
}

+ (NSString *)signWithBody:(NSDictionary *)body url:(NSString *)url timestamp:(NSString *)timestamp
{
    NSString *bodyString = nil;
    NSString *signKeyString = nil;
    if (body == nil || url == nil || timestamp == nil) {
        return nil;
    }
    else {
        bodyString = [[NSString alloc] initWithData:[NSJSONSerialization dataWithJSONObject:body options:0 error:nil] encoding:NSUTF8StringEncoding];
        bodyString = [bodyString stringByReplacingOccurrencesOfString:@"\n" withString:@""];
        bodyString = [bodyString stringByReplacingOccurrencesOfString:@"\r" withString:@""];
        bodyString = [bodyString stringByReplacingOccurrencesOfString:@" " withString:@""];
        bodyString = [bodyString stringByReplacingOccurrencesOfString:@"\b" withString:@""];
        bodyString = [bodyString stringByReplacingOccurrencesOfString:@"\t" withString:@""];

        NSString *appId = [UPNetworkSettings sharedSettings].appID;
        NSString *appKey = [UPNetworkSettings sharedSettings].appKey;
        signKeyString = [NSString stringWithFormat:@"%@%@%@%@%@", url, bodyString, appId, appKey, timestamp];
        NSString *sha256Sign = [UPPushEncryption getSHA256:signKeyString];
        return sha256Sign;
    }
}
@end
