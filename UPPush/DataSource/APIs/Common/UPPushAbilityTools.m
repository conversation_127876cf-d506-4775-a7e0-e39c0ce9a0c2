//
//  UPPushAbilityTools.m
//  UPPushCore
//
//  Created by 吴子航 on 2022/11/24.
//

#import "UPPushAbilityTools.h"

NSArray<NSString *> *ability2String(UPPushAbility ability, UPPushDataSourceVersion version)
{
    NSMutableArray<NSString *> *abilityList = [NSMutableArray array];
    NSDictionary<NSNumber *, NSString *> *abilityMap = @{};
    switch (version) {
        case UPPushDataSourceVersion_3:
            abilityMap = @{
                @(UPPushAbility_Action) : @"action",
                @(UPPushAbility_Notification) : @"notification",
                @(UPPushAbility_Dialog) : @"dialog",
                @(UPPushAbility_Voice) : @"voice",
                @(UPPushAbility_Toast) : @"toast",
                @(UPPushAbility_DownloadFile) : @"action_downloadFile",
                @(UPPushAbility_DeliverData) : @"action_deliverData",
                @(UPPushAbility_OpenApp) : @"action_openApp",
                @(UPPushAbility_OpenPage) : @"action_openPage",
                @(UPPushAbility_OpenLink) : @"action_openLink",
                @(UPPushAbility_CallApi) : @"action_callApi",
                @(UPPushAbility_ControlDevice) : @"action_controlDevice",
                @(UPPushAbility_ActivateApp) : @"action_activateApp",
                @(UPPushAbility_MakeVideoCall) : @"action_makeVideoCall",
                @(UPPushAbility_ControlScene) : @"action_controlScene",
            };
            break;
        case UPPushDataSourceVersion_4:
            abilityMap = @{
                @(UPPushAbility_Action) : @"action",
                @(UPPushAbility_Notification) : @"notification",
                @(UPPushAbility_Dialog) : @"dialog",
                @(UPPushAbility_Voice) : @"voice",
                @(UPPushAbility_Toast) : @"toast",
                @(UPPushAbility_DownloadFile) : @"downloadFile",
                @(UPPushAbility_DeliverData) : @"deliverData",
                @(UPPushAbility_OpenApp) : @"openApp",
                @(UPPushAbility_OpenPage) : @"openPage",
                @(UPPushAbility_OpenLink) : @"openLink",
                @(UPPushAbility_CallApi) : @"callApi",
                @(UPPushAbility_ControlDevice) : @"controlDevice",
                @(UPPushAbility_ActivateApp) : @"activateApp",
                @(UPPushAbility_MakeVideoCall) : @"makeVideoCall",
                @(UPPushAbility_ControlScene) : @"controlScene",
            };
            break;
    }

    [abilityMap enumerateKeysAndObjectsUsingBlock:^(NSNumber *_Nonnull key, NSString *_Nonnull obj, BOOL *_Nonnull stop) {
      if (ability & key.unsignedIntegerValue) {
          [abilityList addObject:obj];
      }
    }];

    return abilityList.copy;
}
