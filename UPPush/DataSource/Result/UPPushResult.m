//
//  UPPushResult.m
//  UPPushCore
//
//  Created by osiris on 2020/10/31.
//

#import "UPPushResult.h"

NSString *const kUPPushResponse_Code = @"retCode";
NSString *const kUPPushResponse_Info = @"retInfo";
NSString *const kUPPushCode_Success = @"00000";
NSString *const kUPPushInfo_Success = @"操作成功";
NSString *const kUPPushNetErrorCode = @"99999";
NSString *const kUPPushNetErrotInfo = @"网络异常";
NSString *const kUPPushResponseErrorDataKey =
    @"com.alamofire.serialization.response.error.data";
NSString *const kUPPushResponseErrorKey = @"error";
NSString *const kUPPushError_description = @"error_description";
NSString *const kUPPushCode_Failure = @"-1";
NSString *const kUPPushResponseDataError = @"服务器返回数据异常";
NSString *const kUPPushResponse_Payload = @"payload";

@implementation UPPushResult
@synthesize isSuccess, code, info, userInfo, payload;

+ (UPPushResult *)responseDataError
{
    UPPushResult *result = [[UPPushResult alloc] init];
    result.code = kUPPushCode_Failure;
    result.info = kUPPushResponseDataError;
    result.isSuccess = NO;
    return result;
}

- (instancetype)initWithResponseError:(NSError *)error
{
    if (self = [super init]) {
        [self createResultWithResponseError:error];
    }
    return self;
}

- (instancetype)initWithResponseObject:(id)responseObject
{
    if (self = [super init]) {
        if ([responseObject[kUPPushResponse_Code]
                isEqualToString:kUPPushCode_Success]) {
            self.isSuccess = YES;
            self.code = kUPPushCode_Success;
            self.info = kUPPushInfo_Success;
            id payload = responseObject[kUPPushResponse_Payload];
            if ([payload isKindOfClass:[NSDictionary class]]) {
                self.payload = (NSDictionary *)payload;
            }
        }
        else {
            self.isSuccess = NO;
            self.code = responseObject[kUPPushResponse_Code];
            self.info = responseObject[kUPPushResponse_Info];
        }
    }
    return self;
}

#pragma mark - private
- (void)configWithErrorInfo:(NSDictionary *)info
{
    if (![info isKindOfClass:[NSDictionary class]]) {
        self.code = kUPPushNetErrorCode;
        self.info = kUPPushNetErrotInfo;
        return;
    }
    NSString *error = info[kUPPushResponseErrorKey];
    NSString *errorDescription = info[kUPPushError_description];
    NSString *retCode = error ? error : errorDescription;
    NSString *retInfo = errorDescription ? errorDescription : error;
    if (![retCode isKindOfClass:[NSString class]]) {
        retCode = kUPPushNetErrorCode;
    }
    if (![retInfo isKindOfClass:[NSString class]]) {
        retInfo = kUPPushNetErrotInfo;
    }
    self.code = retCode;
    self.info = retInfo;
}

- (void)createResultWithResponseError:(NSError *)error
{
    if ([error.domain isEqualToString:NSURLErrorDomain]) {
        self.code = kUPPushNetErrorCode;
        self.info = [NSString stringWithFormat:@"%@", error];
    }
    else {
        NSData *errorData = error.userInfo[kUPPushResponseErrorDataKey];
        if ([errorData isKindOfClass:[NSData class]]) {
            NSDictionary *errorInfo =
                [NSJSONSerialization JSONObjectWithData:errorData
                                                options:NSJSONReadingMutableContainers
                                                  error:NULL];
            [self configWithErrorInfo:errorInfo];
        }
        else {
            self.code = kUPPushNetErrorCode;
            self.info = [NSString stringWithFormat:@"%@", error];
        }
    }
    self.isSuccess = NO;
}
@end
