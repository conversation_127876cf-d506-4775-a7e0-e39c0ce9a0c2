//
//  UPPushSeasiaDataSource.m
//  UPPushCore
//
//  Created by osiris on 2020/10/29.
//

#import "UPPushSeasiaDataSource.h"
#import "UPPushSERegisterAPI.h"
#import "UPPushSEUnregisterAPI.h"
#import "UPPushSEReportStatusAPI.h"
#import "UPPushDataSourceRequest.h"
#import "UPPushResponseParser.h"
#import "UPPushAbilityTools.h"
#import <UIKit/UIDevice.h>

@implementation UPPushSeasiaDataSource

#pragma mark - UPPushDataSourceProtocol
- (void)registerPush:(nonnull NSString *)pushToken clientType:(nonnull NSString *)clientType ability:(nonnull NSArray<NSString *> *)ability deviceAlias:(nonnull NSString *)deviceAlias version:(nonnull NSString *)version channel:(nonnull NSString *)channel result:(nonnull UPPushResultCallback)result
{
    UPPushSERegisterAPI *registerAPI = [[UPPushSERegisterAPI alloc] initWithPushToken:pushToken
                                                                           clientType:clientType
                                                                              ability:ability
                                                                          deviceAlias:deviceAlias
                                                                              version:version
                                                                              channel:channel];
    registerAPI.responseParser = [UPPushResponseParser new];
    [UPPushDataSourceRequest request:registerAPI result:result];
}

- (void)unregisterPush:(nonnull UPPushResultCallback)result
{
    UPPushSEUnregisterAPI *unregisterAPI = [[UPPushSEUnregisterAPI alloc] init];
    unregisterAPI.responseParser = [UPPushResponseParser new];
    [UPPushDataSourceRequest request:unregisterAPI result:result];
}

- (void)reportMessage:(nonnull NSString *)messageID status:(nonnull NSString *)status result:(nonnull UPPushResultCallback)result
{
    UPPushSEReportStatusAPI *reportAPI = [[UPPushSEReportStatusAPI alloc] initWithMessageID:messageID
                                                                                     status:status];
    reportAPI.responseParser = [UPPushResponseParser new];
    [UPPushDataSourceRequest request:reportAPI result:result];
}

- (void)configAPIEnvironment:(UPPushEnvironment)environment
{
    [UPPushSERequestBase setUPPushAPIEnvironment:environment];
}

- (void)configAPILanguage:(NSString *)language
{
    [UPPushSERequestBase setUPPushAPILanguage:language];
}

#pragma mark - UPPushDataSourceFacadeProtocol
- (void)registerPush:(nonnull NSString *)pushToken ability:(UPPushAbility)ability result:(nonnull UPPushResultCallback)result
{
    [self registerPush:pushToken
            clientType:@"2"
               ability:ability2String(ability, UPPushDataSourceVersion_3)
           deviceAlias:[[UIDevice currentDevice] name] ?: @""
               version:@"v3"
               channel:pushToken && pushToken.length > 0 ? @"2" : @"4"
                result:result];
}

- (void)reportMessage:(nonnull NSString *)messageID result:(nonnull UPPushResultCallback)result
{
    [self reportMessage:messageID status:/*暂时无用，只做已读操作*/ @"" result:result];
}


@end
