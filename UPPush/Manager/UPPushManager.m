//
//  UPPushManager.m
//  UPPush
//
//  Created by osir<PERSON> on 2020/10/15.
//

#import "UPPushManager.h"
#import "UPPushInitializer.h"
#import "UPPushHomeDataSource.h"
#import "UPPushSeasiaDataSource.h"
#import "UPPushBroadcastSender.h"
#import "UPPushSettings.h"

@interface UPPushManager ()

@property (nonatomic, strong) UPPush *push;

@end

@implementation UPPushManager
+ (instancetype)instance
{
    static dispatch_once_t onceToken;
    static UPPushManager *manager;
    dispatch_once(&onceToken, ^{
      if (!manager) {
          manager = [[UPPushManager alloc] init];
      }
    });

    return manager;
}

- (void)initializeUPPush:(id<UPPushProviderProtocol>)provider channel:(nonnull id<UPPushChannelProtocol>)channel
{
    UPPushInitializer *initializer = [[UPPushInitializer alloc] init];
    initializer.provider = provider;
    initializer.broadcastSender = [UPPushBroadcastSender new];
    if ([provider respondsToSelector:@selector(provideChannelType)]) {
        switch ([provider provideChannelType]) {
            case UPPushChannel_Home:
                initializer.dataSource = [UPPushHomeDataSource new];
                break;
            case UPPushChannel_Seasia:
                initializer.dataSource = [UPPushSeasiaDataSource new];
                break;
            default:
                break;
        }
    }
    else {
        initializer.dataSource = [UPPushHomeDataSource new];
    }
    if ([provider respondsToSelector:@selector(environment)]) {
        [initializer.dataSource configAPIEnvironment:[provider environment]];
    }
    initializer.channel = channel;
    initializer.settings = UPPushSettings.instance;
    _push = [[UPPush alloc] initWithInitializer:initializer];
}
@end
