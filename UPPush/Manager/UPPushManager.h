//
//  UPPushManager.h
//  UPPush
//
//  Created by osiris on 2020/10/15.
//

#import "UPPush.h"
#import "UPPushProviderProtocol.h"
#import "UPPushChannelProtocol.h"

NS_ASSUME_NONNULL_BEGIN
/**
 推送组件实例持有者（单例子）
 */
@interface UPPushManager : NSObject
/**
 推送组件实例
 */
@property (nonatomic, strong, readonly) UPPush *push;

+ (instancetype)instance;

/**
 推送组件初始化使用
 @brief 需在AppDelegate didLaunch中调用
 */
- (void)initializeUPPush:(id<UPPushProviderProtocol>)provider channel:(id<UPPushChannelProtocol>)channel;
@end

NS_ASSUME_NONNULL_END
