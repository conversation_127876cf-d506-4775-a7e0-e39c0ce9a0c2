//
//  UPPushSettings.m
//  UPPushCore
//
//  Created by 吴子航 on 2023/2/6.
//

#import "UPPushSettings.h"
#import "UPPushStringTools.h"

@implementation UPPushSettings
@synthesize language = _language, languageChanged = _languageChanged;

+ (instancetype)instance
{
    static dispatch_once_t onceToken;
    static UPPushSettings *settings;
    dispatch_once(&onceToken, ^{
      settings = UPPushSettings.new;
    });

    return settings;
}

- (instancetype)init
{
    if (self = [super init]) {
        _language = @"";
    }

    return self;
}

- (void)setLanguage:(NSString *)language
{
    NSString *copyLanguage = language.copy;
    if (UPPush_isEmptyString(copyLanguage)) {
        copyLanguage = @"";
    }
    if ([copyLanguage isEqualToString:_language]) {
        return;
    }
    _language = copyLanguage;
    _languageChanged ? _languageChanged() : nil;
}

@end
