//
//  FakeUPPushChannel.m
//  UPPushTests
//
//  Created by osiris on 2020/10/27.
//

#import "FakeUPPushChannel.h"
#import "UPPushOriginalMessage.h"

@implementation FakeUPPushChannel
@synthesize delegate, dataSource;

+ (instancetype)instance
{
    static FakeUPPushChannel *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[FakeUPPushChannel alloc] init];
    });

    return instance;
}

#pragma mark - UPPushChannelProtocol
- (NSString *)platformPushToken
{
    return [[NSString alloc] initWithData:[self.dataSource pushTokenOfAPNs] encoding:NSUTF8StringEncoding];
}

- (void)launch
{
    NSLog(@"push token:%@", [self platformPushToken]);
    NSLog(@"launchingOption:%@", [self.dataSource launchingOption]);
}

- (void)didRecieveLocalNotification:(nonnull NSDictionary *)notification
{
    UPPushOriginalMessage *originalMessage = [[UPPushOriginalMessage alloc] init];
    originalMessage.originalMessage = notification;
    originalMessage.isCustom = NO;
    originalMessage.isLocal = YES;
    [self.delegate recieveMessage:originalMessage];
}


- (void)didRecieveRemoteNotification:(nonnull NSDictionary *)notification
{
    UPPushOriginalMessage *originalMessage = [[UPPushOriginalMessage alloc] init];
    originalMessage.originalMessage = notification;
    originalMessage.isCustom = NO;
    originalMessage.isLocal = NO;
    [self.delegate recieveMessage:originalMessage];
}


- (void)configAPNsToken
{
}

- (void)clearFakeDataInfo
{
    [super clearFakeDataInfo];
}
@end
