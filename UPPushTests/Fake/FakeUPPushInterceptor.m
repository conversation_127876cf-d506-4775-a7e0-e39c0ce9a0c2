//
//  FakeUPPushInterceptor.m
//  UPPushTests
//
//  Created by osiris on 2020/10/27.
//

#import "FakeUPPushInterceptor.h"
@interface FakeUPPushInterceptor ()

@property (nonatomic, copy) BOOL (^check)(NSString *messageName);

@end

@implementation FakeUPPushInterceptor
#pragma mark - UPPushInterceptorProtocol
- (BOOL)check:(nonnull id<UPPushMessageProtocol>)pushMessage
{
    return _check ? _check(pushMessage.messageName) : NO;
}

- (void)intercept:(nonnull id<UPPushMessageProtocol>)pushMessage
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ pushMessage ]];
}

- (BOOL)isFire
{
    return YES;
}

- (UPPushAbility)supportAbility
{
    return UPPushAbility_None;
}

#pragma mark - mock
- (void)mockInterceptorCheck:(BOOL (^)(NSString *messageName))check
{
    _check = check;
}

@end
