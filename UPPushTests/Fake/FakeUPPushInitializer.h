//
//  FakeUPPushInitializer.h
//  UPPushTests
//
//  Created by osiris on 2020/10/26.
//

#import "FakeUPPushBase.h"
#import "UPPushInitializerProtocol.h"

NS_ASSUME_NONNULL_BEGIN

@interface FakeUPPushInitializer : FakeUPPushBase <UPPushInitializerProtocol>

@property (nonatomic, strong) id<UPPushProviderProtocol> provider;
@property (nonatomic, strong) id<UPPushChannelProtocol> channel;
@property (nonatomic, strong) id<UPPushDataSourceFacadeProtocol> dataSource;
@property (nonatomic, strong) id<UPPushBroadcastSenderProtocol> broadcastSender;
@property (nonatomic, strong) id<UPPushSettingsProtocol> settings;

+ (instancetype)instance;

@end

NS_ASSUME_NONNULL_END
