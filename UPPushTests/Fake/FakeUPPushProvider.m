//
//  FakeUPPushProviderProtocol.m
//  UPPushTests
//
//  Created by osiris on 2020/10/26.
//

#import "FakeUPPushProvider.h"

@interface FakeUPPushProvider ()

@property (nonatomic, strong) NSData *pushTokenData;

@end
@implementation FakeUPPushProvider

+ (instancetype)instance
{
    static FakeUPPushProvider *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[FakeUPPushProvider alloc] init];
    });

    return instance;
}

- (void)clearFakeDataInfo
{
    [super clearFakeDataInfo];
}

- (NSData *)providePushToken
{
    [self recordInvokedActionOfSelector:_cmd];
    return _pushTokenData;
}

#pragma mark - UPPushProviderProtocol
- (UPPushChannelType)provideChannelType
{
    return UPPushChannel_Home;
}

- (NSDictionary *)launchingOption
{
    return @{};
}

#pragma mark - mock
- (void)mockPushToken:(NSData *)pushToken
{
    _pushTokenData = pushToken;
}
@end
