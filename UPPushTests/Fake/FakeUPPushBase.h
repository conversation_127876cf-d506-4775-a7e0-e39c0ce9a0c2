//
//  FakeUPPushBase.h
//  UPPushTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XCTest/XCTest.h>
NS_ASSUME_NONNULL_BEGIN

@interface FakeUPPushBase : NSObject
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSNumber *> *selectorInvokedInfo;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSArray *> *selectorParametersInfo;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSMutableArray *> *selectorMultipleInvocationParametersInfo;

- (void)recordInvokedActionOfSelector:(SEL)selector;
- (NSUInteger)getInvocationTimesOfSelector:(SEL)selector;
- (BOOL)isDelegateSelectorInvoked:(SEL)selector;
- (void)deleteRecordInvokedActionOfSelector:(SEL)selector;
- (void)clearFakeDataInfo;
- (void)recordInvokeActionOfSelector:(SEL)selector parameters:(NSArray *)parameters;
- (NSArray *)getParametersOfSelector:(SEL)selector;
- (void)recordMultipleInvocationParameters:(NSMutableArray *)parameters ofSelector:(SEL)selector;
- (NSMutableArray *)getMultipleInvocationParametersRecrodsOfSelector:(SEL)selector;
@property (nonatomic, strong) XCTestExpectation *expectation;
@end

NS_ASSUME_NONNULL_END
