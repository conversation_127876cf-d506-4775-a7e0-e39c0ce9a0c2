//
//  FakeUPPushBroadcastSender.m
//  UPPushTests
//
//  Created by osiris on 2020/10/28.
//

#import "FakeUPPushBroadcastSender.h"

@implementation FakeUPPushBroadcastSender

+ (instancetype)instance
{
    static FakeUPPushBroadcastSender *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[FakeUPPushBroadcastSender alloc] init];
    });

    return instance;
}

- (void)sendBroadcast:(nonnull id<UPPushMessageProtocol>)message
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ message ]];
}

@end
