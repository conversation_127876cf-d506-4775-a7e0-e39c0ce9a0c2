//
//  FakeUPPushDataSource.m
//  UPPushTests
//
//  Created by osiris on 2020/10/27.
//

#import "FakeUPPushDataSource.h"
#import "FakeUPPushResult.h"
#import "UPPushAbilityTools.h"

@interface FakeUPPushDataSource ()

@property (nonatomic, assign) BOOL registerPushResult;
@property (nonatomic, assign) BOOL unregisterPushResult;
@property (nonatomic, assign) BOOL reportStatusResult;

@end

@implementation FakeUPPushDataSource

+ (instancetype)instance
{
    static FakeUPPushDataSource *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[FakeUPPushDataSource alloc] init];
    });

    return instance;
}

- (void)clearFakeDataInfo
{
    [super clearFakeDataInfo];
    _registerPushResult = YES;
    _unregisterPushResult = YES;
    _reportStatusResult = YES;
}

#pragma mark - mock
- (void)mockReportStatusResult:(BOOL)result
{
    _reportStatusResult = result;
}

- (void)mockRegisterPushResult:(BOOL)result
{
    _registerPushResult = result;
}

- (void)mockUnregisterPushResult:(BOOL)result
{
    _unregisterPushResult = result;
}

#pragma mark - UPPushDataSourceProtocol
- (void)registerPush:(nonnull NSString *)pushToken clientType:(nonnull NSString *)clientType ability:(nonnull NSArray<NSString *> *)ability deviceAlias:(nonnull NSString *)deviceAlias version:(nonnull NSString *)version channel:(nonnull NSString *)channel result:(nonnull UPPushResultCallback)result
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ pushToken, channel, deviceAlias, version, ability, clientType ]];
    FakeUPPushResult *pushResult = [[FakeUPPushResult alloc] init];
    pushResult.isSuccess = _registerPushResult;
    result ? result(pushResult) : nil;
}

- (void)unregisterPush:(UPPushResultCallback)result
{
    [self recordInvokedActionOfSelector:_cmd];
    FakeUPPushResult *pushResult = [[FakeUPPushResult alloc] init];
    pushResult.isSuccess = _unregisterPushResult;
    result ? result(pushResult) : nil;
}

- (void)reportMessage:(nonnull NSString *)messageID status:(nonnull NSString *)status result:(nonnull UPPushResultCallback)result
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ messageID, status ]];
    FakeUPPushResult *pushResult = [[FakeUPPushResult alloc] init];
    pushResult.isSuccess = _reportStatusResult;
    result ? result(pushResult) : nil;
}

- (void)configAPILanguage:(nonnull NSString *)language
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ language ]];
}

#pragma mark - UPPushDataSourceFacadeProtocol
- (void)registerPush:(nonnull NSString *)pushToken ability:(UPPushAbility)ability result:(nonnull UPPushResultCallback)result
{
    NSString *token = pushToken;
    NSString *channel = @"0";
    if (!pushToken || pushToken.length == 0) {
        token = @"空对象";
        channel = @"4";
    }
    [self registerPush:token clientType:@"2" ability:ability2String(ability, UPPushDataSourceVersion_3) deviceAlias:@"fakeModel" version:@"v3" channel:channel result:result];
}

- (void)reportMessage:(NSString *)messageID result:(UPPushResultCallback)result
{
    [self reportMessage:messageID status:@"" result:result];
}

@end
