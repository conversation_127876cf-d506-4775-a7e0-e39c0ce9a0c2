//
//  FakeUPPushAllInterceptor.h
//  UPPushTests
//
//  Created by osiris on 2020/11/16.
//

#import "FakeUPPushBase.h"
#import "UPPushInterceptorProtocol.h"

NS_ASSUME_NONNULL_BEGIN

@interface FakeUPPushAllInterceptor : FakeUPPushBase <UPPushBuildInInterceptorProtocol>

@property (nonatomic, strong, readonly) id<UPPushMessageProtocol> message;

- (void)mockAbility:(UPPushAbility)mockAbility;
@end

NS_ASSUME_NONNULL_END
