//
//  FakeUPPushInitializerProtocol.m
//  UPPushTests
//
//  Created by osiris on 2020/10/26.
//

#import "FakeUPPushInitializer.h"
#import "FakeUPPushProvider.h"

@implementation FakeUPPushInitializer

+ (instancetype)instance
{
    static FakeUPPushInitializer *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[FakeUPPushInitializer alloc] init];
    });

    return instance;
}

- (void)clearFakeDataInfo
{
    [super clearFakeDataInfo];
}

@end
