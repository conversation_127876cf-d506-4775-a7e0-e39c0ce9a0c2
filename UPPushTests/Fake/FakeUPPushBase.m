//
//  FakeDelegateBase.m
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FakeUPPushBase.h"

@implementation FakeUPPushBase
- (instancetype)init
{
    if (self = [super init]) {
        _selectorInvokedInfo = [NSMutableDictionary dictionary];
        _selectorParametersInfo = [NSMutableDictionary dictionary];
        _selectorMultipleInvocationParametersInfo = [NSMutableDictionary dictionary];
        _expectation = nil;
    }
    return self;
}

- (void)recordInvokedActionOfSelector:(SEL)selector
{
    NSUInteger iCount = [self getInvocationTimesOfSelector:selector];
    NSString *selectorName = NSStringFromSelector(selector);
    iCount++;
    [self.selectorInvokedInfo setObject:@(iCount) forKey:selectorName];
}

- (NSUInteger)getInvocationTimesOfSelector:(SEL)selector
{
    NSString *selectorName = NSStringFromSelector(selector);
    NSNumber *num = self.selectorInvokedInfo[selectorName];
    return [num isKindOfClass:[NSNumber class]] ? num.integerValue : 0;
}

- (void)deleteRecordInvokedActionOfSelector:(SEL)selector
{
    if (selector == nil) {
        return;
    }
    NSString *selectorName = NSStringFromSelector(selector);
    [self.selectorInvokedInfo removeObjectForKey:selectorName];
}

- (BOOL)isDelegateSelectorInvoked:(SEL)selector
{
    if (selector == nil) {
        return NO;
    }
    NSString *selectorName = NSStringFromSelector(selector);
    NSNumber *num = self.selectorInvokedInfo[selectorName];
    return [num isKindOfClass:[NSNumber class]] && num.boolValue;
}

- (void)clearFakeDataInfo
{
    [self.selectorInvokedInfo removeAllObjects];
    [self.selectorParametersInfo removeAllObjects];
    [self.selectorMultipleInvocationParametersInfo removeAllObjects];
    _expectation = nil;
}

- (void)recordInvokeActionOfSelector:(SEL)selector parameters:(NSArray *)parameters
{
    if (!selector) {
        return;
    }
    [self recordInvokedActionOfSelector:selector];
    NSString *selectorName = NSStringFromSelector(selector);
    [self.selectorParametersInfo setValue:parameters forKey:selectorName];
}

- (NSArray *)getParametersOfSelector:(SEL)selector
{
    if (!selector) {
        return nil;
    }
    NSString *selectorName = NSStringFromSelector(selector);
    return self.selectorParametersInfo[selectorName];
}

- (void)recordMultipleInvocationParameters:(NSMutableArray *)parameters ofSelector:(SEL)selector
{
    if (!selector) {
        return;
    }
    NSString *selectorName = NSStringFromSelector(selector);
    [self.selectorMultipleInvocationParametersInfo setValue:parameters forKey:selectorName];
}

- (NSMutableArray *)getMultipleInvocationParametersRecrodsOfSelector:(SEL)selector
{
    if (!selector) {
        return [NSMutableArray array];
    }
    NSString *selectorName = NSStringFromSelector(selector);
    NSMutableArray *arr = self.selectorMultipleInvocationParametersInfo[selectorName];
    if (arr == nil) {
        arr = [NSMutableArray array];
    }
    return arr;
}

@end
