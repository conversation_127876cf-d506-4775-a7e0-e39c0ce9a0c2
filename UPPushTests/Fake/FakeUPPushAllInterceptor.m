//
//  FakeUPPushAllInterceptor.m
//  UPPushTests
//
//  Created by osiris on 2020/11/16.
//

#import "FakeUPPushAllInterceptor.h"

@interface FakeUPPushAllInterceptor ()

@property (nonatomic, strong) id<UPPushMessageProtocol> message;
@property (nonatomic, assign) UPPushAbility ability;

@end

@implementation FakeUPPushAllInterceptor
@synthesize delegate;
- (void)mockAbility:(UPPushAbility)mockAbility
{
    _ability = mockAbility;
}
#pragma mark - UPPushInterceptorProtocol
- (BOOL)check:(nonnull id<UPPushMessageProtocol>)pushMessage
{
    return YES;
}

- (void)intercept:(nonnull id<UPPushMessageProtocol>)pushMessage
{
    [self recordInvokeActionOfSelector:_cmd parameters:@[ pushMessage ]];
    _message = [pushMessage copyWithZone:nil];
}

- (BOOL)isFire
{
    return YES;
}

- (UPPushAbility)supportAbility
{
    return _ability;
}

@end
