//
//  FakeUPPushSettings.m
//  UPPushTests
//
//  Created by 吴子航 on 2023/2/6.
//

#import "FakeUPPushSettings.h"

@implementation FakeUPPushSettings

@synthesize language;

+ (instancetype)instance
{
    static FakeUPPushSettings *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[FakeUPPushSettings alloc] init];
    });

    return instance;
}

@end
