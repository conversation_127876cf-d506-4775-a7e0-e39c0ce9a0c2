//
//  CucumberRunner.c
//  upuserdomainTests
//
//  Created by 振兴郑 on 2019/4/15.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "Cucumberish.h"
#import <XCTest/XCTest.h>
#import "InitializationSteps.h"
#import "PushSteps.h"
#import "MessageSteps.h"

@interface CucumberRunner : NSObject

@end

@implementation CucumberRunner

__attribute__((constructor)) void CucumberInit()
{
    [[Cucumberish instance] setPrettyNamesAllowed:NO];
    [Cucumberish instance].fixMissingLastScenario = YES;
    [[[InitializationSteps alloc] init] defineStepsAndHocks];
    [[[PushSteps alloc] init] defineStepsAndHocks];
    [[[MessageSteps alloc] init] defineStepsAndHocks];
    NSBundle *bundle = [NSBundle bundleForClass:[CucumberRunner class]];
    Cucumberish *cucumber = [[Cucumberish instance] parserFeaturesInDirectory:@"features" fromBundle:bundle includeTags:nil excludeTags:@[ @"ios_ignore", @"ignore" ]];
    [cucumber beginExecution];
}
@end
