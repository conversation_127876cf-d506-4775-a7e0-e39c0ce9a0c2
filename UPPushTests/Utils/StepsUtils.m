//
//  StepsUtils.m
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "StepsUtils.h"

@implementation StepsUtils
NSArray<NSArray<NSString *> *> *getTableDataListFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *expect = userInfo[@"DataTable"];
    NSMutableArray *result = expect.mutableCopy;
    [result removeObjectAtIndex:0];
    return result;
}

NSArray<NSString *> *getTableDataInterceptor(NSDictionary *userInfo)
{
    NSMutableArray *array = [NSMutableArray array];
    [getTableDataListFromExpectUserInfo(userInfo) enumerateObjectsUsingBlock:^(NSArray<NSString *> *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [array addObject:obj.firstObject];
    }];

    return [array copy];
}

BOOL checkInterceptorArrayIsEqual(NSArray<NSString *> *array0, NSArray<NSString *> *array1)
{
    NSArray *sortArray0 = [array0 sortedArrayUsingSelector:@selector(compare:)];
    NSArray *sortArray1 = [array1 sortedArrayUsingSelector:@selector(compare:)];
    NSMutableString *string0 = [[NSMutableString alloc] init];
    NSMutableString *string1 = [[NSMutableString alloc] init];
    [sortArray0 enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [string0 appendString:obj];
    }];
    [sortArray1 enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [string1 appendString:obj];
    }];

    return [string0 isEqualToString:string1];
}

NSArray<UPPushMessage *> *createMessageList(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *array = getTableDataListFromExpectUserInfo(userInfo);
    UPPushMessage *message = nil;
    NSMutableArray<UPPushMessage *> *messageArray = [[NSMutableArray alloc] init];
    for (NSArray<NSString *> *param in array) {
        message = [[UPPushMessage alloc] init];
        [message setValue:param[0] forKey:@"messageID"];
        [message setValue:param[1] forKey:@"messageName"];
        [messageArray addObject:message];
    }

    return messageArray;
}

NSArray<UPPushOriginalMessage *> *createOriginalMessageList(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *array = getTableDataListFromExpectUserInfo(userInfo);
    NSDictionary *messageDict = nil;
    NSMutableArray<UPPushOriginalMessage *> *messageArray = [[NSMutableArray alloc] init];
    UPPushOriginalMessage *originalMessage = nil;
    for (NSArray<NSString *> *param in array) {
        BOOL isNull = [param[1] isEqualToString:@"空对象"] || [param[1] isEqualToString:@"空字符串"];
        NSData *base64 = [NSJSONSerialization dataWithJSONObject:@{ @"msgId" : param[0],
                                                                    @"msgName" : !isNull ? param[1] : @"" }
                                                         options:0
                                                           error:nil];
        NSString *base64String = [base64 base64EncodedStringWithOptions:0];
        messageDict = @{ @"content" : base64String };
        originalMessage = [[UPPushOriginalMessage alloc] init];
        originalMessage.originalMessage = messageDict;
        [messageArray addObject:originalMessage];
    }

    return messageArray;
}

NSArray<NSString *> *pushDataSource(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *array = getTableDataListFromExpectUserInfo(userInfo);
    return array.firstObject;
}

NSArray<NSString *> *interceptorCondition(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *array = getTableDataListFromExpectUserInfo(userInfo);

    return array.firstObject;
}

NSDictionary<NSString *, NSDictionary<NSString *, NSString *> *> *messageDataList(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *expect = [userInfo[@"DataTable"] copy];
    NSArray<NSString *> *keyList = expect.firstObject;

    NSArray<NSArray<NSString *> *> *array = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableDictionary<NSString *, NSDictionary<NSString *, NSString *> *> *messageDict = [NSMutableDictionary dictionary];
    NSMutableDictionary<NSString *, NSString *> *messageItem = nil;
    for (NSArray<NSString *> *messageParams in array) {
        messageItem = [NSMutableDictionary dictionary];
        [keyList enumerateObjectsUsingBlock:^(NSString *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          if (idx == 0) {
              return;
          }
          [messageItem setObject:messageParams[idx] forKey:obj];
        }];
        [messageDict setObject:messageItem forKey:messageParams.firstObject];
    }

    return [messageDict copy];
}

UPPushAbility transform2Ability(NSArray<NSString *> *abilities)
{
    NSDictionary<NSString *, NSNumber *> *abilitySource = @{
        @"Action" : @(UPPushAbility_Action),
        @"Notification" : @(UPPushAbility_Notification),
        @"Dialog" : @(UPPushAbility_Dialog),
        @"Voice" : @(UPPushAbility_Voice),
        @"Toast" : @(UPPushAbility_Toast),
        @"DownloadFile" : @(UPPushAbility_DownloadFile),
        @"DeliverData" : @(UPPushAbility_DeliverData),
        @"OpenApp" : @(UPPushAbility_OpenApp),
        @"OpenPage" : @(UPPushAbility_OpenPage),
        @"OpenLink" : @(UPPushAbility_OpenLink),
        @"CallApi" : @(UPPushAbility_CallApi),
        @"ControlDevice" : @(UPPushAbility_ControlDevice),
        @"ActivateApp" : @(UPPushAbility_ActivateApp),
        @"MakeVideoCall" : @(UPPushAbility_MakeVideoCall),
        @"ControlScene" : @(UPPushAbility_ControlScene)
    };
    __block UPPushAbility ability = UPPushAbility_None;
    [abilities enumerateObjectsUsingBlock:^(NSString *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      ability |= abilitySource[obj].unsignedIntegerValue;
    }];

    return ability;
}

NSString *transformAbilities(NSArray *abilities)
{
    NSArray *sortAbility = [abilities sortedArrayUsingComparator:^NSComparisonResult(id _Nonnull obj1, id _Nonnull obj2) {
      return [obj1 compare:obj2];
    }];
    NSMutableString *abilityStr = [NSMutableString stringWithString:@"["];
    for (NSInteger i = 0; i < sortAbility.count; i++) {
        [abilityStr appendFormat:@"%@%@", sortAbility[i], i == sortAbility.count - 1 ? @"" : @", "];
    }
    [abilityStr appendString:@"]"];

    return abilityStr.copy;
}

@end
