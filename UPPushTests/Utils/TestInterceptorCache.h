//
//  TestInterceptorCache.h
//  UPPushTests
//
//  Created by osiris on 2020/11/17.
//

#import <Foundation/Foundation.h>
#import "UPPushInterceptorProtocol.h"

NS_ASSUME_NONNULL_BEGIN

@interface TestInterceptorCache : NSObject

@property (nonatomic, strong, readonly) NSMutableDictionary<NSString *, id<UPPushInterceptorProtocol>> *interceptorDict;

+ (instancetype)instance;

- (void)reset;
@end

NS_ASSUME_NONNULL_END
