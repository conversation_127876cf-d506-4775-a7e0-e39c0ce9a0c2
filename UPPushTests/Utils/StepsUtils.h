//
//  StepsUtils.h
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPPushMessage.h"
#import "UPPushOriginalMessage.h"
#import "UPPushGlobalDeclaration.h"

NS_ASSUME_NONNULL_BEGIN

@interface StepsUtils : NSObject
NSArray<NSString *> *getTableDataInterceptor(NSDictionary *userInfo);
BOOL checkInterceptorArrayIsEqual(NSArray<NSString *> *array0, NSArray<NSString *> *array1);
NSArray<UPPushMessage *> *createMessageList(NSDictionary *userInfo);
NSArray<UPPushOriginalMessage *> *createOriginalMessageList(NSDictionary *userInfo);
NSArray<NSString *> *pushDataSource(NSDictionary *userInfo);
NSArray<NSString *> *interceptorCondition(NSDictionary *userInfo);
NSDictionary<NSString *, NSDictionary<NSString *, NSString *> *> *messageDataList(NSDictionary *userInfo);
UPPushAbility transform2Ability(NSArray<NSString *> *abilities);
NSString *transformAbilities(NSArray *abilities);
@end

NS_ASSUME_NONNULL_END
