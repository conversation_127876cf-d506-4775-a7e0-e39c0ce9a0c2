//
//  TestInterceptorCache;.m
//  UPPushTests
//
//  Created by osiris on 2020/11/17.
//

#import "TestInterceptorCache.h"

@interface TestInterceptorCache ()

@property (nonatomic, strong) NSMutableDictionary<NSString *, id<UPPushInterceptorProtocol>> *interceptorDict;

@end

@implementation TestInterceptorCache

+ (instancetype)instance
{
    static dispatch_once_t onceToken;
    static TestInterceptorCache *interceptorCache = nil;
    dispatch_once(&onceToken, ^{
      interceptorCache = [[TestInterceptorCache alloc] init];
    });

    return interceptorCache;
}

- (instancetype)init
{
    if (self = [super init]) {
        _interceptorDict = [NSMutableDictionary dictionary];
    }

    return self;
}
- (void)reset
{
    [_interceptorDict removeAllObjects];
}
@end
