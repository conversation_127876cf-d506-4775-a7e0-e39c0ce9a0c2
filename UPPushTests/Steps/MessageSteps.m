//
//  MessageSteps.m
//  UPPushTests
//
//  Created by osiris on 2020/11/16.
//

#import "MessageSteps.h"
#import <Cucumberish/Cucumberish.h>
#import <OCMock/OCMock.h>
#import "StepsUtils.h"
#import "FakeUPPushManager.h"
#import "FakeUPPushChannel.h"
#import "FakeUPPushProvider.h"
#import "FakeUPPushDataSource.h"
#import "FakeUPPushAllInterceptor.h"
#import "FakeUPPushBroadcastSender.h"
#import "UPPushBadgeManager.h"
#import "TestInterceptorCache.h"

@interface MessageSteps ()

@property (nonatomic, strong) NSDictionary<NSString *, NSDictionary<NSString *, NSString *> *> *messageList;

@end

@implementation MessageSteps

- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      [[FakeUPPushManager instance] initializationUPPush];
      [TestInterceptorCache.instance reset];
    });
    Given(@"^推送消息数据初始化如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      self.messageList = messageDataList(userInfo);
    });
    When(@"^信道收到的推送消息为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<NSString *> *messageEntry = [args[0] componentsSeparatedByString:@"."];
      [[FakeUPPushChannel instance] didRecieveRemoteNotification:@{ @"content" : self.messageList[messageEntry[0]][messageEntry[1]],
                                                                    @"aps" : @{@"alert" : @"title"} }];
    });
    When(@"^清除当前Badge数量$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[FakeUPPushManager instance].push syncBadge:0];
    });
    When(@"^设置当前Badge数量为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[FakeUPPushManager instance].push syncBadge:args[0].integerValue];
    });
    Then(@"拦截器\"([^\"]*)\"接收到的消息为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *prefix = @"messageObject";
      FakeUPPushAllInterceptor *interceptor = (FakeUPPushAllInterceptor *)[TestInterceptorCache.instance.interceptorDict objectForKey:args[0]];
      CCIAssert([args[1] isEqualToString:[NSString stringWithFormat:@"%@%@", prefix, interceptor.message.messageID]], @"推送通知解析错误");
    });
    Then(@"^获取当前Badge数量为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger badgeNumber = args[0].integerValue;
      NSInteger actualBadgeNumber = [[[FakeUPPushManager instance].push valueForKey:@"badgeManager"] badgeNumber];
      CCIAssert(actualBadgeNumber == badgeNumber, @"Badge 数量设置错误，badge为：%d，实际为：%d", badgeNumber, actualBadgeNumber);
    });
}
@end
