//
//  PushSteps.m
//  UPPushTests
//
//  Created by osiris on 2020/10/26.
//

#import "PushSteps.h"
#import <Cucumberish/Cucumberish.h>
#import <OCMock/OCMock.h>
#import "StepsUtils.h"
#import "FakeUPPushManager.h"
#import "FakeUPPushChannel.h"
#import "FakeUPPushProvider.h"
#import "FakeUPPushDataSource.h"
#import "FakeUPPushInterceptor.h"
#import "FakeUPPushBroadcastSender.h"
#import "TestInterceptorCache.h"
#import "FakeUPPushAllInterceptor.h"

@interface PushSteps ()

@property (nonatomic, assign) BOOL registerPushResult;
@property (nonatomic, assign) BOOL unregisterPushResult;
@property (nonatomic, assign) BOOL reportStatusResult;
@property (nonatomic, strong) id mockDispatcher;

@end

@implementation PushSteps

- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      [[FakeUPPushManager instance] initializationUPPush];
      self.registerPushResult = YES;
      self.unregisterPushResult = YES;
      self.reportStatusResult = YES;
      [TestInterceptorCache.instance reset];
      self.mockDispatcher = OCMPartialMock([FakeUPPushManager instance].dispatcher);
    });
    Given(@"^推送id提供者提供的推送id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *pushToken = nil;
      if (![args[0] isEqualToString:@"空对象"]) {
          pushToken = args[0];
      }
      [[FakeUPPushProvider instance] mockPushToken:[pushToken dataUsingEncoding:NSUTF8StringEncoding]];
    });
    Given(@"^推送数据源注册接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      [[FakeUPPushDataSource instance] mockRegisterPushResult:[result isEqualToString:@"成功"]];
    });
    Given(@"^推送数据源调用注销推送接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      [[FakeUPPushDataSource instance] mockUnregisterPushResult:[result isEqualToString:@"成功"]];
    });
    Given(@"^消息分发器为空$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[FakeUPPushManager instance] setDispatcherNull];
    });
    Given(@"拦截器\"([^\"]*)\"消息拦截条件为:", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      if ([TestInterceptorCache.instance.interceptorDict.allKeys containsObject:args[0]]) {
          return;
      }
      FakeUPPushInterceptor *interceptor = [[FakeUPPushInterceptor alloc] init];
      [interceptor mockInterceptorCheck:^BOOL(NSString *messageName) {
        return [messageName isEqualToString:interceptorCondition(userInfo).firstObject];
      }];
      [TestInterceptorCache.instance.interceptorDict setObject:interceptor forKey:args[0]];
    });
    Given(@"推送数据源上报消息状态返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      [[FakeUPPushDataSource instance] mockReportStatusResult:[result isEqualToString:@"成功"]];
    });
    Given(@"^推送库注册能力为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *param = args[0];
      NSArray *abilities = [param componentsSeparatedByString:@","];
      FakeUPPushAllInterceptor *buildInInterceptor = [FakeUPPushAllInterceptor new];
      [buildInInterceptor mockAbility:transform2Ability(abilities)];
      [[FakeUPPushManager instance].push registerInterceptor:buildInInterceptor];
    });
    When(@"^调用注册推送方法,自定义能力为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *param = args[0];
      NSArray *abilities = [param componentsSeparatedByString:@","];
      [[FakeUPPushManager instance]
              .push registerPush:transform2Ability(abilities)
                        callback:^(BOOL result) {
                          self.registerPushResult = result;
                        }];
    });
    When(@"^调用注册推送方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[FakeUPPushManager instance]
              .push registerPush:^(BOOL result) {
        self.registerPushResult = result;
      }];
    });
    When(@"^调用注销推送方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[FakeUPPushManager instance]
              .push unregisterPush:^(BOOL result) {
        self.unregisterPushResult = result;
      }];
    });
    When(@"^调用添加拦截器方法,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *param = args[0];
      if (![param isEqualToString:@"空对象"]) {
          if (TestInterceptorCache.instance.interceptorDict[param] == nil) {
              if ([param isEqualToString:@"fakeInterceptorAll"]) {
                  [TestInterceptorCache.instance.interceptorDict setObject:[FakeUPPushAllInterceptor new] forKey:param];
              }
              else {
                  [TestInterceptorCache.instance.interceptorDict setObject:[FakeUPPushInterceptor new] forKey:param];
              }
          }
          [[FakeUPPushManager instance].push registerInterceptor:TestInterceptorCache.instance.interceptorDict[param]];
      }
      else {
          [[FakeUPPushManager instance].push registerInterceptor:nil];
      }
    });
    When(@"^调用移除拦截器方法,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *param = args[0];
      if (![param isEqualToString:@"空对象"]) {
          [[FakeUPPushManager instance].push unregisterInterceptor:TestInterceptorCache.instance.interceptorDict[param]];
          [TestInterceptorCache.instance.interceptorDict removeObjectForKey:param];
      }
      else {
          [[FakeUPPushManager instance].push unregisterInterceptor:nil];
      }
    });
    When(@"^推送信道收到一条消息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[FakeUPPushChannel instance].delegate recieveMessage:createOriginalMessageList(userInfo).firstObject];
    });
    When(@"^调用消息状态上报接口,传入消息ID为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *param = args[0];
      if ([param isEqualToString:@"空对象"]) {
          param = nil;
      }
      else if ([param isEqualToString:@"空字符串"]) {
          param = @"";
      }
      [[FakeUPPushManager instance]
              .push report:param
                    status:Message_Status_read
                  callBack:^(BOOL result) {
                    self.reportStatusResult = result;
                  }];
    });
    Then(@"^推送id提供者的获取推送id接口调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger invocationTimes = args[0].integerValue;
      NSUInteger actualTimes = [[FakeUPPushProvider instance] getInvocationTimesOfSelector:@selector(providePushToken)] > 0 ? invocationTimes : 0;
      CCIAssert(actualTimes == invocationTimes, @"推送id提供者的获取推送id接口被调用:%ld,实际为:%ld", invocationTimes, actualTimes);
    });
    Then(@"^推送数据源的注册接口调用\"([^\"]*)\"次,参数为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger invocationTimes = args[0].integerValue;
      NSUInteger actualTimes = [[FakeUPPushDataSource instance] getInvocationTimesOfSelector:@selector(registerPush:clientType:ability:deviceAlias:version:channel:result:)];
      CCIAssert(actualTimes == invocationTimes, @"推送数据源的注册接口被调用:%ld,实际为:%ld", invocationTimes, actualTimes);
      NSArray<NSString *> *paramArray = pushDataSource(userInfo);
      NSString *registerPushParam = [NSString stringWithFormat:@"pushId=%@,channel=%@,devAlias=%@,version=%@,ability=%@,clientType=%@", paramArray[0], paramArray[1], paramArray[2], paramArray[3], paramArray[4], paramArray[5]];
      NSArray *params = [[FakeUPPushDataSource instance] getParametersOfSelector:@selector(registerPush:clientType:ability:deviceAlias:version:channel:result:)];
      NSString *actualRegisterPushParam = [NSString stringWithFormat:@"pushId=%@,channel=%@,devAlias=%@,version=%@,ability=%@,clientType=%@", params[0], params[1], params[2], params[3], transformAbilities(params[4]), params[5]];
      CCIAssert([actualRegisterPushParam isEqualToString:registerPushParam], @"推送数据源的注册参数为%@实际为%@", registerPushParam, actualRegisterPushParam);
    });
    Then(@"^调用回调函数注册结果,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      CCIAssert(self.registerPushResult == [args[0] isEqualToString:@"成功"], @"注册推送结果与实际不一致");
    });
    Then(@"^推送数据源的注销接口调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger invocationTimes = args[0].integerValue;
      NSUInteger actualTimes = [[FakeUPPushDataSource instance] getInvocationTimesOfSelector:@selector(unregisterPush:)];
      CCIAssert(actualTimes == invocationTimes, @"推送数据源的注销接口被调用:%ld,实际为:%ld", invocationTimes, actualTimes);
    });
    Then(@"^调用回调函数注销结果,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      CCIAssert(self.unregisterPushResult == [args[0] isEqualToString:@"成功"], @"注销推送结果与实际不一致");
    });
    Then(@"^拦截器列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      CCIAssert(checkInterceptorArrayIsEqual(TestInterceptorCache.instance.interceptorDict.allKeys, getTableDataInterceptor(userInfo)), @"拦截器列表结果与实际不一致");
    });
    Then(@"^消息分发器的分发消息方法被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo){
             //        OCMVerify([self.mockDispatcher dispatch:createOriginalMessageList(userInfo).firstObject]);
         });
    Then(@"^消息分发器的分发消息方法被调用\"([^\"]*)\"次,参数为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo){
             //        OCMVerify([self.mockDispatcher dispatch:createOriginalMessageList(userInfo).firstObject]);
         });
    Then(@"^广播发送器,发送如下消息:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      id<UPPushMessageProtocol> actualMessage = [[FakeUPPushBroadcastSender instance] getParametersOfSelector:@selector(sendBroadcast:)].firstObject;
      id<UPPushMessageProtocol> message = createMessageList(userInfo).firstObject;
      CCIAssert([message.messageID isEqualToString:actualMessage.messageID] &&
                    [message.messageName isEqualToString:actualMessage.messageName],
                @"广播发送器,发送消息结果与实际不一致");
    });
    Then(@"^消息拦截器\"([^\"]*)\"的处理方法被调用\"([^\"]*)\"次,参数为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger invocationTimes = args[1].integerValue;
      NSUInteger actualTimes = [(FakeUPPushInterceptor *)TestInterceptorCache.instance.interceptorDict[args[0]] getInvocationTimesOfSelector:@selector(intercept:)];
      CCIAssert(actualTimes == invocationTimes, @"^消息拦截器处理方法被调用:%ld,实际为:%ld", invocationTimes, actualTimes);
      id<UPPushMessageProtocol> message = createMessageList(userInfo).firstObject;
      id<UPPushMessageProtocol> actualMessage = [(FakeUPPushInterceptor *)TestInterceptorCache.instance.interceptorDict[args[0]] getParametersOfSelector:@selector(intercept:)].firstObject;
      CCIAssert([message.messageID isEqualToString:actualMessage.messageID] &&
                    [message.messageName isEqualToString:actualMessage.messageName],
                @"消息拦截器方法参数消息结果与实际不一致");
    });
    Then(@"^消息状态上报接口回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      CCIAssert(self.reportStatusResult == [args[0] isEqualToString:@"成功"], @"上报消息状态结果与实际不一致");
    });
    Then(@"^推送数据源消息状态上报接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger invocationTimes = args[0].integerValue;
      NSUInteger actualTimes = [[FakeUPPushDataSource instance] getInvocationTimesOfSelector:@selector(reportMessage:status:result:)];
      CCIAssert(actualTimes == invocationTimes, @"推送数据源的上报消息状态接口被调用:%ld,实际为:%ld", invocationTimes, actualTimes);
    });
    Then(@"^推送数据源消息状态上报接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger invocationTimes = args[0].integerValue;
      NSUInteger actualTimes = [[FakeUPPushDataSource instance] getInvocationTimesOfSelector:@selector(reportMessage:status:result:)];
      CCIAssert(actualTimes == invocationTimes, @"推送数据源的上报消息状态接口被调用:%ld,实际为:%ld", invocationTimes, actualTimes);
      NSString *message = [[FakeUPPushDataSource instance] getParametersOfSelector:@selector(reportMessage:status:result:)].firstObject;
      CCIAssert([message isEqualToString:args[1]], @"推送数据源的上报消息状态接口被调用参数为:%ld,实际为:%ld");
    });
    Then(@"^获取推送ID接口返回结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *actualPushID = [FakeUPPushManager instance].push.pushID;
      NSString *expectPushID = args[0];
      if ([expectPushID isEqualToString:@"空对象"]) {
          expectPushID = @"";
      }
      CCIAssert([expectPushID isEqualToString:actualPushID], @"获取推送ID为:%ld,实际为:%ld", expectPushID, actualPushID);
    });
}

@end
