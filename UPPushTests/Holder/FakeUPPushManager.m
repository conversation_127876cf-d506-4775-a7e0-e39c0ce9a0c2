//
//  FakeUPPushManager.m
//  UPPushTests
//
//  Created by osiris on 2020/10/26.
//

#import "FakeUPPushManager.h"
#import "FakeUPPushInitializer.h"
#import "FakeUPPushProvider.h"
#import "FakeUPPushChannel.h"
#import "FakeUPPushDataSource.h"
#import "FakeUPPushBroadcastSender.h"
#import "FakeUPPushSettings.h"
#import "UPPushDispatcherProtocol.h"

@implementation FakeUPPushManager

+ (instancetype)instance
{
    static FakeUPPushManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[FakeUPPushManager alloc] init];
    });

    return instance;
}

- (void)initializationUPPush
{
    [[FakeUPPushProvider instance] clearFakeDataInfo];
    [[FakeUPPushChannel instance] clearFakeDataInfo];
    [[FakeUPPushDataSource instance] clearFakeDataInfo];
    [[FakeUPPushBroadcastSender instance] clearFakeDataInfo];
    [[FakeUPPushSettings instance] clearFakeDataInfo];
    FakeUPPushInitializer *fakeConfig = [FakeUPPushInitializer instance];
    fakeConfig.provider = [FakeUPPushProvider instance];
    fakeConfig.channel = [FakeUPPushChannel instance];
    fakeConfig.dataSource = [FakeUPPushDataSource instance];
    fakeConfig.broadcastSender = [FakeUPPushBroadcastSender instance];
    fakeConfig.settings = [FakeUPPushSettings instance];
    _push = [[UPPush alloc] initWithInitializer:fakeConfig];
}

#pragma mark - public
- (id<UPPushDispatcherProtocol>)dispatcher
{
    return [_push valueForKey:@"dispatcher"];
}

- (void)setDispatcherNull
{
    [_push setValue:nil forKey:@"dispatcher"];
}
@end
