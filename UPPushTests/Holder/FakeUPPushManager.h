//
//  FakeUPPushManager.h
//  UPPushTests
//
//  Created by osiris on 2020/10/26.
//

#import <Foundation/Foundation.h>
#import "UPPush.h"
#import "UPPushDispatcherProtocol.h"

NS_ASSUME_NONNULL_BEGIN
@interface FakeUPPushManager : NSObject

@property (nonatomic, strong, readonly) id<UPPushDispatcherProtocol> dispatcher;
@property (nonatomic, strong, readonly) UPPush *push;
+ (instancetype)instance;
- (void)initializationUPPush;

- (void)setDispatcherNull;
@end

NS_ASSUME_NONNULL_END
