PODS:
  - AFNetworking (4.0.1):
    - AFNetworking/NSURLSession (= 4.0.1)
    - AFNetworking/Reachability (= 4.0.1)
    - AFNetworking/Security (= 4.0.1)
    - AFNetworking/Serialization (= 4.0.1)
    - AFNetworking/UIKit (= 4.0.1)
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (4.0.1)
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AFNetworking/UIKit (4.0.1):
    - AFNetworking/NSURLSession
  - Cucumberish (1.4.0)
  - Firebase/Analytics (8.12.1):
    - Firebase/Core
  - Firebase/Core (8.12.1):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 8.12.0)
  - Firebase/CoreOnly (8.12.1):
    - FirebaseCore (= 8.12.1)
  - Firebase/Messaging (8.12.1):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 8.12.0)
  - FirebaseAnalytics (8.12.0):
    - FirebaseAnalytics/AdIdSupport (= 8.12.0)
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (~> 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (8.12.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleAppMeasurement (= 8.12.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (~> 2.30908.0)
  - FirebaseCore (8.12.1):
    - FirebaseCoreDiagnostics (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
  - FirebaseCoreDiagnostics (8.15.0):
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
    - nanopb (~> 2.30908.0)
  - FirebaseInstallations (8.15.0):
    - FirebaseCore (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - PromisesObjC (< 3.0, >= 1.2)
  - FirebaseMessaging (8.12.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Reachability (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - nanopb (~> 2.30908.0)
  - GoogleAppMeasurement (8.12.0):
    - GoogleAppMeasurement/AdIdSupport (= 8.12.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (~> 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (8.12.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 8.12.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (~> 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (8.12.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (~> 2.30908.0)
  - GoogleDataTransport (9.2.5):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.11.5):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.11.5):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.11.5):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.11.5):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.11.5):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.11.5)"
  - GoogleUtilities/Reachability (7.11.5):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.11.5):
    - GoogleUtilities/Logger
  - JCore (3.2.9)
  - JPush (4.8.1):
    - JCore (>= 2.0.0)
  - MJExtension (3.2.1)
  - nanopb (2.30908.0):
    - nanopb/decode (= 2.30908.0)
    - nanopb/encode (= 2.30908.0)
  - nanopb/decode (2.30908.0)
  - nanopb/encode (2.30908.0)
  - OCMock (3.8.1)
  - PromisesObjC (2.3.1)
  - Protobuf (3.17.0)
  - UHMasonry (1.1.2.2023060801)
  - uplog (1.5.1):
    - AFNetworking (>= 4.0.1)
    - Protobuf (= 3.17.0)
    - ZipArchive (>= 1.4.0)
  - upnetwork (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign (= 4.0.6)
    - upnetwork/Headers (= 4.0.6)
    - upnetwork/HTTPDns (= 4.0.6)
    - upnetwork/Manager (= 4.0.6)
    - upnetwork/Request (= 4.0.6)
    - upnetwork/Settings (= 4.0.6)
    - upnetwork/Utils (= 4.0.6)
  - upnetwork/DynamicSign (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign/Private (= 4.0.6)
  - upnetwork/DynamicSign/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Headers (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Headers/Private (= 4.0.6)
  - upnetwork/Headers/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/HTTPDns (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Manager (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Manager/Private (= 4.0.6)
  - upnetwork/Manager/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Request (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Request/Private (= 4.0.6)
  - upnetwork/Request/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Settings (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Settings/Private (= 4.0.6)
  - upnetwork/Settings/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Utils (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Utils/Private (= 4.0.6)
  - upnetwork/Utils/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - UPTools/ModuleLanguage (0.1.33):
    - AFNetworking (>= 3.1.0)
    - UHMasonry (>= 1.1.1)
  - upuserdomain (3.15.1):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
    - upuserdomain/upuserdomain (= 3.15.1)
    - upuserdomain/UserDomainAPIs (= 3.15.1)
    - upuserdomain/UserDomainDataSource (= 3.15.1)
  - upuserdomain/upuserdomain (3.15.1):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - upuserdomain/UserDomainAPIs (3.15.1):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - upuserdomain/UserDomainDataSource (3.15.1):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - UPVDN (*******.2023032102):
    - uplog (>= 1.1.2)
    - UPVDN/Back (= *******.2023032102)
    - UPVDN/Categorys (= *******.2023032102)
    - UPVDN/Launcher (= *******.2023032102)
    - UPVDN/Page (= *******.2023032102)
    - UPVDN/Patch (= *******.2023032102)
    - UPVDN/ResultListener (= *******.2023032102)
    - UPVDN/Utils (= *******.2023032102)
    - UPVDN/VDNManager (= *******.2023032102)
    - UPVDN/Vdns (= *******.2023032102)
    - UPVDN/VirtualDomain (= *******.2023032102)
  - UPVDN/Back (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Categorys (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Launcher (*******.2023032102):
    - uplog (>= 1.1.2)
    - UPVDN/Launcher/Native (= *******.2023032102)
  - UPVDN/Launcher/Native (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Page (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Patch (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/ResultListener (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Utils (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/VDNManager (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Vdns (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/VirtualDomain (*******.2023032102):
    - uplog (>= 1.1.2)
  - ZipArchive (1.4.0)

DEPENDENCIES:
  - AFNetworking (= 4.0.1)
  - Cucumberish (= 1.4.0)
  - Firebase/Analytics (= 8.12.1)
  - Firebase/Messaging (= 8.12.1)
  - JCore (= 3.2.9)
  - JPush (= 4.8.1)
  - MJExtension (= 3.2.1)
  - OCMock (= 3.8.1)
  - UHMasonry (= 1.1.2.2023060801)
  - uplog (= 1.5.1)
  - upnetwork (= 4.0.6)
  - UPTools/ModuleLanguage (= 0.1.33)
  - upuserdomain (= 3.15.1)
  - UPVDN (= *******.2023032102)

SPEC REPOS:
  https://git.haier.net/uplus/shell/cocoapods/Specs.git:
    - UHMasonry
    - uplog
    - upnetwork
    - UPTools
    - upuserdomain
    - UPVDN
  https://github.com/CocoaPods/Specs.git:
    - AFNetworking
    - Cucumberish
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - JCore
    - JPush
    - MJExtension
    - nanopb
    - OCMock
    - PromisesObjC
    - Protobuf
    - ZipArchive

SPEC CHECKSUMS:
  AFNetworking: 3bd23d814e976cd148d7d44c3ab78017b744cd58
  Cucumberish: 6cbd0c1f50306b369acebfe7d9f514c9c287d26c
  Firebase: 580d09e8edafc3073ebf09c03fd42e4d80d35fe9
  FirebaseAnalytics: bd10d7706ba8d6e01ea2816f8fe9e9b881cb0918
  FirebaseCore: 8138de860a90ca7eec5e324da5788fb0ebf1d93c
  FirebaseCoreDiagnostics: 92e07a649aeb66352b319d43bdd2ee3942af84cb
  FirebaseInstallations: 40bd9054049b2eae9a2c38ef1c3dd213df3605cd
  FirebaseMessaging: 23db8bf05585e929ada8af0f0968933c25252808
  GoogleAppMeasurement: ae033c3aad67e68294369373056b4d74cc8ae0d6
  GoogleDataTransport: 54dee9d48d14580407f8f5fbf2f496e92437a2f2
  GoogleUtilities: 13e2c67ede716b8741c7989e26893d151b2b2084
  JCore: 077afdffd7772ecc3f9faef9727af040d31f1d7b
  JPush: 2caabdb6d62d009bc07a9bd115d03c7c42512b5e
  MJExtension: 635f2c663dcb1bf76fa4b715b2570a5710aec545
  nanopb: a0ba3315591a9ae0a16a309ee504766e90db0c96
  OCMock: 29f6e52085b4e7d9b075cbf03ed7c3112f82f934
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4
  Protobuf: 7327d4444215b5f18e560a97f879ff5503c4581c
  UHMasonry: 3be12fd6fbbb52fad07a26b4e0a8c667c9fd24f4
  uplog: 7774a64dab5e5178282819e1e63ad57ae415e805
  upnetwork: 5209ebffb17785e0aafd7344888908f702231c68
  UPTools: 9e12fdee13a51234f2729823db70075f7dabd156
  upuserdomain: 81aa314740841ec57ce2283a7bddd21078f4ebd5
  UPVDN: cf8f1246c0af89bae2f5b7191880a177f1319d8e
  ZipArchive: e25a4373192673e3229ac8d6e9f64a3e5713c966

PODFILE CHECKSUM: 7c90db46b677ddd89e83063892dea0f520bfa5f9

COCOAPODS: 1.16.2
