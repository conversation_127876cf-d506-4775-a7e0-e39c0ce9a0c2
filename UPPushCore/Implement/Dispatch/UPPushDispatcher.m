//
//  UPPushDispatcher.m
//  UPPush
//
//  Created by osiris on 2020/10/28.
//

#import "UPPushDispatcher.h"
#import <MJExtension/MJExtension.h>
#import <UPLog/UPLog.h>

@interface UPPushDispatcher ()

@property (nonatomic, strong) id<UPPushParserProtocol> parser;
@property (nonatomic, strong) id<UPPushContextProtocol> context;

@end

@implementation UPPushDispatcher

- (nonnull instancetype)initWithParser:(nonnull id<UPPushParserProtocol>)parser context:(nonnull id<UPPushContextProtocol>)context
{
    if (self = [super init]) {
        _parser = parser;
        _context = context;
    }

    return self;
}

- (void)dispatch:(id<UPPushOriginalMessageProtocol>)originalMessage
{
    id<UPPushMessageProtocol> message = [_parser parse:originalMessage];
    if (message == nil) {
        UPLogError(@"UPPush", @"<UPPush Debugging> 消息解析失败：%@", [originalMessage.originalMessage mj_JSONString]);
        return;
    }

    BOOL isInterceptByExternalInterceptor = [self interceptMessage:message by:[_context.interceptors.externalInterceptors copy]];

    if (!isInterceptByExternalInterceptor) {
        [self interceptMessage:message by:[_context.interceptors.buildInInterceptors copy]];
    }

    [_context.broadcastSender sendBroadcast:message];
}

#pragma mark - private
- (BOOL)interceptMessage:(id<UPPushMessageProtocol>)message by:(NSArray<id<UPPushInterceptorProtocol>> *)interceptors
{
    BOOL result = NO;
    NSArray<id<UPPushInterceptorProtocol>> *tempInterceptors = [interceptors copy];
    for (id<UPPushInterceptorProtocol> interceptor in tempInterceptors) {
        if (![interceptor respondsToSelector:@selector(isFire)] || ![interceptor isFire]) {
            continue;
        }
        if ([interceptor respondsToSelector:@selector(check:)] && [interceptor check:message]) {
            [interceptor respondsToSelector:@selector(intercept:)] ? [interceptor intercept:message] : nil;
            result = YES;
        }
    }

    return result;
}
@end
