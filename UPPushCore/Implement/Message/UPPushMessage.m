//
//  UPPushMessage.m
//  UPPushCore
//
//  Created by osiris on 2020/11/2.
//

#import "UPPushMessage.h"
#import <MJExtension/MJExtension.h>
#import "UPPushMessageBody.h"

@interface UPPushMessage ()

@property (nonatomic, copy) NSString *alertBody;
@property (nonatomic, copy) NSString *alertSubTitle;
@property (nonatomic, copy) NSString *alertTitle;
@property (nonatomic, assign) NSInteger businessType;
@property (nonatomic, assign) NSInteger expires;
@property (nonatomic, assign) BOOL isLongMessage;
@property (nonatomic, copy) NSString *messageDescription;
@property (nonatomic, assign) NSInteger messageHistoryExpires;
@property (nonatomic, copy) NSString *messageID;
@property (nonatomic, copy) NSString *messageName;
@property (nonatomic, copy) NSString *messageSound;
@property (nonatomic, assign) NSInteger messageType;
@property (nonatomic, assign) NSInteger timestamp;
@property (nonatomic, strong) UPPushMessageBody *messageBody;

@end

@implementation UPPushMessage
@synthesize isCustom, isLocal, receivePushState = _receivePushState, decodeContent;

- (instancetype)init
{
    if (self = [super init]) {
        _receivePushState = ReceivePush_State_None;
    }

    return self;
}

+ (NSDictionary *)mj_replacedKeyFromPropertyName
{
    return @{
        @"alertTitle" : @"alert.title",
        @"alertSubTitle" : @"alert.subtitle",
        @"alertBody" : @"alert.body",
        @"messageID" : @"msgId",
        @"messageType" : @"msgType",
        @"messageName" : @"msgName",
        @"messageDescription" : @"msgDesc",
        @"isLongMessage" : @"isLong",
        @"messageHistoryExpires" : @"msgHistoryExpires",
        @"messageBody" : @"body",
        @"messageSound" : @"sound"
    };
}

+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"receivePushState", @"decodeContent" ];
}

#pragma mark - NSCopying
- (nonnull id)copyWithZone:(nullable NSZone *)zone
{
    UPPushMessage *message = [[[self class] allocWithZone:zone] init];
    message.alertTitle = self.alertTitle;
    message.alertBody = self.alertBody;
    message.alertSubTitle = self.alertSubTitle;
    message.businessType = self.businessType;
    message.expires = self.expires;
    message.messageHistoryExpires = self.messageHistoryExpires;
    message.messageID = self.messageID;
    message.messageType = self.messageType;
    message.messageName = self.messageName;
    message.messageDescription = self.messageDescription;
    message.timestamp = self.timestamp;
    message.isLongMessage = self.isLongMessage;
    message.receivePushState = self.receivePushState;
    message.isLocal = self.isLocal;
    message.isCustom = self.isCustom;
    message.messageBody = [self.messageBody copy];
    message.messageSound = self.messageSound;
    return message;
}

@end
