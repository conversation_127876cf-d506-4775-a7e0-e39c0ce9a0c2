//
//  UPPushMessageBody.m
//  UPPushCore
//
//  Created by osiris on 2020/11/2.
//

#import "UPPushMessageBody.h"
#import <MJExtension/MJExtension.h>
#import "UPPushMessageUI.h"
#import "UPPushMessageExtraData.h"

@interface UPPushMessageBody ()

@property (nonatomic, strong) NSDictionary *extraParam;
@property (nonatomic, strong) UPPushMessageUI *messageUI;
@property (nonatomic, strong) UPPushMessageExtraData *extraData;

@end

@implementation UPPushMessageBody
@synthesize extraData, extraParam, messageUI;

+ (NSDictionary *)mj_replacedKeyFromPropertyName
{
    return @{
        @"extraParam" : @"extParam",
        @"messageUI" : @"view",
        @"extraData" : @"extData",
    };
}

#pragma mark - NSCopying
- (nonnull id)copyWithZone:(nullable NSZone *)zone
{
    UPPushMessageBody *messageBody = [[[self class] allocWithZone:zone] init];
    messageBody.extraData = [self.extraData copy];
    messageBody.messageUI = [self.messageUI copy];
    messageBody.extraParam = [self.extraParam copy];
    return messageBody;
}
@end
