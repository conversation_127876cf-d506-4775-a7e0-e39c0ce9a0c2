//
//  UPPushMessagePage.m
//  UPPushCore
//
//  Created by osiris on 2020/11/2.
//

#import "UPPushMessagePage.h"
#import <MJExtension/MJExtension.h>

@interface UPPushMessagePage ()

@property (nonatomic, assign) NSInteger callID;
@property (nonatomic, copy) NSString *url;
@property (nonatomic, strong) NSDictionary *params;

@end
@implementation UPPushMessagePage

+ (NSDictionary *)mj_replacedKeyFromPropertyName
{
    return @{
        @"callID" : @"callId",
    };
}

#pragma mark - NSCopying
- (nonnull id)copyWithZone:(nullable NSZone *)zone
{
    UPPushMessagePage *messagePage = [[[self class] allocWithZone:zone] init];
    messagePage.callID = self.callID;
    messagePage.url = self.url;
    messagePage.params = [self.params copy];
    return messagePage;
}
@end
