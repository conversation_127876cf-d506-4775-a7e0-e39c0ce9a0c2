//
//  UPPushMessageDeviceControl.m
//  UPPushCore
//
//  Created by osiris on 2020/11/2.
//

#import "UPPushMessageDeviceControl.h"
#import <MJExtension/MJExtension.h>

@interface UPPushMessageDeviceControl ()

@property (nonatomic, assign) NSInteger callID;
@property (nonatomic, copy) NSString *deviceID;
@property (nonatomic, copy) NSString *groupName;
@property (nonatomic, strong) NSDictionary<NSString *, NSString *> *commandList;

@end
@implementation UPPushMessageDeviceControl
+ (NSDictionary *)mj_replacedKeyFromPropertyName
{
    return @{
        @"callID" : @"callId",
        @"deviceID" : @"deviceId",
        @"commandList" : @"cmdList"
    };
}

+ (NSDictionary *)mj_objectClassInArray
{

    return @{
        @"commandList" : [NSDictionary class]
    };
}

+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"commandList"]) {
          [property.type setValue:NSClassFromString(@"NSArray") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}

#pragma mark - NSCopying
- (nonnull id)copyWithZone:(nullable NSZone *)zone
{
    UPPushMessageDeviceControl *messageDeviceControl = [[[self class] allocWithZone:zone] init];
    messageDeviceControl.callID = self.callID;
    messageDeviceControl.deviceID = self.deviceID;
    messageDeviceControl.groupName = self.groupName;
    messageDeviceControl.commandList = [self.commandList copy];
    return messageDeviceControl;
}

@end
