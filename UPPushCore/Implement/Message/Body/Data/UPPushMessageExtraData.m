//
//  UPPushMessageExtraData.m
//  UPPushCore
//
//  Created by osiris on 2020/11/2.
//

#import "UPPushMessageExtraData.h"
#import <MJExtension/MJExtension.h>
#import "UPPushMessageDevice.h"
#import "UPPushMessagePage.h"
#import "UPPushMessageDeviceControl.h"
#import "UPPushMessageAPI.h"

@interface UPPushMessageExtraData ()

@property (nonatomic, assign) NSInteger expireTime;
@property (nonatomic, assign) NSInteger isSaveInMessageCenter;
@property (nonatomic, strong) NSArray<UPPushMessageDevice *> *device;
@property (nonatomic, strong) UPPushMessageAPI *api;
@property (nonatomic, copy) NSString *targetPage;
@property (nonatomic, strong) NSArray<UPPushMessagePage *> *pages;
@property (nonatomic, strong) UPPushMessagePage *page;
@property (nonatomic, strong) NSArray<UPPushMessageDeviceControl *> *deviceControlList;
@property (nonatomic, strong) UPPushMessageDeviceControl *deviceControl;
@property (nonatomic, strong) NSString *clientID;
@property (nonatomic, strong) NSString *deliverData;

@end
@implementation UPPushMessageExtraData

+ (NSDictionary *)mj_replacedKeyFromPropertyName
{
    return @{
        @"isSaveInMessageCenter" : @"isMsgCenter",
        @"clientID" : @"clientId",
        @"deviceControl" : @"devControl",
        @"deviceControlList" : @"devControls"
    };
}

+ (NSDictionary *)mj_objectClassInArray
{

    return @{
        @"device" : [UPPushMessageDevice class],
        @"pages" : [UPPushMessagePage class],
        @"deviceControlList" : [UPPushMessageDeviceControl class],
    };
}

+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"device"]) {
          [property.type setValue:NSClassFromString(@"NSArray") forKey:@"typeClass"];
      }
      if ([property.name isEqualToString:@"pages"]) {
          [property.type setValue:NSClassFromString(@"NSArray") forKey:@"typeClass"];
      }
      if ([property.name isEqualToString:@"deviceControlList"]) {
          [property.type setValue:NSClassFromString(@"NSArray") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}

#pragma mark - NSCopying
- (nonnull id)copyWithZone:(nullable NSZone *)zone
{
    UPPushMessageExtraData *messageExtraData = [[[self class] allocWithZone:zone] init];
    messageExtraData.expireTime = self.expireTime;
    messageExtraData.isSaveInMessageCenter = self.isSaveInMessageCenter;
    NSMutableArray *tempDevices = [NSMutableArray array];
    [self.device enumerateObjectsUsingBlock:^(id<UPPushMessageDeviceProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [tempDevices addObject:[obj copyWithZone:zone]];
    }];
    messageExtraData.device = [tempDevices copy];
    messageExtraData.api = [self.api copyWithZone:zone];
    messageExtraData.targetPage = self.targetPage;
    NSMutableArray *tempPages = [NSMutableArray array];
    [self.pages enumerateObjectsUsingBlock:^(id<UPPushMessagePageProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [tempPages addObject:[obj copyWithZone:zone]];
    }];
    messageExtraData.pages = [tempPages copy];
    messageExtraData.page = [self.page copyWithZone:zone];
    NSMutableArray *tempDeviceControls = [NSMutableArray array];
    [self.deviceControlList enumerateObjectsUsingBlock:^(id<UPPushMessageDeviceControlProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [tempDeviceControls addObject:[obj copyWithZone:zone]];
    }];
    messageExtraData.deviceControlList = [tempDeviceControls copy];
    messageExtraData.deviceControl = [self.deviceControl copyWithZone:zone];
    messageExtraData.clientID = self.clientID;
    messageExtraData.deliverData = self.deliverData;
    return messageExtraData;
}
@end
