//
//  UPPushMessageDevice.m
//  UPPushCore
//
//  Created by osiris on 2020/11/2.
//

#import "UPPushMessageDevice.h"
#import <MJExtension/MJExtension.h>

@interface UPPushMessageDevice ()

@property (nonatomic, assign) NSInteger typeID;
@property (nonatomic, copy) NSString *deviceID;
@property (nonatomic, strong) NSString *deviceName;

@end

@implementation UPPushMessageDevice

+ (NSDictionary *)mj_replacedKeyFromPropertyName
{
    return @{
        @"typeID" : @"typeId",
        @"deviceID" : @"deviceId",
    };
}

#pragma mark - NSCopying
- (nonnull id)copyWithZone:(nullable NSZone *)zone
{
    UPPushMessageDevice *messageDevice = [[[self class] allocWithZone:zone] init];
    messageDevice.typeID = self.typeID;
    messageDevice.deviceID = self.deviceID;
    messageDevice.deviceName = self.deviceName;
    return messageDevice;
}
@end
