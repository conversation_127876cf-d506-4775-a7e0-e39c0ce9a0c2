//
//  UPPushMessageAPI.m
//  UPPushCore
//
//  Created by osiris on 2020/11/2.
//

#import "UPPushMessageAPI.h"
#import <MJExtension/MJExtension.h>

@interface UPPushMessageAPI ()

@property (nonatomic, assign) NSInteger callID;
@property (nonatomic, copy) NSString *apiType;
@property (nonatomic, strong) NSDictionary *params;

@end
@implementation UPPushMessageAPI

+ (NSDictionary *)mj_replacedKeyFromPropertyName
{
    return @{
        @"callID" : @"callId",
    };
}

#pragma mark - NSCopying
- (nonnull id)copyWithZone:(nullable NSZone *)zone
{
    UPPushMessageAPI *messageAPI = [[[self class] allocWithZone:zone] init];
    messageAPI.callID = self.callID;
    messageAPI.apiType = self.apiType;
    messageAPI.params = [self.params copy];
    return messageAPI;
}
@end
