//
//  UPPushMessageUI.m
//  UPPushCore
//
//  Created by osiris on 2020/11/2.
//

#import "UPPushMessageUI.h"
#import <MJExtension/MJExtension.h>
#import "UPPushMessageUIButton.h"

@interface UPPushMessageUI ()

@property (nonatomic, assign) UPPushMessageUIType showTypeAppearance;
@property (nonatomic, assign) NSInteger showType;
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *content;
@property (nonatomic, copy) NSArray<id<UPPushMessageUIButtonProtocol>> *buttons;

@end
@implementation UPPushMessageUI

- (instancetype)init
{
    if (self = [super init]) {
        _showTypeAppearance = MessageUI_Unknown;
    }

    return self;
}

+ (NSDictionary *)mj_replacedKeyFromPropertyName
{
    return @{
        @"buttons" : @"btns"
    };
}

+ (NSDictionary *)mj_objectClassInArray
{
    return @{
        @"buttons" : [UPPushMessageUIButton class]

    };
}

+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"buttons"]) {
          [property.type setValue:NSClassFromString(@"NSArray") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}

- (UPPushMessageUIType)showTypeAppearance
{
    _showTypeAppearance = [self matchShowType];
    return _showTypeAppearance;
}

#pragma mark - private
- (UPPushMessageUIType)matchShowType
{
    UPPushMessageUIType resultType = MessageUI_Unknown;
    __block NSInteger showTypeGroup = -999;
    NSArray<NSArray<NSNumber *> *> *showTypeArray = @[ @[ @(-1) ], @[ @0 ], @[ @2, @3, @5, @20, @21, @22 ], @[ @4 ], @[ @6 ] ];
    for (int i = 0; i < showTypeArray.count; i++) {
        [showTypeArray[i] enumerateObjectsUsingBlock:^(NSNumber *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          if (self.showType == obj.integerValue) {
              showTypeGroup = i;
          }
        }];
    }
    switch (showTypeGroup) {
        case 0:
            resultType = MessageUI_Jump;
            break;
        case 1:
            resultType = MessageUI_Toast;
            break;
        case 2:
            resultType = MessageUI_Alert;
            break;
        case 3:
            resultType = MessageUI_ExclamationMark;
            break;
        case 4:
            resultType = MessageUI_TapToast;
            break;
        default:
            break;
    }

    return resultType;
}

#pragma mark - NSCopying
- (nonnull id)copyWithZone:(nullable NSZone *)zone
{
    UPPushMessageUI *messageUI = [[[self class] allocWithZone:zone] init];
    messageUI.showTypeAppearance = self.showTypeAppearance;
    messageUI.showType = self.showType;
    messageUI.title = self.title;
    messageUI.content = self.content;
    NSMutableArray *tempButtons = [NSMutableArray array];
    [self.buttons enumerateObjectsUsingBlock:^(id<UPPushMessageUIButtonProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [tempButtons addObject:[obj copyWithZone:zone]];
    }];
    messageUI.buttons = [tempButtons copy];
    return messageUI;
}
@end
