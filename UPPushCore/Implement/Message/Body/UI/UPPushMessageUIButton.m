//
//  UPPushMessageUIButton.m
//  UPPushCore
//
//  Created by osiris on 2020/11/2.
//

#import "UPPushMessageUIButton.h"
#import <MJExtension/MJExtension.h>

@interface UPPushMessageUIButton ()

@property (nonatomic, copy) NSString *text;
@property (nonatomic, assign) NSInteger callID;

@end
@implementation UPPushMessageUIButton
+ (NSDictionary *)mj_replacedKeyFromPropertyName
{
    return @{
        @"callID" : @"callId",
    };
}

#pragma mark - NSCopying
- (nonnull id)copyWithZone:(nullable NSZone *)zone
{
    UPPushMessageUIButton *messageUIButton = [[[self class] allocWithZone:zone] init];
    messageUIButton.text = self.text;
    messageUIButton.callID = self.callID;

    return messageUIButton;
}
@end
