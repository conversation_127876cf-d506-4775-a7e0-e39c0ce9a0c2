//
//  UPPush.m
//  UPPush
//
//  Created by osiris on 2020/10/15.
//

#import "UPPush.h"
#import "UPPushChannelProtocol.h"
#import "UPPushDispatcher.h"
#import "UPPushBroadcastSenderProtocol.h"
#import "UPPushParser.h"
#import "UPPushContextProtocol.h"
#import "UPPushRetryAssistant.h"
#import "UPPushBadgeManager.h"
#import "UPPushInterceptorManager.h"
#import "UPPushStringTools.h"

@interface UPPush () <UPPushContextProtocol, UPPushBadgeDelegate>

@property (nonatomic, strong) id<UPPushInitializerProtocol> pushInitializer;
@property (nonatomic, strong) UPPushDispatcher *dispatcher;
@property (nonatomic, strong) UPPushInterceptorManager *interceptorManager;
@property (nonatomic, assign) UPPushAbility ability;
@property (nonatomic, strong) UPPushBadgeManager *badgeManager;

@end

@implementation UPPush
@synthesize provider, channel, dataSource, broadcastSender, settings;
#pragma mark - UPPushProtocol
- (instancetype)initWithInitializer:(id<UPPushInitializerProtocol>)initializer
{
    if (self = [super init]) {
        if (![self checkInitializer:initializer]) {
            return nil;
        }
        _ability = UPPushAbility_None;
        _pushInitializer = initializer;
        _pushInitializer.channel.delegate = self;
        _pushInitializer.channel.dataSource = self;
        _interceptorManager = [[UPPushInterceptorManager alloc] init];
        _dispatcher = [[UPPushDispatcher alloc] initWithParser:[UPPushParser new] context:self];
        _badgeManager = [[UPPushBadgeManager alloc] initWithDelegate:self];
    }
    return self;
}

- (BOOL)registerInterceptor:(nonnull id<UPPushInterceptorProtocol>)interceptor
{
    if (!interceptor) {
        return NO;
    }

    return [_interceptorManager registerInterceptor:interceptor];
}

- (BOOL)unregisterInterceptor:(nonnull id<UPPushInterceptorProtocol>)interceptor
{
    if (!interceptor) {
        return NO;
    }

    return [_interceptorManager unregisterInterceptor:interceptor];
}

- (void)registerPush:(UPPushAbility)ability callback:(void (^)(BOOL))callback
{
    _ability = ability;
    NSString *language = UPPush_isEmptyString(self.settings.language) ? @"" : self.settings.language;
    [self.dataSource configAPILanguage:language];
    [_pushInitializer.channel launch];
    [UPPushRetryAssistant retry:^(void (^retryCallback)(BOOL)) {
      [self.dataSource registerPush:self.pushToken
                            ability:self.ability | self.interceptorManager.abilities
                             result:^(id<UPPushResultProtocol> result) {
                               retryCallback(result.isSuccess);
                               callback ? callback(result.isSuccess) : nil;
                             }];
    }
                          count:5
                       interval:4
                           step:0];
}

- (void)registerPush:(void (^)(BOOL))callback
{
    [self registerPush:UPPushAbility_None callback:callback];
}

- (void)unregisterPush:(nonnull void (^)(BOOL))callBack
{
    [self.dataSource unregisterPush:^(id<UPPushResultProtocol> result) {
      callBack ? callBack(result.isSuccess) : nil;
    }];
}

- (void)report:(nonnull NSString *)messageID status:(UPPushMessageStatus)status callBack:(nonnull void (^)(BOOL))callBack
{
    if (UPPush_isEmptyString(messageID)) {
        callBack ? callBack(NO) : nil;
        return;
    }
    [self.dataSource reportMessage:messageID
                            result:^(id<UPPushResultProtocol> result) {
                              callBack ? callBack(result.isSuccess) : nil;
                            }];
}

- (void)syncBadge:(NSInteger)badge
{
    [_badgeManager configAppBadge:badge];
}

- (NSString *)pushID
{
    return self.pushToken;
}
#pragma mark - UPPushContextProtocol
- (NSString *)pushToken
{
    if (![_pushInitializer.channel respondsToSelector:@selector(platformPushToken)]) {
        return @"";
    }
    return _pushInitializer.channel.platformPushToken;
}

- (id<UPPushProviderProtocol>)provider
{
    return _pushInitializer.provider;
}

- (id<UPPushChannelProtocol>)channel
{
    return _pushInitializer.channel;
}

- (id<UPPushDataSourceFacadeProtocol>)dataSource
{
    return _pushInitializer.dataSource;
}

- (id<UPPushBroadcastSenderProtocol>)broadcastSender
{
    return _pushInitializer.broadcastSender;
}

- (id<UPPushSettingsProtocol>)settings
{
    return _pushInitializer.settings;
}

- (id<UPPushInterceptorManagerProtocol>)interceptors
{
    return _interceptorManager;
}

#pragma mark - UPPushChannelDelegate
- (void)recieveMessage:(id<UPPushOriginalMessageProtocol>)originalMessage
{
    if (!originalMessage || !originalMessage.originalMessage) {
        return;
    }
    [_dispatcher dispatch:originalMessage];
}

- (void)pushTokenRefresh
{
    [self registerPush:_ability | _interceptorManager.abilities
              callback:^(BOOL result){
              }];
}
#pragma mark - UPPushChannelDataSource
- (nullable NSData *)pushTokenOfAPNs
{
    if (![_pushInitializer.provider respondsToSelector:@selector(providePushToken)]) {
        return nil;
    }
    return _pushInitializer.provider.providePushToken;
}

- (NSDictionary *)launchingOption
{
    if (![_pushInitializer.provider respondsToSelector:@selector(launchingOption)]) {
        return @{};
    }
    return _pushInitializer.provider.launchingOption;
}

#pragma mark - UPPushBadgeDelegate
- (void)syncBadgeNumber:(NSInteger)badgeNumber
{
    if (![self.channel respondsToSelector:@selector(syncBadgeNumber:)]) {
        return;
    }
    [self.channel syncBadgeNumber:badgeNumber];
}

- (NSInteger)badgeNumber
{
    if (![self.channel respondsToSelector:@selector(badgeNumber)]) {
        return 0;
    }
    return [self.channel badgeNumber];
}
#pragma mark - private methods
- (BOOL)checkInitializer:(id<UPPushInitializerProtocol>)initializer
{
    return initializer.provider && initializer.broadcastSender && initializer.dataSource && initializer.settings;
}
@end
