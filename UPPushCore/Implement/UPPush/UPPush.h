//
//  UPPush.h
//  UPPush
//
//  Created by osiris on 2020/10/15.
//

#import <Foundation/Foundation.h>
#import "UPPushProtocol.h"
#import "UPPushInitializerProtocol.h"


NS_ASSUME_NONNULL_BEGIN
/**
 推送模块
 */
@interface UPPush : NSObject <UPPushProtocol, UPPushChannelDelegate, UPPushChannelDataSource>

@property (nonatomic, strong, readonly) id<UPPushInitializerProtocol> pushInitializer;

@end

NS_ASSUME_NONNULL_END
