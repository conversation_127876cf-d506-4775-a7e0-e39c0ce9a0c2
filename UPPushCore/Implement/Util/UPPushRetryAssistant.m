//
//  UPPushRetryAssistant.m
//  UPPushCore
//
//  Created by osiris on 2020/11/3.
//

#import "UPPushRetryAssistant.h"

@interface UPPushRetryAssistant ()


@end

@implementation UPPushRetryAssistant

+ (void)retry:(void (^)(void (^)(BOOL)))task count:(NSInteger)count interval:(NSTimeInterval)interval step:(NSTimeInterval)step
{
    if (count == 0) {
        return;
    }
    __block NSInteger blockCount = count;
    task(^(BOOL result) {
      if (!result) {
          dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(interval * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [UPPushRetryAssistant retry:task count:--blockCount interval:interval + step step:step];
          });
      }
    });
}


@end
