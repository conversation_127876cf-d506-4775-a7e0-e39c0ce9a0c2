//
//  UPPushBadgeManager.h
//  UPPush
//
//  Created by osiris on 2019/1/14.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 角标管理器代理
*/
@protocol UPPushBadgeDelegate <NSObject>
/**
 同步不同平台badge
 @param badgeNumber App角标
*/
- (void)syncBadgeNumber:(NSInteger)badgeNumber;

/**
 不同平台badge
*/
- (NSInteger)badgeNumber;

@end

@interface UPPushBadgeManager : NSObject
/**
 初始化角标管理器
 @param delegate 代理
 */
- (instancetype)initWithDelegate:(id<UPPushBadgeDelegate>)delegate;
/**
 *  设置App badge
 *
 *  @param badge 当前来源badge
 */
- (void)configAppBadge:(NSInteger)badge;
/**
 * 各源badge数量
 */
- (NSInteger)badgeNumber;

@end

NS_ASSUME_NONNULL_END
