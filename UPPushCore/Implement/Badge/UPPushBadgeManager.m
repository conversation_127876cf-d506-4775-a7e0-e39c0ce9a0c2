//
//  UPPushBadgeManager.m
//  UPPush
//
//  Created by osiris on 2019/1/14.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPPushBadgeManager.h"

static NSString *const kUPPushBadgeCount = @"kUPPushBadgeCount";

@interface UPPushBadgeManager ()

@property (nonatomic, weak) id<UPPushBadgeDelegate> delegate;
@property (nonatomic, assign) NSInteger currentBadge;

@end
@implementation UPPushBadgeManager

- (instancetype)initWithDelegate:(id<UPPushBadgeDelegate>)delegate
{
    if (self = [super init]) {
        _delegate = delegate;
        [self configAppBadge:0];
    }

    return self;
}

#pragma mark - public methods
static dispatch_semaphore_t badge_sigin;
static dispatch_once_t badge_onceToken;
- (void)configAppBadge:(NSInteger)badge
{
    dispatch_once(&badge_onceToken, ^{
      badge_sigin = dispatch_semaphore_create(1);
    });
    dispatch_semaphore_wait(badge_sigin, DISPATCH_TIME_FOREVER);
    NSInteger newBadge = MAX(badge, 0);
    [[UIApplication sharedApplication] setApplicationIconBadgeNumber:newBadge];
    [self.delegate syncBadgeNumber:newBadge];
    self.currentBadge = newBadge;
    dispatch_semaphore_signal(badge_sigin);
}

- (NSInteger)badgeNumber
{
    dispatch_semaphore_wait(badge_sigin, DISPATCH_TIME_FOREVER);
    NSInteger count = self.currentBadge;
    dispatch_semaphore_signal(badge_sigin);

    return count;
}
@end
