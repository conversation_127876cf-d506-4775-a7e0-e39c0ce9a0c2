//
//  UPPushParser.m
//  UPPush
//
//  Created by osiris on 2020/10/15.
//

#import "UPPushParser.h"
#import <MJExtension/MJExtension.h>
#import <UPLog/UPLog.h>
#import "UPPushMessage.h"

NSString *const kUPPushMessageContent = @"content";
NSString *const kUPPushMessageAPS = @"aps";
NSString *const kUPPushMessageAPSAlert = @"alert";
NSString *const kUPPushMessageAPSTitle = @"title";
extern NSString *const kUPPushMessageIsLocal;

@interface UPPushParser ()

@end
@implementation UPPushParser

- (id<UPPushMessageProtocol>)parse:(id<UPPushOriginalMessageProtocol>)originalMessage
{
    UPLogDebug(@"UPPush", @"<UPPush Debugging> 原始推送结构:%@", [originalMessage.originalMessage mj_JSONString]);
    NSDictionary *pretreatMessage = [originalMessage.originalMessage copy];
    NSDictionary *decodeContent = nil;
    if (!originalMessage.isLocal) {
        decodeContent = [self decodeMessageContent:originalMessage.originalMessage];
        pretreatMessage = [self restructureMessageContent:decodeContent originalMessage:originalMessage.originalMessage];
    }
    UPPushMessage *message = [UPPushMessage mj_objectWithKeyValues:pretreatMessage];
    message.isLocal = originalMessage.isLocal;
    message.isCustom = originalMessage.isCustom;
    message.decodeContent = decodeContent;
    return message;
}

#pragma mark - private method
- (NSDictionary *)decodeMessageContent:(NSDictionary *)message
{
    if (message[kUPPushMessageContent] == nil) {
        return nil;
    }
    NSData *data = [[NSData alloc] initWithBase64EncodedString:message[kUPPushMessageContent] options:NSDataBase64DecodingIgnoreUnknownCharacters];
    if (data == nil) {
        return nil;
    }

    NSError *error;
    NSDictionary *info = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableLeaves error:&error];
    if (error) {
        return nil;
    }

    return info;
}

- (NSDictionary *)restructureMessageContent:(NSDictionary *)decodeMessage originalMessage:(NSDictionary *)message
{
    if (!decodeMessage) {
        return nil;
    }
    NSMutableDictionary *allInfo = [NSMutableDictionary dictionaryWithDictionary:decodeMessage];
    id aps = message[kUPPushMessageAPS];
    if (aps && aps[kUPPushMessageAPSAlert]) {
        if ([aps[kUPPushMessageAPSAlert] isKindOfClass:NSDictionary.class]) {
            [allInfo addEntriesFromDictionary:message[kUPPushMessageAPS]];
        }
        else {
            [allInfo addEntriesFromDictionary:@{
                kUPPushMessageAPSAlert : @{
                    kUPPushMessageAPSTitle : aps[kUPPushMessageAPSAlert]
                }
            }];
        }
    }

    return [allInfo copy];
}


@end
