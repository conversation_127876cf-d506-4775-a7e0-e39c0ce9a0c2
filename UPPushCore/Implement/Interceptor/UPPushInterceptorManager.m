//
//  UPPushInterceptorManager.m
//  UPPushCore
//
//  Created by osiris on 2020/11/2.
//

#import "UPPushInterceptorManager.h"


@interface UPPushInterceptorManager ()

@property (nonatomic, strong) NSMutableArray<id<UPPushInterceptorProtocol>> *externalInterceptorList;
@property (nonatomic, strong) NSMutableArray<id<UPPushInterceptorProtocol>> *buildInInterceptorList;

@end

@implementation UPPushInterceptorManager

- (instancetype)init
{
    if (self = [super init]) {
        _externalInterceptorList = [NSMutableArray array];
        _buildInInterceptorList = [NSMutableArray array];
    }

    return self;
}

#pragma mark - UPPushInterceptorManagerProtocol

- (NSArray<id<UPPushInterceptorProtocol>> *)externalInterceptors
{
    return [_externalInterceptorList copy];
}

- (NSArray<id<UPPushBuildInInterceptorProtocol>> *)buildInInterceptors
{
    return [_buildInInterceptorList copy];
}

- (UPPushAbility)abilities
{
    UPPushAbility ability = UPPushAbility_None;
    NSMutableArray<id<UPPushInterceptorProtocol>> *interceptors = [NSMutableArray arrayWithArray:self.externalInterceptors];
    [interceptors addObjectsFromArray:self.buildInInterceptors];
    for (id<UPPushInterceptorProtocol> item in interceptors) {
        if ([item respondsToSelector:@selector(supportAbility)]) {
            ability |= item.supportAbility;
        }
    }
    return ability;
}

- (BOOL)registerInterceptor:(nonnull id<UPPushInterceptorProtocol>)interceptor
{
    if ([interceptor conformsToProtocol:@protocol(UPPushBuildInInterceptorProtocol)]) {
        if ([_buildInInterceptorList containsObject:interceptor]) {
            return YES;
        }

        [_buildInInterceptorList addObject:interceptor];
        return YES;
    }

    if ([interceptor conformsToProtocol:@protocol(UPPushInterceptorProtocol)] &&
        ![interceptor conformsToProtocol:@protocol(UPPushBuildInInterceptorProtocol)]) {
        if ([_externalInterceptorList containsObject:interceptor]) {
            return YES;
        }

        [_externalInterceptorList addObject:interceptor];
        return YES;
    }

    return NO;
}

- (BOOL)unregisterInterceptor:(nonnull id<UPPushInterceptorProtocol>)interceptor
{
    if ([interceptor conformsToProtocol:@protocol(UPPushBuildInInterceptorProtocol)]) {
        if (![_buildInInterceptorList containsObject:interceptor]) {
            return YES;
        }

        [_buildInInterceptorList removeObject:interceptor];
        return YES;
    }

    if ([interceptor conformsToProtocol:@protocol(UPPushInterceptorProtocol)] &&
        ![interceptor conformsToProtocol:@protocol(UPPushBuildInInterceptorProtocol)]) {
        if (![_externalInterceptorList containsObject:interceptor]) {
            return YES;
        }

        [_externalInterceptorList removeObject:interceptor];
        return YES;
    }

    return NO;
}

@end
