//
//  UPPushResultProtocol.h
//  UPPushCore
//
//  Created by osiris on 2020/10/31.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/**
 推送模块返回结果协议
 */
@protocol UPPushResultProtocol <NSObject>
/**
 * 是否成功
 */
@property (assign, nonatomic) BOOL isSuccess;

/**
 * 返回码
 */
@property (copy, nonatomic) NSString *code;

/**
 * 返回信息
 */
@property (copy, nonatomic) NSString *info;

/**
 * 回调数据
 */
@property (strong, nonatomic) id userInfo;

///  UPPushGetIconRequestTool 使用
@property (nonatomic, strong, nullable) NSDictionary *payload;

@end
NS_ASSUME_NONNULL_END
