//
//  UPPushContextProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/16.
//

#import "UPPushInitializerProtocol.h"
#import "UPPushInterceptorManagerProtocol.h"

NS_ASSUME_NONNULL_BEGIN
/**
 推送消息模块上下文协议
 */
@protocol UPPushContextProtocol <UPPushInitializerProtocol>
/**
 推送拦截器列表
 */
@property (nonatomic, strong, readonly) id<UPPushInterceptorManagerProtocol> interceptors;
/**
 推送Token
 */
@property (nonatomic, strong, readonly) NSString *pushToken;


@end
NS_ASSUME_NONNULL_END
