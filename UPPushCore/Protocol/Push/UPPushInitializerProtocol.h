//
//  UPPushInitializerProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/15.
//

#import "UPPushChannelProtocol.h"
#import "UPPushProviderProtocol.h"
#import "UPPushDataSourceProtocol.h"
#import "UPPushBroadcastSenderProtocol.h"
#import "UPPushSettingsProtocol.h"

NS_ASSUME_NONNULL_BEGIN
/**
 推送模块初始化配置协议
 */
@protocol UPPushInitializerProtocol <NSObject>
/**
 推送模块所需数据提供者
 */
@property (nonatomic, strong) id<UPPushProviderProtocol> provider;
/**
 推送通道
 */
@property (nonatomic, strong) id<UPPushChannelProtocol> channel;
/**
 推送模块数据源
 */
@property (nonatomic, strong) id<UPPushDataSourceFacadeProtocol> dataSource;
/**
 推送模块广播发送者
 */
@property (nonatomic, strong) id<UPPushBroadcastSenderProtocol> broadcastSender;
/**
 推送模块配置项
 */
@property (nonatomic, strong) id<UPPushSettingsProtocol> settings;
@end
NS_ASSUME_NONNULL_END
