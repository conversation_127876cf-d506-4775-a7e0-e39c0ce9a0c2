//
//  UPPushManagerProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/15.
//
#import <Foundation/Foundation.h>
#import "UPPushInterceptorProtocol.h"
#import "UPPushInitializerProtocol.h"
#import "UPPushMessageProtocol.h"

NS_ASSUME_NONNULL_BEGIN
/**
 推送消息模块协议
 */
@protocol UPPushProtocol <NSObject>
/**
 初始化推送消息模块
 @param initializer 初始化配置
 @return 推送消息模块对象
 */
- (instancetype)initWithInitializer:(id<UPPushInitializerProtocol>)initializer;
/**
 注册推送功能
 @param ability app提供处理推送能力
 @warning 需在登录后调用
 */
- (void)registerPush:(UPPushAbility)ability callback:(void (^)(BOOL))callback;
/**
 注册推送功能
 @warning 需在登录后调用
 */
- (void)registerPush:(void (^)(BOOL))callback;
/**
 注销推送功能
 @param callBack 注销回调
 */
- (void)unregisterPush:(void (^)(BOOL))callBack;
/**
 注册推送消息拦截器
 @param interceptor 拦截器
 @return 注册拦截器结果
 */
- (BOOL)registerInterceptor:(id<UPPushInterceptorProtocol>)interceptor;
/**
 注销推送消息拦截器
 @param interceptor 拦截器
 @return 注销拦截器结果
 */
- (BOOL)unregisterInterceptor:(id<UPPushInterceptorProtocol>)interceptor;
/**
 上报消息状态
 @param messageID 消息ID
 @param status 消息状态
 */
- (void)report:(NSString *)messageID status:(UPPushMessageStatus)status callBack:(nonnull void (^)(BOOL))callBack;
/**
 同步App badge
 @param badge  App角标
*/
- (void)syncBadge:(NSInteger)badge;
/**
 推送ID
*/
- (NSString *)pushID;
@end
NS_ASSUME_NONNULL_END
