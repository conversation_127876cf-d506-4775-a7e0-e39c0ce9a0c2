//
//  UPPushAPIs.h
//  UPPushAPIs
//
//  Created by osiris on 2020/10/20.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSInteger, UPPushEnvironment) {
    UPPushEnvironment_Development,
    UPPushEnvironment_Acceptance,
    UPPushEnvironment_Production
};

typedef NS_OPTIONS(NSUInteger, UPPushAbility) {
    UPPushAbility_None = 0,
    UPPushAbility_Action = 1 << 0, // 通用action定义
    UPPushAbility_Notification = 1 << 1, // 通知栏
    UPPushAbility_Dialog = 1 << 2, // 弹框
    UPPushAbility_Voice = 1 << 3, // 播放音频
    UPPushAbility_Toast = 1 << 4, // toast浮层
    UPPushAbility_DownloadFile = 1 << 5, // 下载文件
    UPPushAbility_DeliverData = 1 << 6, //透传数据
    UPPushAbility_OpenApp = 1 << 7, // 打开App
    UPPushAbility_OpenPage = 1 << 8, // 跳转页面
    UPPushAbility_OpenLink = 1 << 9, // 跳转链接
    UPPushAbility_CallApi = 1 << 10, // 调用函数
    UPPushAbility_ControlDevice = 1 << 11, // 控制设备
    UPPushAbility_ActivateApp = 1 << 12, // 激活App
    UPPushAbility_MakeVideoCall = 1 << 13, // 视频通话
    UPPushAbility_ControlScene = 1 << 14, // 控制场景
};
