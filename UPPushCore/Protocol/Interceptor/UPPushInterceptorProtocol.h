//
//  UPPushInterceptor.h
//  UPPush
//
//  Created by osiris on 2020/10/15.
//

#import <Foundation/Foundation.h>
#import "UPPushMessageProtocol.h"
#import "UPPushGlobalDeclaration.h"

NS_ASSUME_NONNULL_BEGIN
/**
 推送拦截器协议
 */
@protocol UPPushInterceptorProtocol <NSObject>
/**
 拦截器是否为激活状态
 */
- (BOOL)isFire;
/**
 校验推送消息是否为拦截器所拦截
 @param pushMessage 推送消息
 */
- (BOOL)check:(id<UPPushMessageProtocol>)pushMessage;
/**
 拦截推送消息
 @param pushMessage 推送消息
 */
- (void)intercept:(id<UPPushMessageProtocol>)pushMessage;
/**
 支持的能力
 */
- (UPPushAbility)supportAbility;
@end

/**
 内建推送拦截器代理
 */
@protocol UPPushBuildInInterceptorDelegate <NSObject>
@required
/**
 打点
 */
- (void)track:(NSString *)eventID param:(NSDictionary *)param;
/**
 修改密码
 */
- (void)logoutForChangePassword:(id<UPPushMessageProtocol>)message;

@optional

/**
 自定义直接跳转类型推送
 */
- (void)handleDirectJumpMessage:(id<UPPushMessageProtocol>)message;
/**
 是否需要自定义直接跳转类型推送；若返回YES，需要在handleDirectJumpMessage: 中自行完成跳转工作
 */
- (BOOL)customHandleDirectJumpMessage:(id<UPPushMessageProtocol>)message;

/**
 打点
 @param eventName 事件标识，注意：eventName可能不是标准的事件id，因此不能直接传给GIO或Firebase进行埋点，需要在协议方法实现了进行具体的判断
 @param messageInfo 消息体
 @param extraInfo 其它附加参数
 */
- (void)trackEvent:(NSString *)eventName messageInfo:(NSDictionary *)messageInfo extraInfo:(NSDictionary *)extraInfo;

@end
/**
 内建推送拦截器事件观察者协议
 */
@protocol UPPushBuildInInterceptorObserverProtocol <NSObject>
/**
 内建推送拦截器即将处理消息
 @warning 此方法在自建异步队列执行
 */
- (void)willProcessMessage:(id<UPPushMessageProtocol>)message;
/**
 内建推送拦截器处理消息结束
 @warning 此方法在自建异步队列执行
 */
- (void)didProcessMessage:(id<UPPushMessageProtocol>)message;

@end
/**
 内建推送拦截器事件观察者协议
 */
@protocol UPPushBuildInInterceptorSubjectProtocol <NSObject>
/**
 注册内建推送拦截器监听者
 */
- (void)setObserver:(id<UPPushBuildInInterceptorObserverProtocol>)observer;

@end
/**
 内建推送拦截器协议
 */
@protocol UPPushBuildInInterceptorProtocol <UPPushInterceptorProtocol, UPPushBuildInInterceptorSubjectProtocol>
/**
 内建推送拦截器代理
 */
@property (nonatomic, weak) id<UPPushBuildInInterceptorDelegate> delegate;

@end
NS_ASSUME_NONNULL_END
