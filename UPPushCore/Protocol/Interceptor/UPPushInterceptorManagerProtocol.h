//
//  UPPushInterceptorManagerProtocol.h
//  UPPush
//
//  Created by osiris on 2020/11/9.
//

#import <Foundation/Foundation.h>
#import "UPPushInterceptorProtocol.h"

NS_ASSUME_NONNULL_BEGIN
/**
 内建推送拦截器协议
 */
@protocol UPPushInterceptorManagerProtocol <NSObject>
/**
 外部拦截器集合
 */
@property (nonatomic, strong, readonly) NSArray<id<UPPushInterceptorProtocol>> *externalInterceptors;
/**
 内建拦截器集合
 */
@property (nonatomic, strong, readonly) NSArray<id<UPPushBuildInInterceptorProtocol>> *buildInInterceptors;
/**
 提供能力集合
 */
@property (nonatomic, assign, readonly) UPPushAbility abilities;
/**
 注册推送消息拦截器
 @param interceptor 拦截器
 @return 注册拦截器结果
 */
- (BOOL)registerInterceptor:(id<UPPushInterceptorProtocol>)interceptor;
/**
 注销推送消息拦截器
 @param interceptor 拦截器
 @return 注销拦截器结果
 */
- (BOOL)unregisterInterceptor:(id<UPPushInterceptorProtocol>)interceptor;
@end
NS_ASSUME_NONNULL_END
