//
//  UPPushDataSourceProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/27.
//

#import "UPPushResultProtocol.h"
#import "UPPushGlobalDeclaration.h"

NS_ASSUME_NONNULL_BEGIN
/**
 推送组件回调Block

 @param result (id<UPPushResultProtocol>)
 @see UPPushResultProtocol
 */
typedef void (^UPPushResultCallback)(id<UPPushResultProtocol> result);

/**
 推送模块数据源协议
 */
@protocol UPPushDataSourceProtocol <NSObject>
/** 向服务器注册推送功能
@param pushToken 推送token
@param clientType client类型。固定为2，在iot 服务端的定义为 mobileApp
@param ability 推送能力
@param deviceAlias 设备别名
@param version 消息版本
@param channel 消息通道
 */
- (void)registerPush:(NSString *)pushToken
          clientType:(NSString *)clientType
             ability:(NSArray<NSString *> *)ability
         deviceAlias:(NSString *)deviceAlias
             version:(NSString *)version
             channel:(NSString *)channel
              result:(UPPushResultCallback)result;
/**
 向服务器注销推送功能
 */
- (void)unregisterPush:(UPPushResultCallback)result;
/**
 向服务器上报消息状态
 */
- (void)reportMessage:(NSString *)messageID
               status:(NSString *)status
               result:(UPPushResultCallback)result;

/**
 配置API 开发环境
 */
- (void)configAPIEnvironment:(UPPushEnvironment)environment;

/**
 配置API语言参数
 */
- (void)configAPILanguage:(NSString *)language;
@end

/**
 推送模块数据源装饰协议
 */
@protocol UPPushDataSourceFacadeProtocol <UPPushDataSourceProtocol>
/** 向服务器注册推送功能
@param pushToken 推送token
 */
- (void)registerPush:(NSString *)pushToken
             ability:(UPPushAbility)ability
              result:(UPPushResultCallback)result;

/**
 向服务器上报消息状态
 */
- (void)reportMessage:(NSString *)messageID
               result:(UPPushResultCallback)result;
@end

NS_ASSUME_NONNULL_END
