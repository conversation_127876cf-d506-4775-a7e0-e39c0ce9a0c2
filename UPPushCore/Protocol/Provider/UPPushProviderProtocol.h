//
//  UPPushProviderProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/22.
//

#import <Foundation/Foundation.h>
#import "UPPushChannelProtocol.h"
#import "UPPushGlobalDeclaration.h"

NS_ASSUME_NONNULL_BEGIN
/**
 推送模块所需数据提供者协议
 */
@protocol UPPushProviderProtocol <NSObject>
/**
 提供者提供推送Token
 @return 推送Token
 */
- (NSData *)providePushToken;
/**
 提供者提供推送信道类型
 @return 推送信道类型
 */
- (UPPushChannelType)provideChannelType;
/**
 App启动参数
 @return 启动参数
 */
- (NSDictionary *)launchingOption;
/**
 UPPush 开发环境
 */
- (UPPushEnvironment)environment;
@end

NS_ASSUME_NONNULL_END
