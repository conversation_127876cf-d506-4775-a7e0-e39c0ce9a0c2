//
//  UPPushDispatcherProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/26.
//

#import <Foundation/Foundation.h>
#import "UPPushMessageProtocol.h"
#import "UPPushParserProtocol.h"
#import "UPPushContextProtocol.h"

NS_ASSUME_NONNULL_BEGIN
/**
 不同类型推送消息分发器协议
 */
@protocol UPPushDispatcherProtocol <NSObject>

/**
 分发器初始化
 @param parser 解析器
 */
- (instancetype)initWithParser:(id<UPPushParserProtocol>)parser context:(id<UPPushContextProtocol>)context;
/**
 分发消息
 @param originalMessage 原始消息
 */
- (void)dispatch:(id<UPPushOriginalMessageProtocol>)originalMessage;

@end

NS_ASSUME_NONNULL_END
