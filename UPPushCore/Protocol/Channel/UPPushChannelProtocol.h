//
//  UPPushChannelProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/15.
//
#import <Foundation/Foundation.h>
#import "UPPushMessageProtocol.h"

/**
 @enum 推送通道类型
 */
typedef NS_ENUM(NSUInteger, UPPushChannelType) {
    /**
     极光通道
     */
    UPPushChannel_Home,
    /**
     Firebase通道
     */
    UPPushChannel_Seasia,
};

NS_ASSUME_NONNULL_BEGIN
/**
 推送通道协议代理
 */
@protocol UPPushChannelDelegate <NSObject>
/**
 接收推送消息
 @param originalMessage 原始推送信息体
 */
- (void)recieveMessage:(id<UPPushOriginalMessageProtocol>)originalMessage;
/**
 通道pushToken 刷新
 */
- (void)pushTokenRefresh;
@end
/**
 推送通道协议数据源
 */
@protocol UPPushChannelDataSource <NSObject>
/**
 获取APNs token
 @return APNs token
 */
- (nullable NSData *)pushTokenOfAPNs;
/**
 App启动参数
 @return 启动参数
 */
- (NSDictionary *)launchingOption;
@end

/**
 推送通道协议
 */
@protocol UPPushChannelProtocol <NSObject>
/**
 推送token
 */
@property (nonatomic, strong, readonly) NSString *platformPushToken;
/**
 推送通道代理
 */
@property (nonatomic, weak) id<UPPushChannelDelegate> delegate;
/**
 推送通道数据源
 */
@property (nonatomic, weak) id<UPPushChannelDataSource> dataSource;
/**
 启动推送通道
 */
- (void)launch;
/**
 获取远程推送通知
 */
- (void)didRecieveRemoteNotification:(NSDictionary *)notification;
/**
 获取本地通知
 */
- (void)didRecieveLocalNotification:(NSDictionary *)notification;
@optional
/**
 同步不同平台badge
 @param badgeNumber App角标
*/
- (void)syncBadgeNumber:(NSInteger)badgeNumber;
/**
 不同平台badge
*/
- (NSInteger)badgeNumber;
@end

NS_ASSUME_NONNULL_END
