//
//  UPPushMessageUIButtonProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/26.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/**
 消息UI按钮信息协议
 */
@protocol UPPushMessageUIButtonProtocol <NSObject, NSCopying>
/**
 按钮展示的文本内容
 */
@property (nonatomic, copy, readonly) NSString *text;
/**
 按钮事件调用ID
 @discussion 用来表示此按钮单击事件
 */
@property (nonatomic, assign, readonly) NSInteger callID;
@end
NS_ASSUME_NONNULL_END
