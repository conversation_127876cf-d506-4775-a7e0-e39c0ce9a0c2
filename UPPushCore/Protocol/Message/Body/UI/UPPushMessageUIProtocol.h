//
//  UPPushMessageUIProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/26.
//

#import <Foundation/Foundation.h>
#import "UPPushMessageUIButtonProtocol.h"

/**
 消息UI表现类型
 */
typedef NS_ENUM(NSUInteger, UPPushMessageUIType) {
    MessageUI_Jump,
    MessageUI_Toast,
    MessageUI_Alert,
    MessageUI_ExclamationMark,
    MessageUI_Notification,
    MessageUI_TapToast,
    MessageUI_Unknown
};

NS_ASSUME_NONNULL_BEGIN
/**
 消息UI信息协议
 */
@protocol UPPushMessageUIProtocol <NSObject, NSCopying>
/**
 消息UI表现类型（归纳后）
 */
@property (nonatomic, assign, readonly) UPPushMessageUIType showTypeAppearance;
/**
 消息UI表现原始类型
 */
@property (nonatomic, assign, readonly) NSInteger showType;
/**
 消息UI标题
 */
@property (nonatomic, copy, readonly) NSString *title;
/**
 消息UI内容
 */
@property (nonatomic, copy, readonly) NSString *content;
/**
 消息UI按钮列表
 */
@property (nonatomic, copy, readonly) NSArray<id<UPPushMessageUIButtonProtocol>> *buttons;

@end
NS_ASSUME_NONNULL_END
