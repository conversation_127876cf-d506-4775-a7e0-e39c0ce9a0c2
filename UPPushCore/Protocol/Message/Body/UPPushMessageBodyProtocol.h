//
//  UPPushMessageBodyProtocol.h
//  UPPushCore
//
//  Created by osiris on 2020/11/2.
//

#import <Foundation/Foundation.h>
#import "UPPushMessageUIProtocol.h"
#import "UPPushMessageExtraDataProtocol.h"

NS_ASSUME_NONNULL_BEGIN
/**
 消息体模型协议
 */
@protocol UPPushMessageBodyProtocol <NSObject, NSCopying>
/**
 消息额外信息
 */
@property (nonatomic, strong, readonly) NSDictionary *extraParam;

/**
 消息UI数据
 */
@property (nonatomic, strong, readonly) id<UPPushMessageUIProtocol> messageUI;
/**
 消息业务数据
 */
@property (nonatomic, strong, readonly) id<UPPushMessageExtraDataProtocol> extraData;
@end
NS_ASSUME_NONNULL_END
