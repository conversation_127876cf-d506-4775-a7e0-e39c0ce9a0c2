//
//  UPPushMessageDeviceProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/26.
//

#import <Foundation/Foundation.h>
NS_ASSUME_NONNULL_BEGIN
/**
 设备类消息信息协议
 */
@protocol UPPushMessageDeviceProtocol <NSObject, NSCopying>
/**
 设备typeID
 */
@property (nonatomic, assign, readonly) NSInteger typeID;
/**
 设备mac地址
 */
@property (nonatomic, copy, readonly) NSString *deviceID;
/**
 设备名称
 */
@property (nonatomic, strong, readonly) NSString *deviceName;

@end
NS_ASSUME_NONNULL_END
