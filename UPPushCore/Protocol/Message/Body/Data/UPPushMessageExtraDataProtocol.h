//
//  UPPushMessageExtraDataProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/26.
//

#import <Foundation/Foundation.h>
#import "UPPushMessageDeviceProtocol.h"
#import "UPPushMessageAPIProtocol.h"
#import "UPPushMessagePageProtocol.h"
#import "UPPushMessageDeviceControlProtocol.h"

NS_ASSUME_NONNULL_BEGIN
/**
 消息额外数据协议
 */
@protocol UPPushMessageExtraDataProtocol <NSObject, NSCopying>
/**
 业务消息在本地系统的有效时间
 @discussion 若该值超出时间范围则在APP端视为无效消息，不进行业务处理
 */
@property (nonatomic, assign, readonly) NSInteger expireTime;
/**
 是否存储在APP消息中心
 @discussion 可为空。0：不存储；1：存储
 */
@property (nonatomic, assign, readonly) NSInteger isSaveInMessageCenter;
/**
 Device对象集合
 @discussion 设备信息，若推送与设备无关，可为空
 */
@property (nonatomic, strong, readonly) NSArray<id<UPPushMessageDeviceProtocol>> *device;
/**
 执行API调用处理类消息
 */
@property (nonatomic, strong, readonly) id<UPPushMessageAPIProtocol> api;
/**
 跳转目标页面
 */
@property (nonatomic, copy, readonly) NSString *targetPage;
/**
 跳转多个页面信息
 */
@property (nonatomic, strong, readonly) NSArray<id<UPPushMessagePageProtocol>> *pages;
/**
 跳转单一页面信息
 */
@property (nonatomic, strong, readonly) id<UPPushMessagePageProtocol> page;
/**
 执行多个设备控制类消息
 */
@property (nonatomic, strong, readonly) NSArray<id<UPPushMessageDeviceControlProtocol>> *deviceControlList;
/**
 执行单个设备控制类消息
 */
@property (nonatomic, strong, readonly) id<UPPushMessageDeviceControlProtocol> deviceControl;
/**
 最后登录终端的手机clientID
 */
@property (nonatomic, strong, readonly) NSString *clientID;
/**
 兼容适配V4转V3模型的数据传输，例如AI端发送小优异步消息业务
 */
@property (nonatomic, strong, readonly) NSString *deliverData;

@end
NS_ASSUME_NONNULL_END
