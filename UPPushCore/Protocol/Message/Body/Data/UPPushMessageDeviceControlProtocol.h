//
//  UPPushMessageDeviceControlProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/26.
//

#import <Foundation/Foundation.h>
NS_ASSUME_NONNULL_BEGIN
/**
 设备类消息信息协议
 */
@protocol UPPushMessageDeviceControlProtocol <NSObject, NSCopying>
/**
 调用ID
 @discussion 若为0，则代表自动调用
 */
@property (nonatomic, assign, readonly) NSInteger callID;
/**
 设备mac地址
 */
@property (nonatomic, copy, readonly) NSString *deviceID;
/**
 组命令名称
 */
@property (nonatomic, copy, readonly) NSString *groupName;
/**
 标准模型的命令键值对集合
 */
@property (nonatomic, strong, readonly) NSDictionary<NSString *, NSString *> *commandList;

@end
NS_ASSUME_NONNULL_END
