//
//  UPPushMessagePageProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/26.
//

#import <Foundation/Foundation.h>
NS_ASSUME_NONNULL_BEGIN
/**
 调用API类消息信息协议
 */
@protocol UPPushMessagePageProtocol <NSObject, NSCopying>
/**
 调用ID
 @discussion 若为0，则代表自动调用
 */
@property (nonatomic, assign, readonly) NSInteger callID;
/**
 页面唯一地址
 @discussion 如是native页面，则需在VDN的DNS表中页面保持一致
 */
@property (nonatomic, copy, readonly) NSString *url;
/**
 参数键值对集合
 @discussion 页面跳转参数集合
 */
@property (nonatomic, strong, readonly) NSDictionary *params;

@end
NS_ASSUME_NONNULL_END
