//
//  UPPushMessageAPIProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/26.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/**
 调用API类消息信息协议
 */
@protocol UPPushMessageAPIProtocol <NSObject, NSCopying>
/**
 调用ID
 @discussion 若为0，则代表自动调用
 */
@property (nonatomic, assign, readonly) NSInteger callID;
/**
 api定义
 @discussion 如DELEATE_FAMILY
 */
@property (nonatomic, copy, readonly) NSString *apiType;
/**
 参数键值对集合
 @discussion API调用参数集合
 */
@property (nonatomic, strong, readonly) NSDictionary *params;
@end
NS_ASSUME_NONNULL_END
