//
//  UPPushMessageProtocol.h
//  UPPush
//
//  Created by osiris on 2020/10/15.
//

#import <Foundation/Foundation.h>
#import "UPPushMessageAPNsInfoProtocol.h"
#import "UPPushMessageCustomPropertyProtocol.h"
#import "UPPushMessageBodyProtocol.h"
/**
 @enum 推送消息状态
 */
typedef NS_ENUM(NSUInteger, UPPushMessageStatus) {
    /**
     消息已读
     */
    Message_Status_read = 0
};

/**
 @enum 接收到消息时，App状态
 */
typedef NS_ENUM(NSInteger, UPPushReceivePushState) {
    ReceivePush_State_Active,
    ReceivePush_State_Inactive,
    ReceivePush_State_None
};

NS_ASSUME_NONNULL_BEGIN

@protocol UPPushOriginalMessageProtocol <UPPushMessageCustomPropertyProtocol>

@property (nonatomic, strong) NSDictionary *originalMessage;

@end
/**
 推送消息模型协议
 */
@protocol UPPushMessageProtocol <UPPushMessageAPNsInfoProtocol, UPPushMessageCustomPropertyProtocol, NSCopying>
/**
 消息唯一标示，服务端产生
 */
@property (nonatomic, copy, readonly) NSString *messageID;
/**
 业务类型 0 系统消息，1设备消息
 */
@property (nonatomic, assign, readonly) NSInteger businessType;
/**
 消息类型
 */
@property (nonatomic, assign, readonly) NSInteger messageType;
/**
 业务消息名称
 @discussion 英文字符串，常量定义方式，如:FAMILY_DATA_CHANGE
 */
@property (nonatomic, copy, readonly) NSString *messageName;
/**
 业务消息描述
 @discussion 描述该消息对应的业务消息中文描述，用于直观理解。如上示例可写未为：家庭数据变更消息
 */
@property (nonatomic, copy, readonly) NSString *messageDescription;
/**
 时间戳
 */
@property (nonatomic, assign, readonly) NSInteger timestamp;
/**
 是否为长消息
 */
@property (nonatomic, assign, readonly) BOOL isLongMessage;
/**
 消息体
 */
@property (nonatomic, strong, readonly) id<UPPushMessageBodyProtocol> messageBody;
/**
 消息接收App状态
 */
@property (nonatomic, assign) UPPushReceivePushState receivePushState;
/**
 消息在客户端离线时在第三方推送平台缓存时间，
 过期将不再推送给客户端。单位为秒，最长86400秒，如未指定则默认为86400秒
 */
@property (nonatomic, assign, readonly) NSInteger expires;
/**
 存储在历史消息中的消息过期时间，过期后在App消息中心将无法查询。单位为天。
 取值如下：
 -1：系统默认设置（1年后消息将被自动清除）；
 0：立即过期；
 大于0：过期时间
 */
@property (nonatomic, assign, readonly) NSInteger messageHistoryExpires;
/**
  解密后推送消息原始数据
 */
@property (nonatomic, strong, nullable) NSDictionary *decodeContent;

@end
NS_ASSUME_NONNULL_END
