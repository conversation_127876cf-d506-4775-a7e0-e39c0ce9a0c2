//
//  UPPushParser.h
//  UPPush
//
//  Created by osiris on 2020/10/15.
//

#import <Foundation/Foundation.h>
#import "UPPushMessageProtocol.h"

NS_ASSUME_NONNULL_BEGIN
/**
 推送消息解析器协议
 */
@protocol UPPushParserProtocol <NSObject>
/**
 推送消息解析
 @brief 将收到的远程推送消息内容解析为推送消息Model
 @param originalMessage 原始推送消息内容体
 @return 推送消息Model
 */
- (nullable id<UPPushMessageProtocol>)parse:(id<UPPushOriginalMessageProtocol>)originalMessage;

@end
NS_ASSUME_NONNULL_END
